GIT
  remote: ******************:ta-rails/rails_action_core.git
  revision: 6c57d8576a923298bda3ad56cf81cc332390d9fb
  specs:
    rails_action_core (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_bpm.git
  revision: 52e4dc95740e48248ab97cd7a98e26fa074a6ec6
  specs:
    rails_bpm (0.1.0)
      jbuilder
      rack-cors
      rails
      tallty_import_export

GIT
  remote: ******************:ta-rails/rails_com.git
  revision: a23dee9fd82c393b3f73f6285053a110c7faac3a
  specs:
    rails_com (0.1.0)
      aasm
      active_record_extended
      acts_as_list
      ancestry
      attr_json
      aws-sdk-s3
      closure_tree
      deep_cloneable
      execjs
      groupdate
      jbuilder
      matrix
      mime-types
      nilify_blanks
      ohm
      paper_trail
      paranoia
      pundit
      rack-cors
      rails
      ransack
      ransack-enum
      redis
      redis-namespace
      redis-objects
      responders
      rolify
      ruby-pinyin
      strip_attributes
      ta_by_star
      ta_deep_pluck
      ta_default_value_for
      ta_has_event
      typhoeus
      uppy-s3_multipart
      uuidtools
      zip_tricks

GIT
  remote: ******************:ta-rails/rails_data.git
  revision: 27b5fbe0299f518c763542cbfb19c4b92f376384
  specs:
    rails_data (0.1.0)
      jbuilder
      rack-cors
      rails
      tallty_import_export

GIT
  remote: ******************:ta-rails/rails_favor.git
  revision: 464dd93b9342742723bc35a3435a60727dd1c28e
  specs:
    rails_favor (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_notify.git
  revision: aa03bce9764f1bd4ccb4d6e9d7b01c6d0e39323e
  specs:
    rails_notify (0.1.0)
      acts_as_commentable_with_threading
      jbuilder
      rack-cors
      rails
      rest-client

GIT
  remote: ******************:ta-rails/rails_pay.git
  revision: 292e2cd45a6ecd4cc72be5f6caf35bb7e7ebc48b
  specs:
    rails_pay (0.1.0)
      alipay
      jbuilder
      rack-cors
      rails
      wx_pay

GIT
  remote: ******************:ta-rails/rails_permit.git
  revision: 6c7215d8a90b892da72e0c9c6f9c668758bed39f
  specs:
    rails_permit (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_res.git
  revision: 534552eff0853466a73ef2df6ed1d5c6941b1695
  specs:
    rails_res (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_soa_auth.git
  revision: b4fcbd2ad1db9096a38a13296c58cde83ee3615c
  specs:
    rails_soa_auth (1.0.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_state.git
  revision: d4c380a9db301cd3c8193b6aa161fac61e688d0a
  specs:
    rails_state (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_tofu.git
  revision: 4e430b69972d054e1b344fe4ce68ae75f6c16cf5
  specs:
    rails_tofu (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_weixin.git
  revision: b496792dc44facbd7dbfc0544eb41b67c73ef1a7
  specs:
    rails_weixin (0.1.0)
      jbuilder
      rack-cors
      rails (~> 7.1.0)
      wechat

GIT
  remote: http://git.tallty.com/open-source/rspec-rails-swagger.git
  revision: c1b2bb18d1fc3358c0640355750230b1bbc20476
  specs:
    rspec-rails-swagger (0.1.4)
      rails (>= 3.1)
      rspec-rails

GIT
  remote: https://git.tallty.com/open-source/simple_controller.git
  revision: e8c693162ebbc29d82db5389b680d0f7fe18ecb0
  specs:
    simple_controller (1.1.0)
      inherited_resources
      pundit
      ransack
      responders
      ta_ransack_mongo
      will_paginate

PATH
  remote: .
  specs:
    rails_travel (0.1.0)
      jbuilder
      neighbor
      rack-cors
      rails (>= 7.1.3.2)

GEM
  remote: https://gems.ruby-china.com/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record_extended (3.3.0)
      activerecord (>= 5.2, < 8.0.0)
      pg (< 3.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    acts_as_commentable_with_threading (2.0.1)
      activerecord (>= 4.0)
      activesupport (>= 4.0)
      awesome_nested_set (>= 3.0)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    alipay (0.17.0)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    attr_json (2.4.0)
      activerecord (>= 6.0.0, < 7.3)
    awesome_nested_set (3.6.0)
      activerecord (>= 4.0.0, < 7.2)
    awesome_print (1.9.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1109.0)
    aws-sdk-core (3.224.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.101.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.188.0)
      aws-sdk-core (~> 3, >= 3.224.1)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.8)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    builder (3.3.0)
    byebug (11.1.3)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    closure_tree (7.4.0)
      activerecord (>= 4.2.10)
      with_advisory_lock (>= 4.0.0)
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    content_disposition (1.0.0)
    crass (1.0.6)
    date (3.3.4)
    debug_inspector (1.2.0)
    deep_cloneable (3.2.1)
      activerecord (>= 3.1.0, < 9)
    diff-lcs (1.5.1)
    domain_name (0.6.20240107)
    dotenv (3.1.2)
    dotenv-rails (3.1.2)
      dotenv (= 3.1.2)
      railties (>= 6.1)
    drb (2.2.1)
    erubi (1.13.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    execjs (2.10.0)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    ffi (1.17.0)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    globalid (1.2.1)
      activesupport (>= 6.1)
    groupdate (6.6.0)
      activesupport (>= 7.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hirb (0.7.3)
    hiredis (0.6.3)
    htmlentities (4.3.4)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-accept (1.7.0)
    http-cookie (1.0.6)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.12.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    llhttp-ffi (0.5.1)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.7.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0806)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.24.1)
    mutex_m (0.2.0)
    neighbor (0.4.3)
      activerecord (>= 6.1)
    nest (3.2.0)
      redic
    net-imap (0.4.14)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    nilify_blanks (1.4.0)
      activerecord (>= 4.0.0)
      activesupport (>= 4.0.0)
    nio4r (2.7.3)
    nokogiri (1.16.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    ohm (3.1.1)
      nest (~> 3)
      redic (~> 1.5.0)
      stal
    ostruct (0.6.1)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    pg (1.5.7)
    pluck_all (2.3.4)
      activesupport (>= 3.0.0)
      rails_compatibility (>= 0.0.10)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-doc (1.5.0)
      pry (~> 0.11)
      yard (~> 0.9.11)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.1.2)
      stringio
    public_suffix (6.0.1)
    puma (6.4.2)
      nio4r (~> 2.0)
    pundit (2.3.2)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (3.1.7)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails_compatibility (0.0.10)
      activerecord (>= 3)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    ransack (3.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    ransack-enum (1.0.0)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redic (1.5.3)
      hiredis
    redis (5.2.0)
      redis-client (>= 0.22.0)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-objects (1.7.0)
      redis
    reline (0.5.9)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.1)
    rmmseg-cpp-new (0.3.1)
    roda (3.92.0)
      rack
    rolify (6.0.1)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rouge (4.3.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.3)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    ruby-ole (********)
    ruby-pinyin (0.5.0)
      rmmseg-cpp-new (~> 0.3.1)
    rubyzip (2.3.2)
    sassc (2.4.0)
      ffi (~> 1.9)
    shoulda-matchers (6.3.0)
      activesupport (>= 5.2.0)
    spreadsheet (1.3.1)
      bigdecimal
      ruby-ole
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (2.0.3)
      mini_portile2 (~> 2.8.0)
    stal (0.3.0)
      redic (~> 1.5)
    stringio (3.1.1)
    strip_attributes (2.0.0)
      activemodel (>= 3.0, < 9.0)
    ta_by_star (4.1.0)
      activesupport (>= 3.2.0)
    ta_deep_pluck (1.3.0)
      activerecord (>= 3)
      pluck_all (>= 2.3.2)
      rails_compatibility (>= 0.0.4)
    ta_default_value_for (3.4.0)
      activerecord (>= 3.2.0, < 8.0)
    ta_has_event (1.0.1)
      activerecord (>= 4.0)
      verbs
    ta_ransack_mongo (1.0.3)
    tallty_duck_record (1.1.3)
      activemodel (>= 5.0)
      activesupport (>= 5.0)
    tallty_form (1.0.0)
      tallty_duck_record
    tallty_import_export (1.1.5)
      activesupport
      attr_json
      caxlsx
      redis
      redis-objects
      roo
      roo-xls
      tallty_form
      zip-zip
    thor (1.3.1)
    timeout (0.4.1)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uppy-s3_multipart (1.2.1)
      aws-sdk-s3 (~> 1.0)
      content_disposition (~> 1.0)
      roda (>= 2.27, < 4)
    uuidtools (3.0.0)
    verbs (3.1.0)
      activesupport (>= 2.3.4)
      i18n
    webrick (1.8.1)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wechat (0.17.7)
      activesupport (>= 6.0, < 9)
      http (>= 1.0.4, < 6)
      nokogiri (>= 1.6.0)
      ostruct
      rexml
      thor
      zeitwerk (~> 2.4)
    will_paginate (4.0.1)
    with_advisory_lock (5.3.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    wx_pay (0.21.0)
      activesupport (>= 3.2)
      rest-client (>= 2.0.0)
    yard (0.9.36)
    zeitwerk (2.6.17)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)
    zip_tricks (5.6.0)

PLATFORMS
  ruby

DEPENDENCIES
  annotate
  awesome_print
  better_errors
  binding_of_caller
  dotenv-rails
  factory_bot_rails
  hirb
  pg
  pry-byebug
  pry-doc
  pry-rails
  puma
  rails_action_core!
  rails_bpm!
  rails_com!
  rails_data!
  rails_favor!
  rails_notify!
  rails_pay!
  rails_permit!
  rails_res!
  rails_soa_auth!
  rails_state!
  rails_tofu!
  rails_travel!
  rails_weixin!
  rspec-rails
  rspec-rails-swagger!
  sassc
  shoulda-matchers
  simple_controller!
  sprockets-rails
  sqlite3

BUNDLED WITH
   2.5.6
