class RailsComMigration1750153773 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_notice_relate_actions do |t|
      t.references :notice
      t.references :notice_grp
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :travel_notice_grps do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
  end
end
