class RailsComMigration1715821831 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_extras do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
  end
end
