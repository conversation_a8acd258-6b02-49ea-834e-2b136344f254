class RailsComMigration1745659362 < ActiveRecord::Migration[7.1]

  def change
    add_reference :travel_products, :app_country
    add_column :travel_poi_app_countries, :state, :string, comment: "状态"
    add_column :travel_poi_app_countries, :content, :jsonb, comment: "内容"
    add_column :travel_poi_app_countries, :icon, :string, comment: "icon"
    add_column :travel_poi_app_countries, :cover_image, :jsonb, comment: "封面图"
    add_column :travel_poi_app_countries, :score, :float, comment: "评分"
    add_column :travel_poi_app_countries, :used_count, :integer, comment: "旅行人数"
    add_column :travel_poi_app_countries, :views_count, :integer, comment: "浏览人数"
    add_column :travel_poi_app_countries, :stars_count, :integer, comment: "收藏人数"
    add_column :travel_poi_app_countries, :payload, :jsonb, comment: "其他字段"
  end
end
