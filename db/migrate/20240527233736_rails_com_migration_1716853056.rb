class RailsComMigration1716853056 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_poi_app_countries do |t|
      t.references :app
      t.references :country
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :color, comment: "颜色"
      t.integer :position, comment: "位置"
      t.jsonb :conf, comment: "其他配置"
      t.timestamps
    end
    create_table :travel_poi_countries do |t|
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :continent, comment: "大洲"
      t.jsonb :detail, comment: "详情"
      t.timestamps
    end
  end
end
