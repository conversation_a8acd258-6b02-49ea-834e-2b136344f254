class RailsComMigration1726641625 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_activity_temp_dirs do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :desc, comment: "描述"
      t.timestamps
    end
  end
end
