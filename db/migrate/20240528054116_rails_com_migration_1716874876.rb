class RailsComMigration1716874876 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_relation_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "travel_relation_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "travel_relation_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "travel_relation_actions_uk_action_target_user"
    end
    add_reference :travel_ota_skus, :channel
    create_table :travel_channels do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.jsonb :config, comment: "配置"
      t.jsonb :display_config, comment: "展示配置"
      t.timestamps
    end
    add_reference :travel_orders, :channel
  end
end
