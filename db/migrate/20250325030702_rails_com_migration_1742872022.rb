class RailsComMigration1742872022 < ActiveRecord::Migration[7.1]

  def change
    create_table :wechat_keywords do |t|
      t.string :key, comment: "关键词"
      t.string :value, comment: "返回文案"
      t.string :pattern, comment: "匹配模式"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.timestamps
      t.index :key
    end
    create_table :wechat_sessions do |t|
      t.string :openid, comment: "openid"
      t.string :hash_store, comment: "hash_store"
      t.timestamps
      t.index :openid, unique: true
    end
    create_table :wechat_configs do |t|
      t.string :environment
      t.string :account, comment: "account name"
      t.boolean :enabled
      t.string :appid
      t.string :secret
      t.string :type
      t.string :corpid
      t.string :corpsecret
      t.integer :agentid
      t.boolean :encrypt_mode
      t.string :encoding_aes_key
      t.string :token
      t.string :access_token
      t.string :jsapi_ticket
      t.boolean :skip_verify_ssl
      t.integer :timeout
      t.string :trusted_domain_fullname
      t.timestamps
      t.index [:environment, :account], unique: true
    end
    create_table :travel_demand_presets do |t|
      t.references :demand_grp_preset
      t.references :app
      t.references :poi_region
      t.references :demand_define
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :desc, comment: "描述"
      t.string :mode, comment: "分类"
      t.integer :count, comment: "数量"
      t.integer :duration, comment: "天数"
      t.integer :start_offset, comment: "开始日期偏移量"
      t.integer :end_offset, comment: "结束日期偏移量"
      t.integer :wait_for_move_to_start_offset, comment: "记录移动天数时的暂存字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.decimal :amount, comment: "报价金额"
      t.string :amount_unit, comment: "报价金额单位"
      t.decimal :amount_ratio, comment: "报价金额对人民币汇率"
      t.decimal :amount_rmb, comment: "报价金额兑换为人民币价格"
      t.decimal :offer_amount, comment: "成本金额"
      t.string :offer_amount_unit, comment: "成本金额单位"
      t.decimal :offer_amount_ratio, comment: "成本金额对人民币汇率"
      t.decimal :offer_amount_rmb, comment: "成本金额兑换为人民币价格"
      t.decimal :offer_total_amount, comment: "成本金额 * 数量"
      t.string :offer_total_amount_unit, comment: "成本金额 * 数量单位"
      t.decimal :offer_total_amount_ratio, comment: "成本金额 * 数量对人民币汇率"
      t.decimal :offer_total_amount_rmb, comment: "成本金额 * 数量兑换为人民币价格"
      t.timestamps
    end
    create_table :travel_demand_grp_presets do |t|
      t.references :app
      t.string :name, comment: "名称"
      t.text :desc, comment: "描述"
      t.string :state, comment: "状态"
      t.jsonb :form, comment: "表单"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    add_reference :travel_demand_temps, :demand_preset
    add_reference :travel_demands, :demand_preset
    create_table :soa_auth_auth_accounts do |t|
      t.references :user
      t.string :app_id, comment: "对应的app code"
      t.string :account, comment: "对应的app code"
      t.string :password, comment: "加密后的密码"
      t.string :account_type, comment: "用户类型"
      t.string :encrypt_mode, comment: "加密类型"
      t.string :account_mode, comment: "账号类型"
      t.timestamps
    end
    create_table :weixin_subscribes do |t|
      t.string :oauth_app_id, comment: "对应的wechat client account名称"
      t.string :unionid, comment: "unionid"
      t.string :openid, comment: "openid"
      t.timestamps
    end
    create_table :weixin_mesages do |t|
      t.references :app
      t.references :chat
      t.references :user
      t.string :type, comment: "STI属性"
      t.string :state
      t.string :msg_type, comment: "消息类型"
      t.text :content, comment: "消息内容"
      t.jsonb :body, comment: "消息内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    create_table :weixin_chats do |t|
      t.references :app
      t.references :owner
      t.references :source, polymorphic: true
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :appid, comment: "企业/微信id"
      t.string :state, comment: "状态"
      t.string :chatid, comment: "群聊id"
      t.jsonb :payload, comment: "其他数据"
      t.timestamps
    end
    create_table :soa_auth_oauths do |t|
      t.references :user
      t.references :auth_account
      t.string :type, comment: "STI类型，例如Oauth::Wechat"
      t.string :app_id, comment: "对应的app code，解决一个第三方应用绑定多个内部应用"
      t.string :oauth_app_id, comment: "对应的应用，可以是app_id，也可以是配置的app_account"
      t.string :openid, comment: "oauth_id，在对应鉴权系统中的id"
      t.string :unionid, comment: "unionid，在跨平台中使用"
      t.jsonb :options, comment: "oauth平台额外的属性"
      t.timestamps
    end
  end
end
