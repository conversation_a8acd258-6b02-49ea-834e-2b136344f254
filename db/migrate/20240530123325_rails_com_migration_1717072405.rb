class RailsComMigration1717072405 < ActiveRecord::Migration[7.1]

  def change
    add_reference :travel_demand_temps, :demand_define
    add_column :travel_demand_temps, :payload, :jsonb, comment: "payload payload存储的字段"
    add_column :travel_demand_temps, :payload_summary, :jsonb, comment: "payload summary存储的字段"
    create_table :travel_demand_defines do |t|
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :cover_image, comment: "图标"
      t.jsonb :form, comment: "表单"
      t.timestamps
    end
    add_reference :travel_demands, :demand_define
    add_column :travel_demands, :payload, :jsonb, comment: "payload payload存储的字段"
    add_column :travel_demands, :payload_summary, :jsonb, comment: "payload summary存储的字段"
  end
end
