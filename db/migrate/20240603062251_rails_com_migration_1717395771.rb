class RailsComMigration1717395771 < ActiveRecord::Migration[7.1]

  def change
    create_table :state_bpm_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "state_bpm_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "state_bpm_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "state_bpm_actions_uk_action_target_user"
    end
    create_table :state_activate_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "state_activate_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "state_activate_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "state_activate_actions_uk_action_target_user"
    end
    create_table :state_transitions do |t|
      t.references :machine
      t.references :source
      t.references :target
      t.references :terminate_place
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :type, comment: "STI"
      t.string :seq, comment: "transition的唯一序列号，保持一致"
      t.string :name, comment: "名称"
      t.string :event_name, comment: "操作的英文名称"
      t.string :flag, comment: "程序使用的标记位"
      t.boolean :auto_trigger, comment: "是否自动触发"
      t.jsonb :options, comment: "状态转换的具体配置信息，根据STI的类型不同而不同"
      t.jsonb :trigger_options, comment: "transition的触发器处理"
      t.timestamps
    end
    create_table :state_tokens do |t|
      t.references :app
      t.references :machine
      t.references :event
      t.references :transition
      t.references :token_define
      t.references :token_source, polymorphic: true
      t.references :eventable, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.string :type, comment: "STI"
      t.string :name, comment: "处理节点名称"
      t.string :flag, comment: "处理节点flag"
      t.string :user_name, comment: "user的名称"
      t.string :state
      t.jsonb :token_source_attributes, comment: "token source的attributes缓存"
      t.timestamps
    end
    create_table :state_token_defines do |t|
      t.references :machine
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "token_define的唯一序列号，保持一致"
      t.string :name, comment: "名称"
      t.string :type, comment: "STI"
      t.string :token_type, comment: "对应token的type"
      t.string :token_flag, comment: "对应token的flag"
      t.string :token_default_state, comment: "token生成的默认state"
      t.jsonb :token_form, comment: "token表单"
      t.jsonb :options, comment: "配置信息"
      t.jsonb :limit_options, comment: "限制要求配置信息，包括schedule循环、次数要求等"
      t.jsonb :user_options, comment: "限制可以操作的用户类型设置"
      t.timestamps
    end
    create_table :state_places do |t|
      t.references :machine
      t.string :seq, comment: "place的唯一序列号，保持一致"
      t.string :name, comment: "节点名称"
      t.string :state, comment: "节点状态"
      t.string :type, comment: "STI"
      t.integer :position, comment: "排序"
      t.jsonb :options, comment: "配置信息"
      t.jsonb :trigger_options, comment: "place的触发器处理"
      t.timestamps
    end
    create_table :state_machines do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "状态机名称"
      t.string :state_attr_name, comment: "状态机对应模型属性名称"
      t.string :klass, comment: "类名"
      t.string :klass_singular, comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
      t.string :flag, comment: "程序使用标识"
      t.timestamps
    end
    create_table :state_events do |t|
      t.references :app
      t.references :machine
      t.references :transition
      t.references :user, polymorphic: true
      t.references :source
      t.references :target
      t.references :eventable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.timestamps
    end
  end
end
