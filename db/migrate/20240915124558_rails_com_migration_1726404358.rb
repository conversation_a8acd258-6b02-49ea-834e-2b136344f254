class RailsComMigration1726404358 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_revenues do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.references :data, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.decimal :amount, comment: "金额"
      t.timestamps
    end
  end
end
