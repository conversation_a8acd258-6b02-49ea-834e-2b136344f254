class RailsComMigration1717660401 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_product_days do |t|
      t.references :product
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    add_column :travel_products, :duration, :integer, comment: "天数"
    remove_column :travel_products, :days, :float, comment: "天数"
  end
end
