class RailsComMigration1744861696 < ActiveRecord::Migration[7.1]

  def change
    create_table :xhs_messages do |t|
      t.references :app
      t.references :user
      t.references :chat
      t.references :channel
      t.string :type, comment: "STI属性"
      t.string :seq, comment: "编号"
      t.string :name, comment: "名称"
      t.string :sender
      t.string :state
      t.string :message_type, comment: "消息类型"
      t.text :content, comment: "内容"
      t.string :token, comment: "token"
      t.string :senderid, comment: "消息发送人ID"
      t.string :receiverid, comment: "接受者ID"
      t.datetime :operate_at, comment: "操作时间"
      t.integer :position, comment: "位置"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :xhs_chats do |t|
      t.references :app
      t.references :channel
      t.references :user
      t.datetime :replied_at
      t.boolean :is_replied
      t.string :name, comment: "名称"
      t.integer :position, comment: "位置"
      t.string :state
      t.datetime :operate_at, comment: "操作时间"
      t.string :agentid, comment: "客户ID"
      t.string :nickname, comment: "用户昵称"
      t.string :avatar, comment: "用户头像"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :xhs_channels do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :code, comment: "标识"
      t.string :agentid, comment: "客服ID"
      t.jsonb :config, comment: "配置"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
  end
end
