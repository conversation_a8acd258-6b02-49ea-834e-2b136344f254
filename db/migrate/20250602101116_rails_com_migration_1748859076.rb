class RailsComMigration1748859076 < ActiveRecord::Migration[7.1]

  def change
    # remove_column :travel_activity_temps, :name_embedding, :
    add_reference :travel_products, :destination
    add_column :travel_products, :comments_count, :integer, comment: "评论数量"
    add_column :travel_products, :comment_conf, :string
    add_column :travel_products, :stars_count, :integer, comment: "收藏数量"
    add_column :travel_products, :price, :float, comment: "商品价格"
    add_column :travel_products, :origin_price, :float, comment: "原始价格"
    add_column :travel_products, :position, :integer, comment: "商品排序"
    add_column :travel_products, :sale_count, :integer, comment: "销售数量"
    add_column :travel_products, :score, :float, comment: "评分"
    add_column :travel_products, :origin_score, :float, comment: "原始评分"
    # remove_column :travel_products, :name_embedding, :
  end
end
