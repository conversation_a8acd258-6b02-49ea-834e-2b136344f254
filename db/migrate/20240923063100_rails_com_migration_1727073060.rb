class RailsComMigration1727073060 < ActiveRecord::Migration[7.1]

  def change
    add_column :travel_demand_temps, :offer_amount, :decimal, comment: "成本金额"
    add_column :travel_demand_temps, :offer_amount_unit, :string, comment: "成本金额单位"
    add_column :travel_demand_temps, :offer_amount_ratio, :decimal, comment: "成本金额对人民币汇率"
    add_column :travel_demand_temps, :offer_amount_rmb, :decimal, comment: "成本金额兑换为人民币价格"
    add_column :travel_demands, :offer_amount, :decimal, comment: "成本金额"
    add_column :travel_demands, :offer_amount_unit, :string, comment: "成本金额单位"
    add_column :travel_demands, :offer_amount_ratio, :decimal, comment: "成本金额对人民币汇率"
    add_column :travel_demands, :offer_amount_rmb, :decimal, comment: "成本金额兑换为人民币价格"
  end
end
