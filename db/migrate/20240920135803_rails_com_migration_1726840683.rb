class RailsComMigration1726840683 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_ota_products do |t|
      t.references :app
      t.references :channel
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :seq, comment: "编号"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :ota_no, comment: "ota编号"
      t.datetime :list_time, comment: "上架时间"
      t.datetime :delist_time, comment: "下架时间"
      t.integer :num, comment: "数量"
      t.integer :sold_quantity, comment: "销售数量"
      t.decimal :price, comment: "价格"
      t.jsonb :payload, comment: "额外数据"
      t.timestamps
    end
    add_reference :travel_ota_skus, :ota_product
    add_column :travel_ota_skus, :uid, :string, comment: "套餐"
  end
end
