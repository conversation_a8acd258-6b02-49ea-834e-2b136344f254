class RailsComMigration1726809088 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_receipts do |t|
      t.references :app
      t.references :payment
      t.references :creator
      t.string :seq, comment: "编号"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.decimal :amount, comment: "金额"
      t.string :amount_unit, comment: "金额单位"
      t.decimal :amount_ratio, comment: "金额对人民币汇率"
      t.decimal :amount_rmb, comment: "金额兑换为人民币价格"
      t.text :content, comment: "内容"
      t.jsonb :attachments, comment: "附件"
      t.timestamps
    end
  end
end
