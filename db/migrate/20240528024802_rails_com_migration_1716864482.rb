class RailsComMigration1716864482 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_poi_extras do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    add_column :travel_demand_temps, :mode, :string, comment: "分类"
    add_column :travel_demands, :mode, :string, comment: "分类"
    remove_column :travel_demand_grps, :mode, :string
    remove_column :travel_demand_grp_temps, :mode, :string
    create_table :travel_poi_region_hierarchies do |t|
      t.integer :ancestor_id
      t.integer :descendant_id
      t.integer :generations
      t.datetime :created_at
      t.datetime :updated_at
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "travel/poi/region_anc_desc_idxx"
    end
    create_table :travel_poi_regions do |t|
      t.references :app
      t.references :creator
      t.integer :parent_id, comment: "closure tree parent_id"
      t.string :type, comment: "STI属性"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :detail, comment: "详情"
      t.integer :position, comment: "排序"
      t.timestamps
    end
  end
end
