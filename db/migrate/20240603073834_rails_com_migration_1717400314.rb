class RailsComMigration1717400314 < ActiveRecord::Migration[7.1]

  def change
    add_reference :bpm_tokens, :place
    add_reference :bpm_tokens, :previous_token
    add_reference :bpm_tokens, :operator
    add_reference :bpm_tokens, :app
    add_reference :bpm_tokens, :workflow
    add_reference :bpm_tokens, :instance
    add_reference :bpm_tokens, :activate_source, polymorphic: true
    add_reference :bpm_tokens, :token_source, polymorphic: true
    add_column :bpm_tokens, :type, :string, comment: "STI"
    add_column :bpm_tokens, :place_type, :string, comment: "Place的类型"
    add_column :bpm_tokens, :transition_type, :string, comment: "Transition的类型"
    add_column :bpm_tokens, :name, :string, comment: "Token的名称，默认取自Place"
    add_column :bpm_tokens, :state, :string, comment: "Token状态"
    add_column :bpm_tokens, :comment, :text, comment: "审批备注"
    add_column :bpm_tokens, :options, :jsonb, comment: "Token的额外信息JSON"
    add_column :bpm_tokens, :token_payload, :jsonb, comment: "对应place的place_payload，存储审批时候存储在tokne中的信息"
    add_column :bpm_tokens, :spent_time_in_second, :integer, comment: "耗时时长"
    add_column :bpm_tokens, :operate_logs, :jsonb, comment: "相关的操作日志"
    add_column :bpm_tokens, :action_key, :string, comment: "保存上一个action的操作"
    add_column :bpm_tokens, :action_flag, :string, comment: "保存action的操作flag，action key有可能是重复的，通过action_flag来做区分"
    add_column :bpm_tokens, :timestamp, :integer, comment: "时间戳，当前批次"
    add_column :bpm_tokens, :action_at, :datetime, comment: "激活时间"
    add_column :bpm_instances, :comments_count, :integer, comment: "评论数量"
    add_column :bpm_instances, :comment_conf, :string
    create_table :notify_wechat_template_messages do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :oauth_app_id, comment: "微信服务名称的标识"
      t.string :openid, comment: "微信发送的openid"
      t.jsonb :message, comment: "发送内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    create_table :notify_template_messages do |t|
      t.references :app
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :type, comment: "STI属性"
      t.string :state, comment: "状态"
      t.string :oauth_app_id, comment: "微信服务名称的标识"
      t.string :openid, comment: "微信发送的openid"
      t.jsonb :body, comment: "发送内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    create_table :notify_sms_messages do |t|
      t.references :notifyable, polymorphic: true
      t.string :mobile, comment: "发送手机号"
      t.string :title, comment: "发送标题"
      t.string :account, comment: "发送账号"
      t.string :content, comment: "发送内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    create_table :notify_like_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "notify_like_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "notify_like_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "notify_like_actions_uk_action_target_user"
    end
    create_table :comments do |t|
      t.references :commentable, polymorphic: true
      t.references :user
      t.string :title, comment: "标题"
      t.text :subject, comment: "简述"
      t.text :body, comment: "内容"
      t.jsonb :attachments, comment: "附件"
      t.integer :parent_id, comment: "父节点"
      t.integer :lft, comment: "左节点"
      t.integer :rgt, comment: "右节点"
      t.integer :depth, comment: "层级"
      t.integer :children_count, comment: "子评论数量"
      t.integer :position, comment: "排序"
      t.integer :likes_count, comment: "点赞次数"
      t.timestamps
    end
    create_table :notify_info_messages do |t|
      t.references :app
      t.references :user, polymorphic: true
      t.references :create_user
      t.references :notifyable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :flag, comment: "发送标识，保证唯一性"
      t.string :title, comment: "发送标题"
      t.string :content, comment: "发送内容"
      t.jsonb :meta, comment: "额外信息"
      t.string :url, comment: "链接地址"
      t.datetime :read_at
      t.boolean :is_read
      t.timestamps
    end
  end
end
