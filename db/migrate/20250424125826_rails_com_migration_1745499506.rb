class RailsComMigration1745499506 < ActiveRecord::Migration[7.1]

  def change
    create_table :pay_transfers do |t|
      t.references :app
      t.references :payment
      t.references :from_source, polymorphic: true
      t.references :merchant
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.string :name, comment: "金额转移说明"
      t.decimal :max_amount, comment: "金额上限"
      t.timestamps
    end
    create_table :pay_refunds do |t|
      t.references :app
      t.references :merchant
      t.references :channel
      t.references :payment
      t.references :user
      t.references :transfer
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.string :state
      t.string :name, comment: "退款说明"
      t.decimal :amount, comment: "金额"
      t.string :type, comment: "STI"
      t.datetime :pay_at, comment: "支付时间"
      t.jsonb :meta, comment: "订单处理需要的返回信息"
      t.timestamps
    end
    create_table :pay_payments do |t|
      t.references :app
      t.references :merchant
      t.references :channel
      t.references :source, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :state
      t.string :name, comment: "订单说明(subject)"
      t.string :body, comment: "订单详细说明"
      t.string :batch, comment: "订单批次概念"
      t.string :type, comment: "Payment STI"
      t.decimal :amount, comment: "金额"
      t.string :client_ip
      t.jsonb :payload, comment: "订单提交的额外信息"
      t.jsonb :meta, comment: "订单处理需要的返回信息"
      t.decimal :refund_amount, comment: "已退款金额"
      t.datetime :pay_at, comment: "支付时间"
      t.timestamps
    end
    create_table :pay_merchant_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "pay_merchant_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "pay_merchant_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "pay_merchant_actions_uk_action_target_user"
    end
    create_table :pay_merchants do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "商户名称"
      t.timestamps
    end
    create_table :pay_channels do |t|
      t.references :app
      t.references :merchant
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "渠道名称"
      t.string :type, comment: "渠道STI类型，包括 Pay::Channels::WxChannel Pay::Channels::WxChannelAlipayChannel"
      t.string :payment_type, comment: "支付渠道内的子类型 STI，例如公众号支付小程序支付等"
      t.jsonb :config, comment: "渠道配置参数"
      t.timestamps
    end
  end
end
