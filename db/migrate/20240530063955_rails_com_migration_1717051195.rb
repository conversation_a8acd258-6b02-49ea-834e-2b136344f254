class RailsComMigration1717051195 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_pay_accounts do |t|
      t.references :app
      t.references :supplier
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "账户名称"
      t.string :state, comment: "状态"
      t.string :account_type, comment: "账户类型"
      t.jsonb :payload, comment: "字段"
      t.timestamps
    end
  end
end
