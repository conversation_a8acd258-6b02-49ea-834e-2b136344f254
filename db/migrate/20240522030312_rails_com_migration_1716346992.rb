class RailsComMigration1716346992 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_day_temps do |t|
      t.references :activity_temp
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :desc, comment: "描述"
      t.integer :offset, comment: "偏移量"
      t.timestamps
    end
    create_table :travel_days do |t|
      t.references :activity
      t.references :day_temp
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :desc, comment: "描述"
      t.integer :offset, comment: "偏移量"
      t.timestamps
    end
    create_table :travel_activity_scenic_spot_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.integer :position, comment: "排序"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "travel_activity_scenic_spot_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "travel_activity_scenic_spot_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "travel_activity_scenic_spot_actions_uk_action_target_user"
    end
    create_table :travel_activity_city_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.integer :position, comment: "排序"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "travel_activity_city_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "travel_activity_city_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "travel_activity_city_actions_uk_action_target_user"
    end
  end
end
