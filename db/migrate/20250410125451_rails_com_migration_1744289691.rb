class RailsComMigration1744289691 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_skus do |t|
      t.references :app
      t.references :product
      t.references :creator
      t.string :type, comment: "STI属性"
      t.string :seq, comment: "编号"
      t.integer :comments_count, comment: "评论数量"
      t.string :comment_conf
      t.datetime :recommended_at
      t.boolean :is_recommended
      t.string :name, comment: "商品名称"
      t.string :state, comment: "商品状态"
      t.jsonb :content, comment: "商品内容"
      t.jsonb :cover_image, comment: "商品图片(轮播图)"
      t.float :price, comment: "商品价格"
      t.float :origin_price, comment: "原始价格"
      t.integer :position, comment: "商品排序"
      t.float :total_number, comment: "库存数量"
      t.float :freeze_number, comment: "冻结数量"
      t.integer :sale_count, comment: "销售数量"
      t.integer :stars_count, comment: "收藏数量"
      t.float :score, comment: "评分"
      t.float :origin_score, comment: "原始评分"
      t.integer :origin_sale_count, comment: "原始销售数量"
      t.jsonb :payload, comment: "扩展信息"
      t.timestamps
    end
    create_table :travel_evaluations do |t|
      t.references :app
      t.references :user
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "评分项名称"
      t.float :score, comment: "分值"
      t.jsonb :options, comment: "评论的内容结构"
      t.timestamps
    end
    add_column :travel_products, :recommended_at, :datetime
    add_column :travel_products, :is_recommended, :boolean
    add_reference :travel_destinations, :region
    add_column :travel_poi_regions, :recommended_at, :datetime
    add_column :travel_poi_regions, :is_recommended, :boolean
  end
end
