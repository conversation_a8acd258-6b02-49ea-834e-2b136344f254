class RailsComMigration1728526745 < ActiveRecord::Migration[7.1]

  def change
    add_column :travel_demand_temps, :offer_total_amount, :decimal, comment: "成本金额 * 数量"
    add_column :travel_demand_temps, :offer_total_amount_unit, :string, comment: "成本金额 * 数量单位"
    add_column :travel_demand_temps, :offer_total_amount_ratio, :decimal, comment: "成本金额 * 数量对人民币汇率"
    add_column :travel_demand_temps, :offer_total_amount_rmb, :decimal, comment: "成本金额 * 数量兑换为人民币价格"
    add_column :travel_demands, :offer_total_amount, :decimal, comment: "成本金额 * 数量"
    add_column :travel_demands, :offer_total_amount_unit, :string, comment: "成本金额 * 数量单位"
    add_column :travel_demands, :offer_total_amount_ratio, :decimal, comment: "成本金额 * 数量对人民币汇率"
    add_column :travel_demands, :offer_total_amount_rmb, :decimal, comment: "成本金额 * 数量兑换为人民币价格"
  end
end
