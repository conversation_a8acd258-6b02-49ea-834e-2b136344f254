class RailsComMigration1748857497 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_notices do |t|
      t.references :app
      t.references :user
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.text :content, comment: "内容"
      t.string :state, comment: "状态"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    add_column :travel_orders, :fee_amount, :decimal, comment: "手续费用"
    add_column :travel_orders, :refund_amount, :decimal, comment: "退款费用"
  end
end
