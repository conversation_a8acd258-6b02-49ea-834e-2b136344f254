class RailsComMigration1743933157 < ActiveRecord::Migration[7.1]

  def change
    create_table :travel_records do |t|
      t.references :app
      t.references :creator
      t.references :source, polymorphic: true
      t.string :type, comment: "STI属性"
      t.integer :stars_count, comment: "收藏人数"
      t.datetime :hotted_at
      t.boolean :is_hotted
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :desc, comment: "描述"
      t.string :icon, comment: "icon"
      t.string :content, comment: "内容"
      t.jsonb :cover_image, comment: "封面图"
      t.integer :position, comment: "排序"
      t.float :price, comment: "价格"
      t.float :score, comment: "评分"
      t.integer :used_count, comment: "使用人数"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :travel_destinations do |t|
      t.references :app
      t.references :creator
      t.string :type, comment: "STI属性"
      t.integer :stars_count, comment: "收藏人数"
      t.datetime :hotted_at
      t.boolean :is_hotted
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :desc, comment: "描述"
      t.text :content, comment: "内容"
      t.string :icon, comment: "icon"
      t.integer :position, comment: "排序"
      t.jsonb :cover_image, comment: "封面图"
      t.float :score, comment: "评分"
      t.float :price, comment: "价格"
      t.integer :used_count, comment: "旅行人数"
      t.integer :views_count, comment: "浏览人数"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :favor_mark_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "favor_mark_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "favor_mark_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "favor_mark_actions_uk_action_target_user"
    end
    create_table :favor_folders do |t|
      t.references :app
      t.references :user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "收藏夹名称"
      t.jsonb :cover_image, comment: "封面图"
      t.jsonb :content, comment: "详情"
      t.integer :position, comment: "排序"
      t.jsonb :option, comment: "额外配置信息"
      t.timestamps
    end
  end
end
