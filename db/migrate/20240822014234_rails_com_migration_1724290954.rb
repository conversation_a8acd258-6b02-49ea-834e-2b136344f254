class RailsComMigration1724290954 < ActiveRecord::Migration[7.1]

  def change
    remove_column :travel_demand_temps, :around_amount, :decimal, comment: "预估金额"
    remove_column :travel_demand_temps, :around_amount_unit, :string, comment: "预估金额单位"
    remove_column :travel_demand_temps, :around_amount_ratio, :decimal, comment: "预估金额对人民币汇率"
    remove_column :travel_demand_temps, :around_amount_rmb, :decimal, comment: "预估金额兑换为人民币价格"
    add_column :travel_demand_grp_temps, :amount, :decimal, comment: "报价金额"
    add_column :travel_demand_grp_temps, :amount_unit, :string, comment: "报价金额单位"
    add_column :travel_demand_grp_temps, :amount_ratio, :decimal, comment: "报价金额对人民币汇率"
    add_column :travel_demand_grp_temps, :amount_rmb, :decimal, comment: "报价金额兑换为人民币价格"

    add_column :travel_demand_grps, :amount_sync, :boolean, comment: "将报价金额切换为同步需求之和的值"

    add_column :travel_demand_grps, :amount, :decimal, comment: "报价金额"
    add_column :travel_demand_grps, :amount_unit, :string, comment: "报价金额单位"
    add_column :travel_demand_grps, :amount_ratio, :decimal, comment: "报价金额对人民币汇率"
    add_column :travel_demand_grps, :amount_rmb, :decimal, comment: "报价金额兑换为人民币价格"
    remove_column :travel_demands, :around_amount, :decimal, comment: "预估金额"
    remove_column :travel_demands, :around_amount_unit, :string, comment: "预估金额单位"
    remove_column :travel_demands, :around_amount_ratio, :decimal, comment: "预估金额对人民币汇率"
    remove_column :travel_demands, :around_amount_rmb, :decimal, comment: "预估金额兑换为人民币价格"
  end
end
