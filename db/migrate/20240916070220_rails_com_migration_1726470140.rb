class RailsComMigration1726470140 < ActiveRecord::Migration[7.1]

  def change
    add_column :travel_revenues, :amount_unit, :string, comment: "支付金额单位"
    add_column :travel_revenues, :amount_ratio, :decimal, comment: "支付金额对人民币汇率"
    add_column :travel_revenues, :amount_rmb, :decimal, comment: "支付金额兑换为人民币价格"
    add_column :travel_revenues, :mode, :string, comment: "模式，例如收款/补款等"
    add_column :travel_revenues, :remark, :string, comment: "备注信息"
    add_column :travel_orders, :order_amount, :decimal, comment: "金额"
    add_column :travel_orders, :plus_amount, :decimal, comment: "金额"
    add_column :travel_orders, :discount_amount, :decimal, comment: "金额"
    add_column :travel_orders, :income_amount, :decimal, comment: "金额"
    add_column :travel_orders, :remain_amount, :decimal, comment: "金额"
    add_column :travel_orders, :traveler_num, :integer, comment: "出行人数"
    add_column :travel_orders, :trip_start_date, :date, comment: "出行日期"
    add_column :travel_orders, :trip_end_date, :date, comment: "返程日期"
    add_column :travel_orders, :trip_days, :integer, comment: "出行天数"
    add_column :travel_orders, :item_num, :string, comment: "购买数量"
    add_column :travel_orders, :remark, :text, comment: "备注"
    add_column :travel_orders, :attachments, :jsonb, comment: "附件"
    add_column :travel_orders, :payload, :jsonb, comment: "信息存储"
    add_column :travel_orders, :is_invoiced, :boolean, comment: "是否开票"

    remove_column :travel_orders, :amount, :decimal, comment: "金额"
    remove_column :travel_orders, :total_amount, :decimal, comment: "总金额"
    remove_column :travel_orders, :replenish_amount, :decimal, comment: "已补款金额"
    remove_column :travel_orders, :refund_amount, :decimal, comment: "已退款金额"
  end
end
