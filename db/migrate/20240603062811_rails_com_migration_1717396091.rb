class RailsComMigration1717396091 < ActiveRecord::Migration[7.1]

  def change
    add_column :org_requests, :create_instance_timestamp, :datetime, comment: "创建工作流的操作时间"
    add_column :member_requests, :create_instance_timestamp, :datetime, comment: "创建工作流的操作时间"
    add_column :state_events, :type, :string, comment: "STI"
    add_column :state_events, :state, :string
    add_column :state_events, :state_attr_name, :string, comment: "状态机对应的模型属性名称"
    create_table :bpm_workflow_relations do |t|
      t.references :workflowable, polymorphic: true
      t.references :workflow
      t.timestamps
    end
    create_table :bpm_transitions do |t|
      t.references :workflow
      t.references :place
      t.string :type, comment: "STI"
      t.jsonb :callback_options, comment: "回调设置"
      t.jsonb :options, comment: "transition跳转的额外设置"
      t.timestamps
    end
    create_table :bpm_stars do |t|
      t.references :user
      t.references :workflow
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :bpm_place_relations do |t|
      t.references :workflow
      t.references :source
      t.references :target
      t.timestamps
    end
    create_table :bpm_instance_relations do |t|
      t.references :instance
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "随机数"
      t.string :model_setting_flag, comment: "对应模型的flag"
      t.timestamps
    end
    add_column :memberships, :create_instance_state, :string, comment: "创建工作流的状态"
    add_column :memberships, :create_instance_timestamp, :datetime, comment: "创建工作流的操作时间"
    create_table :bpm_tokens do |t|
      t.timestamps
    end
    add_reference :model_settings, :bpm_workflow
    add_reference :model_settings, :ref_model_setting
    add_column :model_settings, :ref_model_setting_flag, :string, comment: "关联model_setting_flag"
    create_table :bpm_places do |t|
      t.references :workflow
      t.string :type, comment: "STI"
      t.string :seq, comment: "place的唯一序列号，保持一致"
      t.string :name, comment: "节点名称"
      t.string :desc, comment: "节点描述"
      t.integer :position, comment: "根据 tree 边生成的 position"
      t.boolean :is_summary, comment: "是否快捷引用"
      t.jsonb :fields, comment: "workflow form字段在这个place的权限，读写/可见"
      t.jsonb :place_form, comment: "动态表单的json字段"
      t.jsonb :options, comment: "节点的配置信息"
      t.jsonb :timer_options, comment: "节点时间配置"
      t.jsonb :trigger_options, comment: "token进出节点时候可能需要额外操作的内容"
      t.jsonb :token_actions, comment: "操作菜单配置"
      t.jsonb :layout_options, comment: "前端页面使用的配置"
      t.jsonb :activate_options, comment: "同步回调配置"
      t.jsonb :token_source_options, comment: "token place相关配置"
      t.jsonb :form_setting, comment: "表单配置"
      t.timestamps
    end
    create_table :bpm_rules do |t|
      t.references :workflow
      t.string :name, comment: "规则名称"
      t.integer :time_in_second, comment: "设定时间范围"
      t.string :type, comment: "STI"
      t.jsonb :options, comment: "具体配置内容"
      t.timestamps
    end
    create_table :bpm_workflows do |t|
      t.references :app
      t.references :creator
      t.references :mod
      t.references :catalog
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI"
      t.string :name, comment: "流程名称"
      t.text :desc, comment: "流程描述"
      t.jsonb :icon, comment: "流程图标"
      t.jsonb :cover_image, comment: "流程封面"
      t.string :state
      t.integer :position, comment: "catalog内排序"
      t.string :instance_type, comment: "自动生成的instance_type"
      t.string :classify
      t.jsonb :form, comment: "表单配置 "
      t.jsonb :meta, comment: "工作流额外配置信息 "
      t.jsonb :token_actions, comment: "操作菜单配置"
      t.jsonb :trigger_options, comment: "instance状态改变时候需要额外操作的内容"
      t.boolean :auto_complete_same_handle_token, comment: "是否跳过连续相同的审批人"
      t.jsonb :submit_options, comment: "限制条件"
      t.jsonb :form_setting, comment: "表单配置"
      t.timestamps
    end
    create_table :bpm_instances do |t|
      t.references :app
      t.references :workflow
      t.references :creator
      t.references :flowable, polymorphic: true
      t.string :seq, comment: "编号"
      t.string :type, comment: "STI"
      t.jsonb :payload, comment: "流程表单"
      t.jsonb :storage, comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容"
      t.jsonb :summary, comment: "instance在列表页显示的内容"
      t.string :state, comment: "流程状态"
      t.string :flowable_flag, comment: "flowable不同流程的flag"
      t.integer :spent_time_in_second, comment: "耗时时长"
      t.jsonb :cache_payload, comment: "额外存储的结构，根据场合可以作为payload的存储"
      t.datetime :action_at, comment: "激活时间"
      t.jsonb :last_token_attr, comment: "最新token信息"
      t.timestamps
    end
    create_table :bpm_catalogs do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :icon, comment: "图标"
      t.integer :position, comment: "排序"
      t.boolean :published, comment: "是否发布"
      t.timestamps
    end
  end
end
