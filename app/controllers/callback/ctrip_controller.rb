class Callback::CtripController < SimpleController::BaseController
  def notify
    option = params[:ctrip].to_unsafe_h.with_indifferent_access
    config = Travel::Channel::Ctrip.first.config.with_indifferent_access

    # 检查账号
    if config[:account_id] != option[:header][:accountId]
      return render json: { header: { resultCode: '0003', resultMessage: '账号错误' }}
    end

    header_option = option[:header].merge(body: option[:body], signKey: config[:sign_key])
    header = Ctrip::Helper::Client::Header.new(**header_option)
    if header.digest != header.sign
      return render json: { header: { resultCode: '0002', resultMessage: '签名错误' }}
    end

    json = Ctrip::Helper::Order.new(request: option).execute
    render json: json
  end
end
