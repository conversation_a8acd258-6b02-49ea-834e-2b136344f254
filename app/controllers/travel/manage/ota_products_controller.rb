class Travel::Manage::OtaProductsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::OtaProduct,
    collection_name: 'ota_products',
    instance_name: 'ota_product',
    view_path: 'travel/ota_products',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage


  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_ota_products
  end

  private

  def ota_product_params
    params.require(:ota_product).permit(
      *resource_class.try(:extra_permitted_attributes),
      :channel_id,
      :model_flag,
      :type,
      :seq,
      :name,
      :state,
      :ota_no,
      :list_time,
      :delist_time,
      :num,
      :sold_quantity,
      :price,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
    )
  end
end
