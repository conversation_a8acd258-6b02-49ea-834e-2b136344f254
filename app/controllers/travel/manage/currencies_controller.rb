class ::Travel::Manage::CurrenciesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Currency,
    collection_name: 'currencies',
    instance_name: 'currency',
    view_path: 'travel/currencies',
  )

  auth_action :user
  permit_action :travel_manage, :travel_admin

  private

  def currency_params
    params.require(:currency).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :name,
      :unit,
      :symbol,
      :ratio,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
