class ::Travel::Manage::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'travel/activities',
  )

  include Weixin::Controller::Chatable

  auth_action :user
  permit_action :travel_admin, :travel_manage

  def export_xlsx
    file_path = Travel::Activity::ExportShowXlsx.new(resource).export
    send_file file_path, filename: "#{resource.name}.xlsx"
  end

  def fill_create_form
    render json: Travel::Activity.fill_create_form(params[:text], app: current_app)
  end

  def fill_link_create_form
    render json: Travel::Activity.fill_link_create_form(params[:text], order_id: params[:order_id], app: current_app)
  end

  def refresh_demands
    resource.refresh_demands!
    respond_with @activity, template: 'travel/activities/show', status: 201
  end

  protected

  def begin_of_association_chain
    # @current_app = current_app
    current_app
  end

  def method_for_association_chain
    :travel_activities
  end

  private

  def create_activity_params
    update_activity_params.merge(
      creator: current_user
    )
  end

  def update_activity_params
    params.require(:activity).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_temp_id,
      :model_flag,
      :title,
      :desc,
      :duration,
      :state,
      :start_at,
      :import_activity_temp_id,
      :import_start_position,
      :import_count,
      :import_service_name,
      :new_link_order_id,
      :remark,
      :additional_amount,
      :follow_state,
      :import_demand_start_position,
      :import_demand_grp_id,
      # :profit_amount,
      attachments: {},
      import_product_ids: [],
      import_activity_temp_ids: [],
      import_sku_ids: [],
      contact_info: {},
      model_payload: {},
      model_detail: {},
      order_ids: [],
      app_country_ids: [],
      city_ids: [],
      follow_user_ids: [],
      supplier_ids: [],
      traveller_ids: [],
      import_demand_preset_ids: [],
    ).merge(current_user: current_user)
  end
end
