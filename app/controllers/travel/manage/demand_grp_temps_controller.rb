class ::Travel::Manage::DemandGrpTempsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandGrpTemp,
    collection_name: 'demand_grp_temps',
    instance_name: 'demand_grp_temp',
    view_path: 'travel/demand_grp_temps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity_temp, collection_name: :travel_activity_temps

  private

  def begin_of_association_chain
    current_app
  end

  def demand_grp_temp_params
    params.require(:demand_grp_temp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_temp_id,
      :app_id,
      :model_flag,
      :name,
      # :mode,
      :amount,
      :amount_unit,
      :amount_currency_id,
      :amount_ratio,
      :amount_rmb,
      :amount_sync,
      :service_name,
      demand_define_ids: [],
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
