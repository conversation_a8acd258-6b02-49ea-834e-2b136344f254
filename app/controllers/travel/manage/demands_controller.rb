class ::Travel::Manage::DemandsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Demand,
    collection_name: 'demands',
    instance_name: 'demand',
    view_path: 'travel/demands',
    order_off: true
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity, collection_name: :travel_activities, optional: true
  belongs_to :product, collection_name: :travel_products, optional: true
  belongs_to :offer, collection_name: :travel_offers, optional: true

  def import_to_demand_preset
    demand_preset = resource.import_to_demand_preset!
    render json: demand_preset
  end

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    parent ? :demands : :travel_demands
  end

  def demand_params
    params.require(:demand).permit(
      *resource_class.try(:extra_permitted_attributes),
      :demand_grp_id,
      :demand_id,
      :activity_id,
      :demand_temp_id,
      :offer_id,
      :app_id,
      :poi_region_id,
      :model_flag,
      :name,
      :desc,
      :mode,
      :count,
      :duration,
      :start_offset,
      :start_position,
      # :end_offset,
      :offer_amount,
      :offer_amount_unit,
      :offer_amount_ratio,
      :offer_amount_rmb,
      :offer_amount_currency_id,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :amount_currency_id,
      # :start_date,
      # :end_date,
      :offer_total_amount,
      :demand_define_id,
      :new_offer_supplier_id,
      :update_offer_state,
      :demand_preset_id,
      :traveller_no,
      :state,
      payload: {},
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    ).merge(current_user: current_user)
  end
end
