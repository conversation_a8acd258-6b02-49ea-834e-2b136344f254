class Travel::Manage::ReceiptsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Receipt,
    collection_name: 'receipts',
    instance_name: 'receipt',
    view_path: 'travel/receipts',
  )
  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :travel_receipts
  end

  private

  def create_receipt_params
    update_receipt_params.merge(
      creator: current_user
    )
  end

  def update_receipt_params
    params.require(:receipt).permit(
      *resource_class.try(:extra_permitted_attributes),
      :payment_id,
      :seq,
      :model_flag,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :content,
      model_payload: {},
      model_detail: {},
      attachments: {},
    )
  end
end
