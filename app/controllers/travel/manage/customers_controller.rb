class Travel::Manage::CustomersController < SimpleController::BaseController
  defaults(
    resource_class: User,
    collection_name: 'users',
    instance_name: 'user',
    view_path: 'travel/users',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_customers
  end

  private

  def create_user_params
    update_user_params.merge(
      model_flag: 'travel',
      account: params[:customer]&.dig('account'),
      app: current_app
    )
  end

  def update_user_params
    params.require(:customer).permit(
      *resource_class.try(:extra_permitted_attributes),
      :effective_at,
      :invalid_at,
      :name,
      :nickname,
      :mobile,
      :email,
      :gender,
      :identity_id,
      :blocked_at,
      :is_blocked,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      avatar: {},
      res_tag_ids: [],
      travel_channel_ids: [],
    )
  end
end
