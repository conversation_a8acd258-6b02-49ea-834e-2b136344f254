class Travel::Manage::DestinationsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Destination,
    collection_name: 'destinations',
    instance_name: 'destination',
    view_path: 'travel/destinations',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_destinations
  end

  private

  def update_destination_params
    params.require(:destination).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :hotted_at,
      :is_hotted,
      :name,
      :state,
      :desc,
      :icon,
      :content,
      :position,
      :score,
      :price,
      :used_count,
      :views_count,
      :stars_count,
      :country_id,
      cover_image: {},
      payload: {},
      record_ids: [],
    )
  end

  def create_destination_params
    update_destination_params.merge(creator: current_user)
  end
end
