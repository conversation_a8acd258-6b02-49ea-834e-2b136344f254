class Travel::Manage::OffersController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Offer,
    collection_name: 'offers',
    instance_name: 'offer',
    view_path: 'travel/offers',
  )
  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity, collection_name: :travel_activities, optional: true
  belongs_to :demand_grp, collection_name: :travel_demand_grps, optional: true

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    (@activity.present? || @demand_grp.present?) ?
      :offers : :travel_offers
  end

  private

  def offer_params
    params.require(:offer).permit(
      *resource_class.try(:extra_permitted_attributes),
      :payment_id,
      :model_flag,
      :name,
      :state,
      :pay_state,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :amount_currency_id,
      :supplier_id,
      :amount_sync,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      demand_ids: [],
    ).merge(
      app: current_app
    )
  end
end
