class ::Travel::Manage::ActivityTempsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::ActivityTemp,
    collection_name: 'activity_temps',
    instance_name: 'activity_temp',
    view_path: 'travel/activity_temps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  def clone
    resource.clone(
      creator: current_user
    )
    render json: { result: 'success' }
  end

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_activity_temps
  end

  def create_activity_temp_params
    activity_temp_params.merge(
      creator: current_user
    )
  end

  def activity_temp_params
    params.require(:activity_temp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :creator_id,
      :app_id,
      :model_flag,
      :name,
      :desc,
      :duration,
      :state,
      :activity_temp_dir_id,
      :import_demand_start_position,
      :import_demand_grp_id,
      app_country_ids: [],
      city_ids: [],
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      supplier_ids: [],
      import_demand_preset_ids: [],
      ota_sku_origins_attributes: [:id, :name, :code, :state, :from, :channel_id, :_destroy, payload: {}],
    )
  end
end
