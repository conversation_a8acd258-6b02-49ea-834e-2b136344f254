class ::Travel::Manage::DayTempsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DayTemp,
    collection_name: 'day_temps',
    instance_name: 'day_temp',
    view_path: 'travel/day_temps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity_temp, collection_name: :travel_activity_temps, optional: true

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    parent ? :day_temps : :travel_day_temps
  end

  def day_temp_params
    params.require(:day_temp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_temp_id,
      :app_id,
      :model_flag,
      :name,
      :desc,
      :position,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      city_ids: [],
      scenic_spot_ids: [],
    )
  end
end
