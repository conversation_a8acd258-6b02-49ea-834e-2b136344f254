class ::Travel::Manage::ActivityTempDirsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::ActivityTempDir,
    collection_name: 'activity_temp_dirs',
    instance_name: 'activity_temp_dir',
    view_path: 'travel/activity_temp_dirs',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_activity_temp_dirs
  end

  private

  def activity_temp_dir_params
    params.require(:activity_temp_dir).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :model_flag,
      :name,
      :desc,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
