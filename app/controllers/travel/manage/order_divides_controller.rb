class ::Travel::Manage::OrderDividesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::OrderDivide,
    collection_name: 'order_divides',
    instance_name: 'order_divide',
    view_path: 'travel/order_divides',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :order, collection_name: :travel_orders

  private

  def begin_of_association_chain
    current_app
  end

  def order_divide_params
    params.require(:order_divide).permit(
      *resource_class.try(:extra_permitted_attributes),
      :order_id,
      :user_id,
      :weight,
    )
  end
end
