class ::Travel::Manage::DemandGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandGrp,
    collection_name: 'demand_grps',
    instance_name: 'demand_grp',
    view_path: 'travel/demand_grps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity, collection_name: :travel_activities, optional: true

  private

  def begin_of_association_chain
    current_app
  end

  def demand_grp_params
    params.require(:demand_grp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_id,
      :demand_grp_temp_id,
      :app_id,
      :model_flag,
      :name,
      :amount,
      :amount_unit,
      :amount_currency_id,
      :amount_ratio,
      :amount_rmb,
      # :mode,
      :amount_sync,
      demand_define_ids: [],
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
