class Travel::Manage::NoticesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Notice,
    collection_name: 'notices',
    instance_name: 'notice',
    view_path: 'travel/notices',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_notices
  end

  private

  def create_notice_params
    update_notice_params.merge(
      user: current_user
    )
  end

  def update_notice_params
    params.require(:notice).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :name,
      :content,
      :state,
      payload: {},
    )
  end
end
