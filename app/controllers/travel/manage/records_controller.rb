class Travel::Manage::RecordsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Record,
    collection_name: 'records',
    instance_name: 'record',
    view_path: 'travel/records',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_records
  end


  private

  def update_record_params
    params.require(:record).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :type,
      :stars_count,
      :hotted_at,
      :is_hotted,
      :name,
      :state,
      :desc,
      :icon,
      :content,
      :position,
      :price,
      :score,
      :used_count,
      cover_image: {},
      payload: {},
    )
  end

  def create_record_params
    update_record_params.merge(creator: current_user)
  end
end
