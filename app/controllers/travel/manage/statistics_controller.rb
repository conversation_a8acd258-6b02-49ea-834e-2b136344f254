class Travel::Manage::StatisticsController < SimpleController::BaseController
  auth_action :user
  permit_action :travel_admin, :travel_manage

  def create
    orders = current_app.travel_orders.where.not(state: 'terminated')
    activities = current_app.travel_activities.where.not(state: 'terminated')

    data = {
      order: {
        unpaid: orders.ransack(amount_state_eq: '未收款').result.count,
        unlink_divide: orders.ransack(order_divides_id_gt: 0).result.distinct.count,
        unlink_activity: orders.ransack(activities_id_gt: 0).result.distinct.count,
      },
      activity: {
        spec_todo: activities.where(follow_state: 'todo', start_at: [Date.today, Date.tomorrow]).count,
        todo: activities.where(follow_state: 'todo').count,
        today: activities.where(start_at: Date.today).count,
        tomorrow: activities.where(start_at: Date.tomorrow).count,
      }
    }

    render json: data, status: 201
  end

  def order
    orders = current_app.travel_orders.where.not(state: 'terminated')
    orders = orders.ransack(params[:q]).result
    last_orders = nil

    if params[:date].present?
      start_at = params[:date].first.to_date.beginning_of_day
      end_at = params[:date].last.to_date.end_of_day
      orders = orders.between_times()

      last_start_at = 1.years.ago(start_at)
      last_end_at = 1.years.ago(end_at)
      last_orders = orders.where(last_total_amount: last_start_at..last_end_at)
    end

    data = {
      total_order_amount: orders.sum(:order_amount).round(2),
      income_amount: orders.sum(:income_amount).round(2),
      last_total_amount: orders.sum(:last_total_amount).round(2),
    }

    render json: data
  end
end
