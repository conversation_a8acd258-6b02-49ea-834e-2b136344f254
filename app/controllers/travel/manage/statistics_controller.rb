class Travel::Manage::StatisticsController < SimpleController::BaseController
  auth_action :user
  permit_action :travel_admin, :travel_manage

  def create
    orders = current_app.travel_orders.where.not(state: 'terminated')
    activities = current_app.travel_activities.where.not(state: 'terminated')

    data = {
      order: {
        unpaid: orders.ransack(amount_state_eq: '未收款').result.count,
        unlink_divide: orders.ransack(order_divides_id_gt: 0).result.distinct.count,
        unlink_activity: orders.ransack(activities_id_gt: 0).result.distinct.count,
      },
      activity: {
        spec_todo: activities.where(state: 'todo', start_at: [Date.today, Date.tomorrow]).count,
        todo: activities.where(follow_state: 'todo').count,
        today: activities.where(start_at: Date.today).count,
        tomorrow: activities.where(start_at: Date.tomorrow).count,
      }
    }

    render json: data, status: 201
  end

  def orders
    association = current_app.travel_orders.where.not(state: 'terminated')
    association = association.ransack(params[:q]).result
    last_orders = association
    orders = association

    if params[:date].present?
      start_at = params[:date].first.to_date.beginning_of_day
      end_at = params[:date].last.to_date.end_of_day
      orders = params[:mode] == 'created' ? association.where(created_at: start_at..end_at) : association.where(trip_start_date: start_at..end_at)

      last_start_at = 1.years.ago(start_at)
      last_end_at = 1.years.ago(end_at)
      last_orders = params[:mode] == 'created' ? association.where(created_at: last_start_at..last_end_at) : association.where(trip_start_date: last_start_at..last_end_at)
    end

    total_order_amount = orders.sum(:order_amount).round(2)
    income_amount = orders.sum(:income_amount).round(2)
    unrecieved_amount = total_order_amount - income_amount

    data = {
      total_order_amount: total_order_amount,
      income_amount: income_amount,
      unreceived_amount: unrecieved_amount,
      received_amount_rate: total_order_amount > 0 ? (income_amount * 100.0 / total_order_amount).round(2) : 0,
    }

    if params[:data].present?
      last_total_amount = last_orders.sum(:order_amount).round(2)
      last_income_amount = last_orders.sum(:income_amount).round(2)

      total_amount_rate = last_total_amount == 0 ? nil : (data[:total_order_amount] -  last_total_amount) * 100.0 / last_total_amount
      income_amount_rate = last_income_amount == 0 ? nil : (data[:income_amount] - last_income_amount) * 100.0 / last_income_amount

      data.merge!(
        last_total_amount: last_total_amount,
        last_income_amount: last_income_amount,
        total_amount_rate: total_amount_rate ? total_amount_rate.round(2) : nil,
        income_amount_rate: income_amount_rate ? income_amount_rate.round(2) : nil,
      )
    end

    render json: data, status: 201
  end
end
