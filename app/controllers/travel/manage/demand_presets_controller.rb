class Travel::Manage::DemandPresetsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandPreset,
    collection_name: 'demand_presets',
    instance_name: 'demand_preset',
    view_path: 'travel/demand_presets',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_demand_presets
  end

  private

  def demand_preset_params
    params.require(:demand_preset).permit(
      *resource_class.try(:extra_permitted_attributes),
      :demand_grp_preset_id,
      :poi_region_id,
      :demand_define_id,
      :model_flag,
      :name,
      :desc,
      :mode,
      :count,
      :duration,
      :start_offset,
      :end_offset,
      :wait_for_move_to_start_offset,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :offer_amount,
      :offer_amount_unit,
      :offer_amount_ratio,
      :offer_amount_rmb,
      :offer_total_amount,
      :offer_total_amount_unit,
      :offer_total_amount_ratio,
      :offer_total_amount_rmb,
      :offer_amount_currency_id,
      :supplier_id,
      :traveller_no,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
      payload_summary: {},
    )
  end
end
