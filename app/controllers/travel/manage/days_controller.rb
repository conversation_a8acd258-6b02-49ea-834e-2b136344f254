class ::Travel::Manage::DaysController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Day,
    collection_name: 'days',
    instance_name: 'day',
    view_path: 'travel/days',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity, collection_name: :travel_activities, optional: true

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    parent ? :days : :travel_days
  end

  def day_params
    params.require(:day).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_id,
      :day_temp_id,
      :app_id,
      :model_flag,
      :name,
      :desc,
      :date,
      :position,
      city_ids: [],
      scenic_spot_ids: [],
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
