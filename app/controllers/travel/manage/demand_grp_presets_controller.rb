class Travel::Manage::DemandGrpPresetsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandGrpPreset,
    collection_name: 'demand_grp_presets',
    instance_name: 'demand_grp_preset',
    view_path: 'travel/demand_grp_presets',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_demand_grp_presets
  end

  private

  def demand_grp_preset_params
    params.require(:demand_grp_preset).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :desc,
      :state,
      form: {},
      payload: {},
    )
  end
end
