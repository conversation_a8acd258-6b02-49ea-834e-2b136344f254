class Travel::Manage::SuppliersController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Supplier,
    collection_name: 'suppliers',
    instance_name: 'supplier',
    view_path: 'travel/suppliers',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_suppliers
  end

  private

  def supplier_params
    params.require(:supplier).permit(
      *resource_class.try(:extra_permitted_attributes),
      :org_identity_id,
      :model_flag,
      :parent_id,
      :code,
      :name,
      :short_name,
      :type,
      :position,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
      payload_summary: {},
      country_ids: [],
    )
  end
end
