class Travel::Manage::PoiCountriesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Poi::Country,
    collection_name: 'countries',
    instance_name: 'country',
    view_path: 'travel/poi/countries',
  )
  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def	begin_of_association_chain
    case params[:mode].to_s
    when 'public'
      nil
    else
      current_app
    end
  end

  def	method_for_association_chain
    :travel_poi_countries
  end

  private

  def country_params
    params.require(:country).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :name,
      :en_name,
      :continent,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      detail: {},
    )
  end
end
