class Travel::Manage::RevenuesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Revenue,
    collection_name: 'revenues',
    instance_name: 'revenue',
    view_path: 'travel/revenues',
  )
  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :order, collection_name: :travel_orders

  protected

  def	begin_of_association_chain
    current_app
  end

  private

  def create_revenue_params
    update_revenue_params.merge(
      creator: current_user
    )
  end

  def update_revenue_params
    params.require(:revenue).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :seq,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :mode,
      :remark,
      :type,
      :create_time,
      :platform,
      model_payload: {},
      model_detail: {},
      attachments: {},
    )
  end
end
