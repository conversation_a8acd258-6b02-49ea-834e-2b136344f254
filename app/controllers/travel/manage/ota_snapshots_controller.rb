class ::Travel::Manage::OtaSnapshotsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::OtaSnapshot,
    collection_name: 'ota_snapshots',
    instance_name: 'ota_snapshot',
    view_path: 'travel/ota_snapshots',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :ota_sku, collection_name: :travel_ota_skus, optional: true
  belongs_to :order, collection_name: :travel_orders, optional: true

  private

  def begin_of_association_chain
    current_app
  end

  def ota_snapshot_params
    params.require(:ota_snapshot).permit(
      *resource_class.try(:extra_permitted_attributes),
      :ota_sku_id,
      :order_id,
      :model_flag,
      :type,
      :name,
      :amount,
      :count,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      meta: {},
    )
  end
end
