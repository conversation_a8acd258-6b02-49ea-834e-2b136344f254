class ::Travel::Manage::OfferTempsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::OfferTemp,
    collection_name: 'offer_temps',
    instance_name: 'offer_temp',
    view_path: 'travel/offer_temps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :activity_temp, collection_name: :travel_activity_temps, optional: true
  belongs_to :product, collection_name: :travel_products, optional: true
  belongs_to :demand_grp_temp, collection_name: :travel_demand_grp_temps, optional: true

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    (@activity_temp.present? || @product.present? || @demand_grp_temp.present?) ?
      :offer_temps : :travel_offer_temps
  end

  private

  def offer_temp_params
    params.require(:offer_temp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :product_id,
      :activity_temp_id,
      :app_id,
      :supplier_id,
      :model_flag,
      :name,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :amount_currency_id,
      :amount_sync,
      demand_temp_ids: [],
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
