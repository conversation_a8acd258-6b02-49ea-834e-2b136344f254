class Travel::Manage::NoticeGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::NoticeGrp,
    collection_name: 'notice_grps',
    instance_name: 'notice_grp',
    view_path: 'travel/notice_grps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_notice_grps
  end

  private

  def notice_grp_params
    params.require(:notice_grp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :name,
      :state,
      payload: {},
      notice_relate_actions_attributes: [:id, :notice_id, :position, :_destroy],
    )
  end
end
