class ::Travel::Manage::PoiRegionsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Poi::Region,
    collection_name: 'regions',
    instance_name: 'region',
    view_path: 'travel/poi/regions',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :poi_country, collection_name: :travel_poi_countries, optional: true

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    @poi_country.present? ? :regions : :travel_poi_app_regions
  end

  def	after_association_chain association
    association.where(app_id: [nil, current_user.app.id])
  end

  private

  def update_region_params
    resource.app.nil? ? public_update_region_params : app_update_region_params
  end

  def create_region_params
    params.require(:region).permit(
      *resource_class.try(:extra_permitted_attributes),
      :parent_id,
      :type,
      :model_flag,
      :name,
      :en_name,
      :position,
      :longitude,
      :latitude,
      :address,
      model_payload: {},
      model_detail: {},
      detail: {},
    ).merge(
      app: current_user.app,
      creator: current_user,
    )
  end

  def public_update_region_params
    params.require(:region).permit(
      *resource_class.try(:extra_permitted_attributes),
    )
  end

  def app_update_region_params
    params.require(:region).permit(
      *resource_class.try(:extra_permitted_attributes),
      :parent_id,
      :type,
      :model_flag,
      :name,
      :position,
      model_payload: {},
      model_detail: {},
      detail: {},
    )
  end
end
