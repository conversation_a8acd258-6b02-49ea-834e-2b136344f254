class Travel::Manage::ChannelsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Channel,
    collection_name: 'channels',
    instance_name: 'channel',
    view_path: 'travel/channels',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_channels
  end

  private

  def channel_params
    params.require(:channel).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :type,
      :name,
      :state,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      config: {},
      display_config: {},
      form: {},
    )
  end
end
