class Travel::Manage::PoiAppCountriesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Poi::AppCountry,
    collection_name: 'app_countries',
    instance_name: 'app_country',
    view_path: 'travel/poi/app_countries',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def	begin_of_association_chain
    current_user.app
  end

  def	method_for_association_chain
    :travel_poi_app_countries
  end

  private

  def app_country_params
    params.require(:app_country).permit(
      *resource_class.try(:extra_permitted_attributes),
      :country_id,
      :model_flag,
      :name,
      :color,
      :position,
      :state,
      :icon,
      :score,
      :used_count,
      :views_count,
      :stars_count,
      :is_hotted,
      :hotted_at,
      cover_image: {},
      payload: {},
      content: {},
      model_payload: {},
      model_detail: {},
      conf: {},
    )
  end
end
