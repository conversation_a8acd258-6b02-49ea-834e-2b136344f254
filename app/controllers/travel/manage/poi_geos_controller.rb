class Travel::Manage::PoiGeosController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Poi::Geo,
    collection_name: 'geos',
    instance_name: 'geo',
    view_path: 'travel/poi/geos',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_poi_geos
  end

  private

  def geo_params
    params.require(:geo).permit(
      *resource_class.try(:extra_permitted_attributes),
      :parent_id,
      :type,
      :model_flag,
      :name,
      :position,
      :country_id,
      :en_name,
      :longitude,
      :latitude,
      :address,
      :recommended_at,
      :is_recommended,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      detail: {},
      cover_image: {},
      meta: {},
    )
  end

  def create_geo_params
    geo_params.merge(creator: current_user)
  end
end
