class Travel::Manage::ProductsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Product,
    collection_name: 'products',
    instance_name: 'product',
    view_path: 'travel/products',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  def import_entity
    demand_grp = nil
    activity = nil
    resource.class.transaction do
      if import_entity_params[:demand_grp_id].present?
        demand_grp = current_app.travel_demand_grps.find(import_entity_params[:demand_grp_id])
        activity = demand_grp.activity
      elsif import_entity_params[:activity_id].present?
        activity = current_app.travel_activities.find(import_entity_params[:activity_id])
        demand_grp = activity.demand_grps.create!(name: import_entity_params[:demand_grp_name] || resource.name)
      elsif import_entity_params[:activity_extra].present?
        activity = current_app.travel_activities.create!(
          creator: current_user,
          **import_entity_params[:activity_extra],
        )
        demand_grp = activity.demand_grps.create!(name: import_entity_params[:demand_grp_name] || resource.name)
      end

      unless demand_grp
        raise Error::BaseError.new(message: '下发参数缺失')
      end

      resource.generate_into_demand_grps(
        demand_grp,
        **((import_entity_params[:import_extra] || {}).to_h.symbolize_keys)
      )

      render json: { message: 'ok', activity_id: activity.id }, status: 201
    end
  end

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_products
  end

  private

  def import_entity_params
    params.require(:product).permit(
      :demand_grp_id, :activity_id, :demand_grp_name,
      import_extra: [:start_position],
      activity_extra: [
        :title,
        :desc,
        :duration,
        :state,
        :start_at,
        contact_info: {},
        model_payload: {},
        model_detail: {},
        order_ids: [],
        country_ids: [],
        follow_user_ids: [],
      ]
    )
  end

  def create_product_params
    update_product_params.merge(
      creator: current_user
    )
  end

  def update_product_params
    params.require(:product).permit(
      *resource_class.try(:extra_permitted_attributes),
      :product_dir_id,
      :model_flag,
      :seq,
      :name,
      :state,
      :type,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :amount_currency_id,
      :duration,
      :amount_sync,
      :destination_id,
      :price,
      :origin_price,
      :position,
      :sale_count,
      :stars_count,
      :score,
      :origin_score,
      :origin_sale_count,
      :app_country_id,
      :flag,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      cover_image: {},
      content: {},
      country_ids: [],
    )
  end
end
