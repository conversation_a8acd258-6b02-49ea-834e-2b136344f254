class ::Travel::Manage::OtaSkusController < SimpleController::BaseController
  defaults(
    resource_class: Travel::OtaSku,
    collection_name: 'ota_skus',
    instance_name: 'ota_sku',
    view_path: 'travel/ota_skus',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_ota_skus
  end

  def ota_sku_params
    params.require(:ota_sku).permit(
      *resource_class.try(:extra_permitted_attributes),
      :channel_id,
      :product_id,
      :activity_temp_id,
      :model_flag,
      :type,
      :name,
      :ota_product_no,
      :ota_sku_no,
      :app_id,
      :rate,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      meta: {},
    )
  end
end
