class Travel::Manage::ManualRevenuesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::ManualRevenue,
    collection_name: 'revenues',
    instance_name: 'revenue',
    view_path: 'travel/revenues',
  )
  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :order, collection_name: :travel_orders

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :manual_revenues
  end

  private

  def revenue_params
    params.require(:manual_revenue).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :seq,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :mode,
      :remark,
      :type,
      :create_time,
      :platform,
      model_payload: {},
      model_detail: {},
      attachments: {},
    )
  end
end
