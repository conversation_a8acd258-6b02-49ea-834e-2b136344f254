class ::Travel::Manage::ProductDaysController < SimpleController::BaseController
  defaults(
    resource_class: Travel::ProductDay,
    collection_name: 'product_days',
    instance_name: 'product_day',
    view_path: 'travel/product_days',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :product, collection_name: :travel_products, optional: true

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    parent ? :product_days : :travel_product_days
  end

  def product_day_params
    params.require(:product_day).permit(
      *resource_class.try(:extra_permitted_attributes),
      :product_id,
      :app_id,
      :model_flag,
      :name,
      :position,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
