class Travel::Manage::PaymentsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Payment,
    collection_name: 'payments',
    instance_name: 'payment',
    view_path: 'travel/payments',
  )

  include State::Controller::Eventable

  auth_action :user
  permit_action :travel_admin, :travel_manage


  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :travel_payments
  end

  private

  def update_payment_params
    params.require(:payment).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :state,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :amount_currency_id,
      :supplier_id,
      :pay_account_id,
      model_payload: {},
      model_detail: {},
      offer_ids: [],
    )
  end

  def create_payment_params
    update_payment_params.merge(
      creator: current_user
    )
  end
end
