class ::Travel::Manage::OrdersController < SimpleController::BaseController
  include Com::Controller::Versionable

  defaults(
    resource_class: Travel::Order,
    collection_name: 'orders',
    instance_name: 'order',
    view_path: 'travel/orders',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  def fill_create_form
    render json: Travel::Order.fill_create_form(params[:text], app: current_app)
  end

  # 绒布预约单
  def sync
    @order = resource.sync_order(seq: params[:seq])
    respond_with @order, template: "#{view_path}/show", status: 201
  end

  def sync_all_orders
    Travel::OrderJob.perform_later
    render json: { message: '同步任务已提交' }
  end

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_orders
  end

  def create_order_params
    params.require(:order).permit(
      *resource_class.try(:extra_permitted_attributes),
      :order_type,
      :origin,
      :product_id,
      :activity_temp_id,
      :channel_id,
      :ota_sku_id,
      :model_flag,
      :seq,
      :type,
      :name,
      # :ota_no,
      # :ota_state,
      :state,
      :traveler_num,
      :trip_start_date,
      :trip_end_date,
      :trip_days,
      :item_num,
      :remark,
      :order_amount,
      :plus_amount,
      :discount_amount,
      :is_invoiced,
      :import_start_position,
      :import_count,
      :sub_order_id,
      # :app_id,
      channel_payload: {},
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      # meta: {},
      activity_ids: [],
      country_ids: [],
      customer_ids: [],
      maven_ids: [],
      attachments: {},
      order_divides_attributes: [:id, :_destroy, :user_id, :weight],
    ).merge(
      creator: current_user,
      manual_revenues_attributes: manual_revenues_attributes,
    )
  end

  def update_order_params
    resource.type == 'Travel::Order' ?
      create_order_params :
      params.require(:order).permit(
        *resource_class.try(:extra_permitted_attributes),
        :origin,
        :order_type,
        :product_id,
        :activity_temp_id,
        :model_flag,
        :name,
        :state,
        :is_invoiced,
        :remark,
        :plus_amount,
        :discount_amount,
        :order_amount,
        :traveler_num,
        :trip_start_date,
        :trip_end_date,
        :import_start_position,
        :import_count,
        attachments: {},
        model_payload: {},
        model_payload_summary: {},
        model_detail: {},
        activity_ids: [],
        country_ids: [],
        customer_ids: [],
        maven_ids: [],
        order_divides_attributes: [:id, :_destroy, :user_id, :weight],
      ).merge(
        manual_revenues_attributes: manual_revenues_attributes,
      )
  end

  def manual_revenues_attributes
    return [] unless params[:order][:manual_revenues_attributes]

    params[:order][:manual_revenues_attributes].map do |attrs|
      result = attrs.permit(
        :id,
        :_destroy,
        :seq,
        :amount,
        :amount_unit,
        :amount_ratio,
        :amount_rmb,
        :mode,
        :remark,
        :type,
        :create_time,
        :platform,
        model_payload: {},
        model_detail: {},
        attachments: {},
      )

      # 创建时 merge creator
      result.merge(creator: current_user) if result[:id].blank?
      result
    end
  end
end
