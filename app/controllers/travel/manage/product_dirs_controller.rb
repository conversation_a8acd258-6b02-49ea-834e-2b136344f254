class ::Travel::Manage::ProductDirsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::ProductDir,
    collection_name: 'product_dirs',
    instance_name: 'product_dir',
    view_path: 'travel/product_dirs',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_product_dirs
  end

  def product_dir_params
    params.require(:product_dir).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :model_flag,
      :name,
      :desc,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
