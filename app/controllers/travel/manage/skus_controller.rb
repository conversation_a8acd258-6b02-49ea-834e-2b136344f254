class Travel::Manage::SkusController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Sku,
    collection_name: 'skus',
    instance_name: 'sku',
    view_path: 'travel/skus',
  )

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_skus
  end

  private

  def create_sku_params
    update_sku_params.merge(
      creator: current_user,
    )
  end

  def update_sku_params
    params.require(:sku).permit(
      *resource_class.try(:extra_permitted_attributes),
      :product_id,
      :creator_id,
      :type,
      :seq,
      :comments_count,
      :comment_conf,
      :recommended_at,
      :is_recommended,
      :name,
      :state,
      :price,
      :origin_price,
      :position,
      :total_number,
      :freeze_number,
      :sale_count,
      :stars_count,
      :score,
      :origin_score,
      :origin_sale_count,
      :rate,
      content: {},
      cover_image: {},
      payload: {},
      sku_prices_attributes: [
        :_destroy,
        :id,
        :price,
        :original_price,
        :sale_date,
        :mode,
        option: {},
        payload: {},
      ],
    )
  end
end
