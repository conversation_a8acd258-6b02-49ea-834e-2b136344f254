class Travel::Manage::PayAccountsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::PayAccount,
    collection_name: 'pay_accounts',
    instance_name: 'pay_account',
    view_path: 'travel/pay_accounts',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_pay_acccounts
  end

  private

  def pay_account_params
    params.require(:pay_account).permit(
      *resource_class.try(:extra_permitted_attributes),
      :supplier_id,
      :model_flag,
      :type,
      :name,
      :state,
      :account_type,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
    )
  end
end
