class ::Travel::Manage::DemandTempsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandTemp,
    collection_name: 'demand_temps',
    instance_name: 'demand_temp',
    view_path: 'travel/demand_temps',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  belongs_to :product, collection_name: :travel_products, optional: true
  belongs_to :activity_temp, collection_name: :travel_activity_temps, optional: true

  private

  def begin_of_association_chain
    current_app
  end

  def demand_temp_params
    params.require(:demand_temp).permit(
      *resource_class.try(:extra_permitted_attributes),
      :product_id,
      :demand_grp_temp_id,
      # :demand_grp_id,
      :app_id,
      :poi_region_id,
      :model_flag,
      :name,
      :desc,
      :count,
      :duration,
      :start_offset,
      :start_position,
      :offer_total_amount,
      # :end_offset,
      :offer_amount,
      :offer_amount_unit,
      :offer_amount_ratio,
      :offer_amount_rmb,
      :offer_amount_currency_id,
      :amount,
      :amount_unit,
      :amount_ratio,
      :amount_rmb,
      :amount_currency_id,
      :mode,
      :offer_temp_id,
      :demand_define_id,
      :wait_for_move_to_start_offset,
      :new_offer_supplier_id,
      :demand_preset_id,
      :traveller_no,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
      payload_summary: {},
    )
  end
end
