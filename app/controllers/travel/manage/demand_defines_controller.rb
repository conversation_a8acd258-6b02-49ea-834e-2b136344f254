class ::Travel::Manage::DemandDefinesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandDefine,
    collection_name: 'demand_defines',
    instance_name: 'demand_define',
    view_path: 'travel/demand_defines',
  )

  auth_action :user
  permit_action :travel_admin, :travel_manage

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_demand_defines
  end

  def demand_define_params
    params.require(:demand_define).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :name,
      :type,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      cover_image: {},
      form: {},
    )
  end
end
