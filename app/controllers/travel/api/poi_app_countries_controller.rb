class Travel::Api::PoiAppCountriesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Poi::AppCountry,
    collection_name: 'app_countries',
    instance_name: 'app_country',
    view_path: 'travel/poi/app_countries',
    order_off: true,
  )

  include Favor::Controller::Markable
  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_poi_app_countries
  end

  def after_association_chain association
    association.order(is_hotted: :desc, position: :asc)
  end
end
