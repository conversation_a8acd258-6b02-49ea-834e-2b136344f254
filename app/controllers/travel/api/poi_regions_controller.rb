class Travel::Api::PoiRegionsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Poi::Region,
    collection_name: 'regions',
    instance_name: 'region',
    view_path: 'travel/poi/regions',
    order_off: true
  )

  auth_action :user, skip_error: true

  protected

  def after_association_chain association
    association.where(app_id: [nil, current_user&.app_id]).order(position: :asc)
  end
end
