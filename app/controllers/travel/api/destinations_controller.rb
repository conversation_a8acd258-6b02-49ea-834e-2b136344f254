class Travel::Api::DestinationsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Destination,
    collection_name: 'destinations',
    instance_name: 'destination',
    view_path: 'travel/destinations',
    order_off: true,
  )

  include Favor::Controller::Markable
  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_destinations
  end

  def after_association_chain association
    association.order(is_hotted: :desc, position: :asc)
  end
end
