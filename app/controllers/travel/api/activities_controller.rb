class Travel::Api::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'travel/activities',
  )

  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_activities
  end
end
