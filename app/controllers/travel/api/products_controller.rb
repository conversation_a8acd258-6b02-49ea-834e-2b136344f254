class Travel::Api::ProductsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Product,
    collection_name: 'products',
    instance_name: 'product',
    view_path: 'travel/products',
    order_off: true,
  )

  include Favor::Controller::Markable
  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_products
  end

  def after_association_chain association
    association.onsale.order(is_recommended: :desc, position: :asc)
  end
end
