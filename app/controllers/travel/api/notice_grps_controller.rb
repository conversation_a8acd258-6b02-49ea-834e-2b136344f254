class Travel::Api::NoticeGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::NoticeGrp,
    collection_name: 'notice_grps',
    instance_name: 'notice_grp',
    view_path: 'travel/notice_grps',
  )

  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_notice_grps
  end
end
