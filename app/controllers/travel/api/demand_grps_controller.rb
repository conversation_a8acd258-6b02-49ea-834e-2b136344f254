class Travel::Api::DemandGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandGrp,
    collection_name: 'demand_grps',
    instance_name: 'demand_grp',
    view_path: 'travel/demand_grps',
  )

  auth_action :user, skip_error: true
  belongs_to :activity, collection_name: :travel_activities, optional: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    parent ? :demand_grps : :travel_demand_grps
  end
end
