class Travel::Api::DemandTempsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::DemandTemp,
    collection_name: 'demand_temps',
    instance_name: 'demand_temp',
    view_path: 'travel/demand_temps',
  )

  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_demand_temps
  end
end
