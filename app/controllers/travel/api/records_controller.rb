class Travel::Api::RecordsController < SimpleController::BaseController
  defaults(
    resource_class: Travel::Record,
    collection_name: 'records',
    instance_name: 'record',
    view_path: 'travel/records',
    order_off: true,
  )

  include Favor::Controller::Markable
  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :travel_records
  end
end
