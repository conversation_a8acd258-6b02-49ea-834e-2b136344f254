class Travel::Customer < Member
  has_many :travel_orders, class_name: 'Travel::Order', foreign_key: :customer_id
  has_many :travel_activities, class_name: 'Travel::Activity', foreign_key: :customer_id

  action_store_by(
    :relate,
    :customer,
    class_name: 'Travel::Customer',
    action_class_name: 'Travel::CustomerAction',
    alias_name: 'customers',
    inverse_alias_name: 'travel_orders',
  )
end
