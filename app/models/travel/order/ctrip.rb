class Travel::Order::Ctrip < Travel::Order
  after_commit :reset_meta!, on: [:create]

  def reset_meta!
    return nil if model_detail.nil?
    meta, amount, travellers, payment = {}, 0, [], {}
    data = model_detail.with_indifferent_access
    items = data[:items] || []


    items.each do |item|
      passengers = item[:passengers] || []
      _pass = passengers.map do |passenger|
        {
          name: passenger[:name],
          gender: passenger[:gender] == 'F' ? '女' : '男',
          birthday: passenger[:birthDate],
          phone: passenger[:mobile],
          identity_type: passenger[:cardType] ? change_identity_type.key(passenger[:cardType].to_i) || '其他' : nil,
          identity_id: passenger[:cardNo]
        }
      end
      travellers.push(_pass)
      amount += item[:price].to_f * item[:quantity].to_f
    end
    meta[:travellers] = travellers.flatten.uniq

    contacts = data[:contacts] || []
    contactors = contacts.map do |contact|
      {
        name: contact[:name],
        mobile: contact[:mobile],
        email: contact[:email]
      }
    end
    meta[:contactor] = contactors.first || {}

    update_columns(amount: amount, meta: meta)
  end

  def change_identity_type
    {
      '身份证': 1,
      '护照': 2,
      '学生证': 3,
      '军官证': 4,
      '驾驶证': 6,
      '回乡证': 7,
      '台胞证': 8,
      '港澳通行证': 10,
      '其他': 99,
    }
  end
end
