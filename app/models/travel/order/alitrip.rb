class Travel::Order::Alitrip < Travel::Order
  before_create :reset_meta

  def ota_state_zh
    {
      TRADE_NO_CREATE_PAY: '没有创建支付宝交易',
      WAIT_BUYER_PAY: '等待买家付款',
      SELLER_CONSIGNED_PART: '卖家部分发货',
      WAIT_SELLER_SEND_GOODS: '等待卖家发货',
      WAIT_BUYER_CONFIRM_GOODS: '等待买家确认收货',
      TRADE_FINISHED: '交易成功',
      TRADE_CLOSED: '付款以后用户退款成功',
      TRADE_CLOSED_BY_TAOBAO: '付款以前，卖家或买家主动关闭交易',
      PAY_PENDING: '国际信用卡支付付款确认中',
    }.with_indifferent_access.dig(ota_state)
  end

  def change_identity_type
    {
      '身份证': 0,
      '护照': 1,
      '学生证': 2,
      '军官证': 3,
      '回乡证': 4,
      '台胞证': 5,
      '港澳通行证': 6,
      '警官证': 10,
      '士兵证': 11,
      '台湾通行证': 12,
    }
  end

  def reset_meta
    if model_detail.present?
      meta = {}
      sub_order = model_detail.with_indifferent_access

      # 同步sku信息
      buy_item_info = sub_order[:buy_item_info]
      if buy_item_info.present?
        sku_service_name = channel&.config&.dig('sku_service_name') || '出游人群'
        sku = buy_item_info[:sku_properties].to_s.split(';')
                  .map{|s| s.gsub('：', ':').split(':') }
                  .filter{ |a| a.length == 2 }
                  .to_h

        sku_meta = (buy_item_info[:sku_properties].to_s + ';').scan(/(.*?):(.*?);/)
                                                    .filter{|a| a.length == 2}
                                                    .to_h

        meta[:sku] =  {
            title: buy_item_info[:item_title],
            image_url: buy_item_info[:item_image] ?
                        File.join('https://img.alicdn.com/bao/uploaded', buy_item_info[:item_image]) :
                        nil,
            name: buy_item_info[:item_title],
            customer_type: sku['人群'],
            start_date: buy_item_info[:trip_start_date],
            end_date: buy_item_info[:trip_end_date],
            num: buy_item_info[:num],
            price: buy_item_info[:price].to_f / 100,
            meta: sku_meta,
            seq: buy_item_info[:sku_id],
            out_sku_id: buy_item_info[:out_sku_id] || buy_item_info[:sku_id],
            service_name: sku_meta[sku_service_name],
            url: "https://traveldetail.fliggy.com/item.htm?id=#{buy_item_info[:item_id]}",
          }
      end

      travellers = sub_order[:travellers]&.dig(:traveller_info) || []
      meta[:travellers] = travellers.map do |t|
        {
          name: t[:name],
          name_pinyin: t[:name_pinyin],
          email: t[:email],
          identity_type: change_identity_type.key(t[:credential_type]),
          identity_id: t[:credential_no],
          gender: t[:sex] ? t[:sex] == 0 ? '男' : '女' : '',
          birthday: t[:birthday],
          phone: t[:phone],
        }
      end

      # 同步联系人信息
      contactor_info = sub_order[:contactor] || {}
      meta[:contactor] = {
        name: contactor_info[:name],
        email: contactor_info[:email],
        phone: contactor_info[:phone],
      }
      self.meta = meta
    end
  end

  def reset_amount!
    if model_detail.present?
      data = model_detail.with_indifferent_access
      amount, total_amount = 0, 0

      data[:sub_orders][:sub_order_info].each do |sub_order|
        # 同步sku信息
        amount += sub_order[:payment].to_f
        total_amount += sub_order[:total_fee].to_f
      end
      update(amount: amount / 100, total_amount: total_amount / 100)
    end
  end
end
