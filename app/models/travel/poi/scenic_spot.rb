class Travel::Poi::ScenicSpot < Travel::Poi::Region
end

# a = File.read('public/印尼poi.txt')
# ary = a.split("}\n")
# country = Travel::Poi::Country.find_by(name: '印度尼西亚')
# ary.each do |line|
#   data = JSON.parse(line + '}')
#   p data['title']
#   scenic_spot = Travel::Poi::ScenicSpot.find_or_initialize_by(name: data['title'], country_id: country.id)
#   scenic_spot.en_name = data['sub_title']
#   scenic_spot.model_payload ||= {}
#   scenic_spot.model_payload['basic'] = data['basic_json_data']
#   scenic_spot.model_payload['image_urls'] = data['image_urls']
#   scenic_spot.meta = data['detail_json_data']
#   if data['image_urls']&.first
#     scenic_spot.cover_image = { files: [{ url: data['image_urls'].first, fileType: 'image' }] }
#   end
#   scenic_spot.save!
# end
