class Travel::Amount
  attr_accessor :value, :unit

  def initialize(value, unit, ratio = nil, rmb = nil)
    @value = value
    @unit = unit
    @ratio = ratio
    @rmb = rmb
  end

  def currency
    @currency ||= Travel::Currency.find_by_from_redis(unit: @unit)
  end

  def ratio
    @ratio || currency.try(:ratio) || 0
  end

  def rmb
    @rmb || (@value ? @value * ratio : nil)
  end

  def to_json(**_options)
    {
      value: @value,
      unit: @unit,
      symbol: currency.try(:symbol),
      ratio: ratio,
      rmb: rmb,
    }
  end
end
