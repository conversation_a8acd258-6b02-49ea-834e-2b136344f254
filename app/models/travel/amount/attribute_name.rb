class Travel::Amount::Attribute<PERSON>ame
  def initialize(column_name)
    @column_name = column_name
  end

  def column
    @column_name
  end

  def unit
    "#{@column_name}_unit"
  end

  def ratio
    "#{@column_name}_ratio"
  end

  def object
    "#{@column_name}_object"
  end

  def rmb
    "#{@column_name}_rmb"
  end

  def info
    "#{@column_name}_info"
  end

  def currency_name
    "#{@column_name}_currency_name"
  end

  def currency_id
    "#{@column_name}_currency_id"
  end
end
