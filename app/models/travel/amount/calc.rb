class Travel::Amount::<PERSON><PERSON>
  def initialize(column_name, association)
    @names = Travel::Amount::AttributeName.new(column_name)
    @association = association
  end

  # 已确定的人民币金额
  def sum_determined_rmb
    @association.sum(@names.rmb)
  end

  # 未确定的人民币金额
  def sum_shaky_rmb
    @association.where(@names.rmb => nil).sum do |record|
      Travel::Amount::Column.new(record, @names.column).amount_object.rmb || 0
    end
  end

  def sum_rmb
    sum_determined_rmb + sum_shaky_rmb
  end

  def sum_origin_objects
    # NOTE: has_many :offers, -> { distinct }, through: :demands 时，group(:amount_unit).sum(:amount) 会重复计算，distinct 无效

    @association.klass.where(id: @association.select(:id)).group(@names.unit).sum(@names.column).map do |unit, value|
      Travel::Amount.new(value, unit)
    end
  end

  # 原货币按币种合并后的信息，实时汇率，对象数组
  def sum_origin_info
    sum_origin_objects.map(&:to_json)
  end

  def self.join_info(*infos)
    currency_2_info_mapping = infos.reduce({}) do |out, info|
      out[info[:unit]] ||= {
        value: 0,
        rmb: 0,
        unit: info[:unit],
        symbol: info[:symbol],
      }

      out[info[:unit]][:value] += info[:value] || 0
      out[info[:unit]][:rmb] += info[:rmb] || 0
      out
    end

    currency_2_info_mapping.reduce([]) do |out, (currency, info)|
      out << info
      out
    end
  end
end
