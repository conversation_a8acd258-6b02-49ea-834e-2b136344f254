class Travel::DemandPreset < ApplicationRecord
  include Travel::Model::DemandPreset
  include TalltyImportExport::Importable

  Import.class_eval do
    def import_record(line_info, associations)
      app = associations.new.app
      demand_define = Travel::PresetDemandDefine.find_by(
        name: line_info[:demand_define_id],
        app: app
      )
      line_info[:demand_define_id] = demand_define&.id
      demand_preset = associations.find_or_initialize_by(
        name: line_info[:name],
        demand_define: demand_define
      )

      line_info[:offer_amount_currency_id] = Travel::Currency.find_by(
        name: line_info[:offer_amount_currency_id]
      )&.id
      # 供应商
      line_info[:supplier_id] = Travel::Supplier.find_by(
        app: app,
        name: line_info[:supplier_id]
      )&.id
      demand_preset.update(line_info)
    end
  end
end
