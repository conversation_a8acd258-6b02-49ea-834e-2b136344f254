module Travel::Model::ActivityTemp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    seqable

    # attribute :name_embedding, :vector, comment: '名称向量'
    has_neighbors :name_embedding

    # 需要提前定义，后面 ActsAsTemp 会用到
    has_many :temp_entities, class_name: 'Travel::Activity', foreign_key: :activity_temp_id

    include Travel::ActivityAttributes
    include Travel::ActsAsTemp

    belongs_to :app, class_name: '::App'
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :activity_temp_dir, class_name: 'Travel::ActivityTempDir', optional: true

    has_many :demand_grp_temps, class_name: 'Travel::DemandGrpTemp'
    has_many :demand_temps, class_name: 'Travel::DemandTemp'
    has_many :day_temps, class_name: 'Travel::DayTemp'
    has_many :offer_temps, class_name: 'Travel::OfferTemp'

    has_many :activities, class_name: 'Travel::Activity'
    has_many :ota_sku_origins, dependent: :destroy
    accepts_nested_attributes_for :ota_sku_origins, allow_destroy: true, reject_if: :all_blank
    attribute :state, :string, comment: '状态'

    attr_accessor :allow_create_day_temps, :import_demand_start_position, :import_demand_grp_id

    after_create :create_day_temps, unless: :allow_create_day_temps

    after_commit :generate_name_embedding, if: -> { new_record? || saved_change_to_name? }

    # 获取所有 OTA SKU 原始码
    def ota_sku_origin_codes
      ota_sku_origins.pluck(:code).compact.join(',')
    end

    def nested_temps
      demand_grp_temps + day_temps
    end

    def service_names
      demand_grp_temps.pluck(:service_name).uniq.compact
    end

    alias_method :old_generate_entity, :generate_entity

    def generate_entity(
      import_start_offset: 0,
      import_count: 1,
      import_service_name: nil,
      **attrs
    )
      self.class.transaction do
        old_generate_entity(
          import_start_offset: import_start_offset,
          import_count: import_count,
          import_service_name: import_service_name,
          **attrs
        ).tap do |new_activity|
          # 加入 offer 相关逻辑
          generate_offer_temp_into_activity(
            new_activity,
            new_activity.demands.reload,
            import_service_name: import_service_name,
          )
        end
      end
    end

    def generate_offer_temp_into_activity(activity, new_demands, import_service_name: nil)
      offer_temp_id_2_offer_id_mapping = offer_temps.reduce({}) do |out, offer_temp|
        demand_temp_ids = offer_temp.demand_temps.pluck(:id)

        next out unless new_demands.select { |demand| demand.demand_temp_id.in?(demand_temp_ids) }.any?

        new_offer = offer_temp.generate_entity(activity: activity)
        out[offer_temp.id] = new_offer.id
        out
      end

      new_demands.each do |new_demand|
        new_demand.update!(offer_id: offer_temp_id_2_offer_id_mapping[new_demand.demand_temp.offer_temp_id])
      end
    end

    def generate_into_activity(
      activity,
      import_start_offset: 0,
      import_count: 1,
      import_service_name: nil
    )
      self.class.transaction do
        # 需求组、需求、天
        sub_results = nested_temps.map do |nested_temp|
          nested_temp.generate_entity(
            temp_entity_parent: activity,
            import_start_offset: import_start_offset,
            import_count: import_count,
            import_service_name: import_service_name,
          )
        end
        # 结算单模板
        generate_offer_temp_into_activity(
          activity,
          sub_results.select { |r| r.is_a?(Travel::DemandGrp) }.map(&:demands).flatten,
          import_service_name: import_service_name,
        )
      end
    end

    alias_method :old_clone, :clone

    def clone(**attrs)
      new_activity_temp = old_clone(**attrs.merge(allow_create_day_temps: true), name: "复制 #{name}")
      clone_offer_temp_into_activity_temp(
        new_activity_temp,
        new_activity_temp.demand_temps
      )
      # 重置天数
      new_activity_temp.day_temps.each_with_index do |day_temp, index|
        day_temp.update_columns(position: index + 1)
      end
      new_activity_temp
    end

    def clone_offer_temp_into_activity_temp(activity_temp, new_demand_temps)
      offer_temp_id_2_new_offer_temp_id_mapping = offer_temps.reduce({}) do |out, offer_temp|
        new_offer_temp = offer_temp.clone(activity_temp: activity_temp)
        out[offer_temp.id] = new_offer_temp.id
        out
      end

      new_demand_temps.each do |new_demand_temp|
        new_demand_temp.update!(offer_temp_id: offer_temp_id_2_new_offer_temp_id_mapping[new_demand_temp.offer_temp_id])
      end
    end

    # 关联的私有库国家
    action_store(
      :relate,
      :app_country,
      class_name: 'Travel::Poi::AppCountry',
      alias_name: 'app_countries',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_activity_temps',
    )

    # 关联的城市
    action_store(
      :relate,
      :city,
      class_name: 'Travel::Poi::City',
      alias_name: 'cities',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_activity_temps',
    )

    # 关联的供应商
    action_store(
      :relate,
      :supplier,
      class_name: 'Travel::Supplier',
      alias_name: 'suppliers',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_activity_temps',
    )

    def create_day_temps
      duration.times { |i| day_temps.create!(position: i + 1) }
    end

    def set_duration_by_day_temps
      update_columns(duration: day_temps.count)
    end

    def demands_info
      {
        count: demand_temps.count,
        no_offer_count: demand_temps.where(offer_temp_id: nil).count,
      }
    end

    def generate_name_embedding
      return unless Rails.env.production?

      update!(name_embedding: Ai::OpenaiService.embeddings([name]).first)
    end

    def import_demand_preset_ids=(val)
      val.each do |demand_preset_id|
        demand_temps.create(
          demand_grp_temp_id: import_demand_grp_id,
          demand_preset_id: demand_preset_id,
          start_position: import_demand_start_position
        )
      end
    end
  end

  class_methods do
  end
end
