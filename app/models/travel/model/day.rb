module Travel::Model::Day
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    acts_as_list scope: %i[activity_id]

    belongs_to :temp_entity_parent, class_name: 'Travel::Activity', foreign_key: :activity_id

    include Travel::DayAttributes
    include Travel::ActsAsTempEntity

    # 导入模板时，支持偏移
    attr_accessor :import_start_offset

    def position=(val)
      super(val + import_start_offset.to_i)
    end

    # validates_uniqueness_of :position, scope: :activity_id

    belongs_to :activity, touch: true, class_name: 'Travel::Activity'
    belongs_to :day_temp, class_name: 'Travel::DayTemp', optional: true

    default_value_for(:app) { |o| o.activity&.app }

    attribute :date, :date, comment: '日期'

    before_save :init_date
    after_create :update_activity_duration
    after_destroy :update_activity_duration
    # after_save :move_one_day_demand, if: ->{ saved_change_to_position? }

    define_singleton_method(:decrement_all) do
      update_all_with_touch(
        "#{quoted_position_column} = (#{quoted_position_column_with_table_name} - 1)," +
        "date = travel_days.date - INTERVAL '1 day'"
      )
    end

    define_singleton_method(:increment_all) do
      update_all_with_touch(
        "#{quoted_position_column} = (#{quoted_position_column_with_table_name} + 1)," +
        "date = travel_days.date + INTERVAL '1 day'"
      )
    end

    include Travel::DayMoveSyncDemands

    define_sync_demands_methods(Travel::Demand) do |day|
      day&.activity&.demands || Travel::Demand.none
    end

    def demands_during_day
      activity.demands.effective(date)
    end

    def demand_suppliers_info
      demands_during_day.joins(:offer).group('travel_offers.supplier_id').count.each_with_object({}) do |(supplier_id, count), hash|
        key = supplier_id ? :supplier_exists : :supplier_null
        hash[key] ||= 0
        hash[key] += count
      end.merge(
        count: demands_during_day.count
      )
    end

    def init_date
      self.date = activity.start_at + (position - 1).days
    end

    def update_activity_duration
      activity.set_duration_by_days
    end
  end

  class_methods do
  end
end
