module Travel::Model::Demand
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :demand_grp, class_name: 'Travel::DemandGrp'
    belongs_to :temp_entity_parent, class_name: 'Travel::DemandGrp', foreign_key: :demand_grp_id, optional: true

    belongs_to :activity, class_name: 'Travel::Activity'
    belongs_to :product, optional: true
    belongs_to :demand_temp, class_name: 'Travel::DemandTemp', optional: true
    belongs_to :demand_preset, class_name: 'Travel::DemandPreset', optional: true
    belongs_to :offer, class_name: 'Travel::Offer', optional: true, touch: true
    has_one :payment, through: :offer
    has_many :serves, through: :offer
    has_many :orders, through: :activity, source: :orders

    include Travel::DemandAttributes
    attribute :state, :string, comment: '状态'

    delegate :name, to: :demand_grp, prefix: true, allow_nil: true

    effectable(effective_at: :start_date, invalid_at: :end_date, field_type: :date)

    default_value_for(:activity) { |o| o.demand_grp&.activity }
    default_value_for(:app) { |o| o.activity&.app || o.demand_preset&.app }
    default_value_for(:name) { |o| o.demand_preset&.name  }

    after_save :calc_date, if: -> { saved_change_to_start_offset? || saved_change_to_duration? }
    after_save :validate_payment_state
    before_destroy :validate_payment_state_when_destroy
    after_update :reset_activity_follow_state!, if: -> { saved_change_to_state? }
    after_save :set_desc_color, if: -> { saved_change_to_desc? }
    after_destroy ->{ activity&.reload&.touch }

    # 创建后判断有 demand_preset 则按照需求生成 demand_info
    after_create :generate_demand_info_by_preset!, if: :demand_preset

    after_commit -> { offer&.destroyed? || offer&.touch }, if: -> { saved_change_to_offer_id? }
    after_commit :update_offer_name, if: :new_offer_supplier_id

    scope :offer_null_or_eq, ->(offer_id) { where(offer_id: [nil, offer_id == 'noOffer' ? nil : offer_id]) }
    enum state: { todo: 'todo', followed: 'followed', done: 'done' }
    default_value_for(:state) { 'todo' }

    scope :during, ->(start_at, end_at) {
      where(
        'start_date BETWEEN ? AND ? OR end_date BETWEEN ? AND ? OR (start_date <= ? AND end_date >= ?)',
        start_at, end_at,
        start_at, end_at,
        start_at, end_at
      )
    }

    # 只 permit start_offset, duration
    def calc_date
      update_columns(
        start_date: activity.start_at + start_offset.days,
        end_date: activity.start_at + (start_offset + duration - 1).days
      )
    end

    include Travel::ActsAsTempEntity

    # 模板导入时支持偏移
    attr_accessor :import_start_offset, :current_user

    def start_offset=(val)
      super(val.to_i + import_start_offset.to_i)
    end

    ransackable_scope_defines.concat(%i[
      effective
      invalid
      effect_pending
      effect_expire
      offer_null_or_eq
    ])

    # 弃用
    def update_offer_state=(val)
      return unless val.in?(%w[pending confirmed terminated])

      offer&.update(state: val)
    end

    # 创建后判断有 demand_preset 则按照需求生成 demand_info
    def generate_demand_info_by_preset!
      clone_keys = demand_preset.class.clone_keys - [:start_offset, :end_offset]
      attr = demand_preset.attributes.symbolize_keys.slice(
        *clone_keys
      ).merge(new_offer_supplier_id: demand_preset.supplier_id, count: activity.item_num)
      ta_update(attributes: attr)
      # 重新计算日期
      calc_date
      # 重新计算偏移
      calc_offset
      # 重新创建 day
      reload
      activity.days.find_or_create_by(position: self.start_position)
    end

    # 需求导入到 demand_preset 中
    def import_to_demand_preset!
      attr = self.attributes.symbolize_keys.slice(
        *self.class.clone_keys
      ).merge(supplier_id: offer&.supplier_id, demand_define_id: demand_define_id)
      app.travel_demand_presets.create!(attr)
    end

    def set_desc_color
      return nil if current_user.blank?
      member_identity = current_user.member_identities.find{ |m| m.model_payload&.dig('color') }
      color = member_identity&.model_detail&.dig('color')
      update_columns(model_flag: color)
    end

    def process_new_offer_supplier_id
      old_offer = offer
      new_offer = nil

      if new_offer_supplier_id == 0 # 0 表示取消关联
        self.offer_id = nil
        old_offer&.destroy
      else
        # 不再查找已有的 offer，而是直接创建新的
        new_offer = app.travel_offers.create!(
          supplier_id: new_offer_supplier_id,
          amount_sync: true,
          amount_unit: offer_amount_unit,
          amount_ratio: offer_amount_ratio,
        )

        unless new_offer_supplier_id.in?(activity.supplier_ids)
          activity.supplier_ids = activity.supplier_ids + [new_offer_supplier_id]
        end

        if old_offer.present? && old_offer.id != new_offer&.id
          old_offer.destroy
        end
        self.update_columns(offer_id: new_offer.id)

        new_offer.sync_amount_by_demand_offer_amount
      end
    end

    def touch_offer
      offer&.touch
    end

    def update_offer_name
      offer.update_name(force: true) if offer.present?
    end

    def validate_payment_state
      if saved_change_to_offer_id? && payment.present? && payment.state.in?(%w[paid unpaid])
        raise ::Error::BaseError.new(message: "相关支付单已确认，需求【#{name}】不可再次关联")
      end
    end

    def validate_payment_state_when_destroy
      if payment.present? && payment.state.in?(%w[paid unpaid])
        raise ::Error::BaseError.new(message: "相关支付单已确认，需求【#{name}】不可删除")
      end
    end

    def reset_activity_follow_state!
      todo_demands_count = activity.demands.where(state: 'todo').count
      followed_demands_count = activity.demands.where(state: 'followed').count
      state = followed_demands_count > 0 ? 'followed' : 'done'
      activity.update(follow_state: todo_demands_count > 0 ? 'todo' : state)
    end

    # 需求日历需要
    def lite_activity
      return {} unless activity
      {
        name: activity.name,
        color: activity.app_countries.first&.color,
        contact_info: activity.contact_info,
        channel: activity.model_payload&.dig('来源')
      }
    end
  end

  class_methods do
  end
end
