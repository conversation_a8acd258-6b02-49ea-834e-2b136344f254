module Travel::Model::Notice
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    stiable

    belongs_to :app
    belongs_to :user, class_name: '::User', optional: true

    attribute :name,    :string, comment: '名称'
    attribute :content, :text, comment: '内容'
    attribute :state,   :string, comment: '状态'
    attribute :payload, :jsonb, comment: '其他字段'

    default_value_for(:app){ |o| o.user&.app }
  end
end