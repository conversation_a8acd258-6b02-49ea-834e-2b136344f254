module Travel::Model::SkuPrice
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    # association
    belongs_to :app
    belongs_to :product, optional: true
    belongs_to :sku

    class Option
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      class Price
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :price, :float
        attr_json :original_price, :float
        attr_json :flag, :string, default: 'adult'
      end

      attr_json :prices, Price.to_type, array: true, default: []
    end

    # attribute
    attribute :state,          :string,  comment: '状态'
    attribute :price,          :decimal, precision: 10, scale: 2, comment: '价格'
    attribute :original_price, :decimal, precision: 10, scale: 2, comment: '原价'
    attribute :mode,           :string,  comment: '模式'
    attribute :sale_date,      :date,    comment: '销售日期'
    attribute :option,         :jsonb,   comment: '价格配置'
    serialize :option, coder: Option.to_serialization_coder, default: {}
    attribute :payload,        :jsonb,   comment: '其他字段'

    default_value_for(:product) { |o| o.sku&.product }
    default_value_for(:app) { |o| o.sku&.app || o.product&.app }
  end
end
