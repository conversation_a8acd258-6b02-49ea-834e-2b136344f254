module Travel::Model::Destination
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable

    acts_as_list scope: [:app_id]

    include Favor::Ext::Markable
    acts_as_markable

    belongs_to :app
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :region,  class_name: 'Travel::Poi::Region',  optional: true
    belongs_to :country, class_name: 'Travel::Poi::Country', optional: true

    has_many :records, class_name: 'Travel::Record', as: :source

    has_event :hot

    # attribute
    attribute :name,        :string,  comment: '名称'
    attribute :state,       :string,  comment: '状态'
    attribute :desc,        :string,  comment: '描述'
    attribute :content,     :text,    comment: '内容'
    attribute :icon,        :string,  comment: 'icon'
    attribute :position,    :integer, comment: '排序'
    attribute :cover_image, :jsonb,   comment: '封面图'
    attribute :score,       :float,   comment: '评分'
    attribute :price,       :float,   comment: '价格'
    attribute :used_count,  :integer, comment: '旅行人数'
    attribute :views_count, :integer, comment: '浏览人数'
    attribute :stars_count, :integer, comment: '收藏人数'
    attribute :payload,     :jsonb,   comment: '其他字段'

    default_value_for(:app){ |o| o.creator&.app }
    default_value_for(:is_hotted) { false }

    action_store(
      :relate,
      :record,
      class_name: 'Travel::Record',
      action_class_name: 'Travel::RelationAction',
      alias_name: 'records',
      inverse_alias_name: 'destinations',
    )
  end
end