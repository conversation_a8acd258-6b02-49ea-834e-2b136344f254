module Travel::Model::Currency
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable

    attribute :name, :string, comment: '名称'
    attribute :unit, :string, comment: '单位'
    attribute :symbol, :string, comment: '符号'
    attribute :ratio, :decimal, scale: 5, precision: 20, comment: '汇率'

    validates_uniqueness_of :unit
    default_value_for(:symbol) { |o| o.unit }

    after_save do
      self.class.storage_to_redis
    end

    def self.redis_key
      'travel:currency'
    end

    def self.find_by_from_redis(unit:)
      unit ? new(JSON.parse($redis.hget(redis_key, unit) || '{}')) : nil
    end

    def self.storage_to_redis
      all.each do |currency|
        $redis.hset(redis_key, currency.unit, currency.to_json)
      end
    end
  end

  class_methods do
  end
end
