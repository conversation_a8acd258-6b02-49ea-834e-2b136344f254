module Travel::Model::App
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    has_many :travel_activities, class_name: 'Travel::Activity'
    has_many :travel_product_dirs, class_name: 'Travel::ProductDir'
    has_many :travel_products, class_name: 'Travel::Product'
    has_many :travel_product_days, class_name: 'Travel::ProductDay'
    has_many :travel_activity_temp_dirs, class_name: 'Travel::ActivityTempDir'
    has_many :travel_activity_temps, class_name: 'Travel::ActivityTemp'
    has_many :travel_demand_temps, class_name: 'Travel::DemandTemp'
    has_many :travel_day_temps, through: :travel_activity_temps, source: :day_temps
    has_many :travel_demand_grp_temps, through: :travel_activity_temps, source: :demand_grp_temps
    has_many :travel_demand_defines, class_name: 'Travel::DemandDefine'
    has_many :travel_demand_grps, class_name: 'Travel::DemandGrp'
    has_many :travel_demands, class_name: 'Travel::Demand'
    has_many :travel_orders, class_name: 'Travel::Order'
    has_many :travel_channels, class_name: 'Travel::Channel'
    has_many :travel_ota_skus, class_name: 'Travel::OtaSku'
    has_many :travel_days, class_name: 'Travel::Day'
    has_many :travel_poi_regions, class_name: 'Travel::Poi::Region'
    has_many :travel_poi_geos, class_name: 'Travel::Poi::Geo'
    has_many :travel_poi_app_countries, class_name: 'Travel::Poi::AppCountry', dependent: :destroy
    has_many :travel_poi_countries, class_name: 'Travel::Poi::Country', through: :travel_poi_app_countries, source: :country
    has_many :travel_poi_app_regions, class_name: 'Travel::Poi::Region', through: :travel_poi_countries, source: :regions
    has_many :travel_offers, class_name: 'Travel::Offer', dependent: :destroy
    has_many :travel_offer_temps, class_name: 'Travel::OfferTemp', dependent: :destroy
    has_many :travel_payments, class_name: 'Travel::Payment', dependent: :destroy
    has_many :travel_receipts, class_name: 'Travel::Receipt', dependent: :destroy
    has_many :travel_ota_products, class_name: 'Travel::OtaProduct', dependent: :destroy

    has_many :travel_demand_grp_presets, class_name: 'Travel::DemandGrpPreset', dependent: :destroy
    has_many :travel_demand_presets, class_name: 'Travel::DemandPreset', dependent: :destroy

    # 客户
    has_many :travel_customer_members, class_name: 'Travel::Customer'
    has_many :travel_customers, through: :travel_customer_members, source: :user

    # 供应商
    has_many :travel_suppliers, class_name: 'Travel::Supplier'
    has_many :travel_pay_acccounts, class_name: 'Travel::PayAccount'

    has_many :travel_destinations, class_name: 'Travel::Destination'
    has_many :travel_records, class_name: 'Travel::Record'

    has_many :travel_notices, class_name: 'Travel::Notice'
    has_many :travel_notice_grps, class_name: 'Travel::NoticeGrp'
  end

  class_methods do
  end
end
