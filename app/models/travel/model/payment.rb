module Travel::Model::Payment
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include State::Ext::Eventable
    acts_as_state

    seqable prefix: 'P'
    formable

    belongs_to :app
    belongs_to :supplier
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :pay_account, optional: true

    has_many :offers, class_name: 'Travel::Offer'
    has_many :activities,  ->{ distinct }, through: :offers, source: :activity
    has_many :demand_grps, ->{ distinct }, through: :offers, source: :demand_grp
    has_many :receipts, dependent: :destroy

    attribute :state, :string, comment: '状态'

    include Travel::HasAmount

    has_amount(:amount, '金额')

    enum state: {
      pending: 'pending',
      unpaid: 'unpaid',
      paid: 'paid',
    }

    default_value_for(:supplier) { |o| o.pay_account&.supplier || o.offers&.first&.supplier }
    default_value_for(:app) { |o| o.supplier&.app }
    default_value_for(:state) { 'pending' }

    after_save :storage_amount, if: -> { saved_change_to_state? && state == 'unpaid' }
    after_save :pay_offers, if: -> { saved_change_to_state? && state == 'paid' }

    def offers_amount_info
      Travel::Amount::Calc.new(:amount, offers).sum_origin_info
    end

    def storage_amount
      sum_objects = Travel::Amount::Calc.new(:amount, offers).sum_origin_objects

      if sum_objects.length > 1
        raise ::Error::BaseError.new(message: '结算单的币种不一致')
      end

      if sum_objects.length < 1
        raise ::Error::BaseError.new(message: '结算单的币种未确定')
      end

      # 保存支付单的币种与金额
      amount_object = sum_objects.first
      update!(amount: amount_object.value, amount_unit: amount_object.unit)

      amount_storage_into_rmb
    end

    def receipt_amount_info
      Travel::Amount::Calc.new(:amount, receipts).sum_origin_info
    end

    def remain_amount_info
      receipt_amount = Travel::Amount::Calc.new(:amount, receipts).sum_origin_objects&.first

      if receipt_amount.present? && receipt_amount.unit == amount_unit && receipt_amount.value.present? &&
          amount.present? && amount_unit.present?
        Travel::Amount.new(amount - receipt_amount.value, amount_unit)
      else
        Travel::Amount.new(amount, amount_unit)
      end
    end

    def pay_offers
      offers.each(&:pay!)
    end

    def eventable_user
      creator
    end
  end

  class_methods do
  end
end
