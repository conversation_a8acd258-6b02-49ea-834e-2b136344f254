module Travel::Model::Receipt
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    seqable
    formable

    belongs_to :app
    belongs_to :payment
    belongs_to :creator, class_name: '::User', optional: true

    include Travel::HasAmount
    has_amount(:amount, '金额')

    attribute :content, :text, comment: '内容'
    attribute :attachments, :jsonb, comment: '附件'

    default_value_for(:app) { |o| o.payment&.app }
  end

  class_methods do
  end
end
