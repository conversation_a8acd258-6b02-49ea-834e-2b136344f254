module Travel::Model::User
  extend ActiveSupport::Concern

  included do
    action_store_by(
      :relate,
      :customer,
      class_name: 'Travel::Order',
      action_class_name: 'Travel::CustomerAction',
      alias_name: 'customers',
      inverse_alias_name: 'travel_orders',
    )

    has_many :travel_activities, through: :travel_orders, source: :activities

    after_create :generate_customer_member!, if: :require_generate_customer_member?

    def travel_info
      {
        orders_count: travel_orders.count,
        activities_count: travel_activities.count,
        order_amount: travel_orders.sum(:income_amount),
      }
    end

    def generate_customer_member!
      member_identity = app.member_identities.find_by(member_type: 'Travel::Customer')
      Travel::Customer.find_or_create_by(user: self, member_identity: member_identity) if member_identity
    end

    def require_generate_customer_member?
      model_flag == 'travel'
    end
  end


  class_methods do
  end
end
