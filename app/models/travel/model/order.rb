module Travel::Model::Order
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Travel::Model::Ai::Order
    include Travel::Ext::RevenueSource

    formable
    seqable
    stiable

    action_store(
      :relate,
      :customer,
      class_name: '::User',
      action_class_name: 'Travel::CustomerAction',
      alias_name: 'customers',
      inverse_alias_name: 'travel_orders',
    )

    action_store(
      :mark,
      :maven,
      class_name: '::User',
      action_class_name: 'Travel::CustomerAction',
      alias_name: 'mavens',
      inverse_alias_name: 'travel_maven_orders',
    )

    action_store(
      :relate,
      :country,
      class_name: 'Travel::Poi::AppCountry',
      action_class_name: 'Travel::RelationAction',
      alias_name: 'countries',
      inverse_alias_name: 'orders',
    )

    action_store_by(
      :relate,
      :order,
      class_name: 'Travel::Activity',
      alias_name: 'orders',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'activities',
    )

    has_paper_trail_version(
      only: [
        :seq, :state, :traveler_num, :trip_start_date, :trip_end_date,
        :trip_days, :item_num, :remark, :is_invoiced,
        :order_amount, :plus_amount, :discount_amount, :income_amount,
      ],
      meta: {
        operator: proc { |o| o.creator  } # a Proc
      }
    )

    belongs_to :app, class_name: '::App'
    belongs_to :channel, class_name: 'Travel::Channel', optional: true
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :product, optional: true
    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp', optional: true
    belongs_to :ota_sku, optional: true
    belongs_to :ota_product, optional: true
    belongs_to :sub_order, class_name: 'Travel::Order', optional: true
    belongs_to :sku, optional: true

    has_many :order_divides, class_name: 'Travel::OrderDivide'
    has_many :sale_users, through: :order_divides, source: :user
    has_many :ota_snapshots
    has_many :ota_skus, through: :ota_snapshots, source: :ota_sku
    has_many :ota_payments

    has_many :manual_revenues, class_name: 'Travel::ManualRevenue', as: :source
    has_many :pay_revenues, class_name: 'Travel::PayRevenue', as: :source

    accepts_nested_attributes_for :manual_revenues, reject_if: :all_blank, allow_destroy: true
    accepts_nested_attributes_for :order_divides, reject_if: :all_blank, allow_destroy: true

    default_value_for(:type) { 'Travel::Order' }
    default_value_for(:app) { |o| o.channel&.app || o.creator&.app }
    default_value_for(:product) { |o| o.ota_sku&.product }

    class Meta
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      class Product
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :title,       :string
        attr_json :amount,      :float
        attr_json :meta, ActiveModel::Type::Value.new
      end

      class Sku
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :title,     :string  # 名称
        attr_json :image_url, :string  # 图片
        attr_json :seq,       :string  # sku序列号
        attr_json :out_sku_id,       :string
        attr_json :name,            :string # 名称
        attr_json :customer_type,   :string # 针对人群
        attr_json :start_date,      :string # 出行开始日期
        attr_json :end_date,        :string # 出行结束日期
        attr_json :price,           :float  # 价格
        attr_json :num,             :float  # 数量
        attr_json :url,             :string # 链接
        attr_json :service_name,    :string # 出行人群
        attr_json :meta, ActiveModel::Type::Value.new, default: {}
      end

      class Customer
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :name,          :string  # 姓名
        attr_json :name_pinyin,   :string
        attr_json :gender,        :string  # 性别
        attr_json :birthday,      :string
        attr_json :phone,         :string  # 电话
        attr_json :email,         :string  # 邮箱
        attr_json :identity_type, :string  # 证件类型
        attr_json :identity_id,   :string  # 证件号码
      end

      attr_json :sku,        Sku.to_type, default: {}
      attr_json :contactor,  Customer.to_type, default: {} # 联系人
      attr_json :travellers, Customer.to_type, array: true, default: []
    end

    # 相关属性定义
    attribute :name, :string, comment: '订单名称'
    attribute :ota_no, :string, comment: 'OTA 订单号'
    attribute :ota_state, :string, comment: 'OTA 订单状态'
    attribute :state, :string, comment: '状态'
    attribute :traveler_num, :integer, comment: '出行人数'
    attribute :trip_start_date, :date, comment: '出行日期'
    attribute :trip_end_date, :date, comment: '返程日期'
    attribute :trip_days, :integer, comment: '出行天数'
    attribute :item_num, :string, comment: '购买数量'
    attribute :remark, :text, comment: '备注'
    attribute :attachments, :jsonb, comment: '附件'
    attribute :payload, :jsonb, comment: '信息存储'
    attribute :is_invoiced, :boolean, comment: '是否开票'
    attribute :origin, :string, comment: '渠道来源，区别于channel（订单来源）'
    attribute :sku_no, :string, comment: '产品编号'
    attribute :order_type, :string, comment: '订单类型'

    attribute :channel_payload, :jsonb, comment: 'channel 拓展字段表单存储的值'

    attribute :meta, :jsonb, comment: '元数据'
    serialize :meta, coder: Meta.to_serialization_coder, default: {}

    enum state: {
      pending: 'pending', # 待付款
      todo: 'todo', # 未预约
      processing: 'processing', # 进行中
      finished: 'finished',   # 已完成
      terminated: 'terminated', # 已终止
    }

    enum order_type: { normal: 'normal', sub: 'sub', internal: 'internal' }

    before_save :calc_trip_days, if: -> { new_record? || will_save_change_to_trip_start_date? || will_save_change_to_trip_end_date? }

    # after_update :generate_activity_by_activity_temp!, if: -> { saved_change_to_activity_temp_id? }
    after_create :async_product_countries!, if: :require_async_product_countries?
    after_save :generate_activity_by_activity_temp_if_not_exists

    after_save ->{ activities.each(&:touch) }
    after_destroy ->{ activities.each(&:touch) }

    after_commit :reset_order_state, on: :create
    after_commit :async_activity_travellers!, on: [:create, :update]

    # after_create :import_product_to_activties, if: :product
    after_create :async_fee_amount!

    default_value_for(:name) { |o| o.product&.name || o.meta&.sku&.name }
    default_value_for(:is_invoiced) { false }
    default_value_for(:state) { 'pending' }
    default_value_for(:order_type) { 'normal' }
    default_value_for(:item_num) { 1 }

    attr_accessor :import_start_position,
      # 模板导入时的数量（倍率）
      :import_count

    def async_product_countries!
      self.country_ids = product.country_ids
    end

    def ota_state_zh
    end

    # 设施手续费
    def async_fee_amount!
      sku = ota_sku || sku
      if sku && sku.rate.to_f > 0
        seq = "FEE#{seq}"
        revenue = revenues.find_or_initialize_by(type: 'Travel::OtaRevenue', seq: seq, app: app)
        revenue.update(amount: order_amount.to_f * sku.rate.to_f / 100.0, mode: 'fee')
      end
    end

    def customer_ids=(val)
      super
      async_activity_travellers!
    end

    def customers_name
      customers.pluck(:name).compact.join(',')
    end

    def customers_pinyin
      customers.pluck(:pinyin).compact.join(',')
    end

    def reset_order_state
      if pending? && manual_revenues.count > 0
        self.update_columns(state: 'processing')
      end
    end

    # 同步更新行程出行人信息
    def async_activity_travellers!
      activities.each{ |activity| activity.async_order_customers! }
    end

    # 同步二次预约单
    def sync_order(seq: nil)
      Alitrip::Helper::Order.async_record!(
        body: { order_id: seq.to_i },
        channel: channel
      )
      order = Travel::Order.find_by(seq: seq)
      order.update(sub_order: self)
      order.customer_ids = self.customer_ids if order.customers.count < 0
      order
    rescue => e
      raise Error::BaseError.new(message: '同步订单失败，请检查订单号是否正确')
    end

    # 关联的行程模版变更时候，已有行程删除
    def generate_activity_by_activity_temp!
      return unless activity_temp
      activities.find_each{ |activity| activity.destroy }
    end

    # 销售
    def sale_user_names
      sale_users.pluck(:name) * '、'
    end

    # 状态
    def state_zh
      case state
        when 'pending'
          '待付款'
        when 'todo'
          '未预约'
        when 'processing'
          '进行中'
        when 'finished'
          '已完成'
        when 'terminated'
          '已终止'
        else
          '未知状态'
      end
    end

    private

    def require_async_product_countries?
      product && countries.count < 1
    end

    def calc_trip_days
      return unless trip_start_date.present? && trip_end_date.present?

      self.trip_days = (trip_end_date - trip_start_date).to_i + 1
    end
    # 不在根据sku生成行程
    def generate_activity_by_activity_temp_if_not_exists
      # 行程模版变更时，删除已有的行程
      if terminated? || saved_change_to_activity_temp_id?
        activities.find_each{ |activity| activity.destroy if activity.orders.count < 2 }
      end

      reload

      unless activity_temp
        ota_sku_origin = Travel::OtaSkuOrigin.find_by(channel_id: channel_id, code: self.sku_no)
        self.activity_temp_id = ota_sku_origin&.activity_temp_id
      end

      return if activities.present? || !activity_temp || terminated? || sub?

      new_activity = activity_temp.generate_entity(
        **{
          import_start_offset: import_start_position&.-(1),
          import_count: import_count,
          import_service_name: ota_sku_origin&.name,
          start_at: trip_start_date || Date.today,
          duration: trip_days,
          title: activity_temp.name,
          contact_info: {
            name: meta&.contactor&.name,
            mobile: meta&.contactor&.phone,
            email: meta&.contactor&.email,
          }
        }.compact
      )
      new_activity.new_link_order_id = id
    end

    # def import_product_to_activties
      # activities.each do |activity|
      #   activity.update!(import_product_ids: [product_id])
      # end
    # end
  end

  class_methods do
  end
end
