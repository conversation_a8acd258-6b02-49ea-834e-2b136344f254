module Travel::Model::DemandDefine
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable

    belongs_to :app
    has_many :demands, class_name: 'Travel::Demand'
    has_many :demand_presets, class_name: 'Travel::DemandPreset', dependent: :nullify
    has_many :demand_temps, dependent: :nullify

    formable

    attribute :name, :string, comment: '名称'
    attribute :cover_image, :jsonb, comment: '图标'
    attribute :form, :jsonb, comment: '表单'
    serialize :form, coder: Forms::Attr::Item.to_serialization_coder

    def demand_presets_count
      demand_presets.count
    end
  end
end
