module Travel::Model::Sku
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable
    seqable

    include Notify::Ext::Commentable

    has_event :recommend

    belongs_to :app
    belongs_to :product, optional: true
    belongs_to :creator, optional: true, class_name: '::User'

    has_many :sku_prices, class_name: 'Travel::SkuPrice', dependent: :destroy
    accepts_nested_attributes_for :sku_prices, allow_destroy: true, reject_if: :all_blank

    attribute :name,              :string,    comment: '商品名称'
    attribute :state,             :string,    comment: '商品状态'
    attribute :content,           :jsonb,     comment: '商品内容'
    attribute :cover_image,       :jsonb,     comment: '商品图片(轮播图)'
    attribute :price,             :float,     comment: '商品价格'
    attribute :origin_price,      :float,     comment: '原始价格'
    attribute :position,          :integer,   comment: '商品排序'
    attribute :total_number,      :float,     comment: '库存数量'
    attribute :freeze_number,     :float,     comment: '冻结数量'
    attribute :sale_count,        :integer,   comment: '销售数量'
    attribute :stars_count,       :integer,   comment: '收藏数量'
    attribute :score,             :float,     comment: '评分'
    attribute :origin_score,      :float,     comment: '原始评分'
    attribute :origin_sale_count, :integer,   comment: '原始销售数量'
    attribute :payload,           :jsonb,     comment: '扩展信息'
    attribute :rate,              :float,     comment: '手续费率'

    delegate :name, :model_payload, to: :product, prefix: true

    default_value_for(:rate) { 0 }
  end
end
