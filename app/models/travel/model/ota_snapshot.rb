module Travel::Model::OtaSnapshot
  # OTA 平台产品的 sku
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    stiable

    belongs_to :ota_sku, class_name: 'Travel::OtaSku'
    belongs_to :order, class_name: 'Travel::Order'

    attribute :name, :string, comment: '名称'
    attribute :amount, :decimal, scale: 5, precision: 20, comment: '金额'
    attribute :count, :integer, comment: '数量'
    attribute :meta, :jsonb, comment: '元数据'

    delegate :ota_type, :ota_adapter, to: :ota_sku
  end

  class_methods do
  end
end
