module Travel::Model::OfferTemp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :product, class_name: 'Travel::Product', optional: true
    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp', optional: true
    belongs_to :temp_parent, class_name: 'Travel::ActivityTemp', foreign_key: :activity_temp_id

    has_many :demand_temps, class_name: 'Travel::DemandTemp'
    has_many :demand_grp_temps, -> { distinct }, through: :demand_temps

    has_one :demand_temp, class_name: 'Travel::DemandTemp'
    has_one :demand_grp_temp, through: :demand_temp

    has_many :offers, class_name: 'Travel::Offer', foreign_key: :offer_temp_id
    has_many :temp_entities, class_name: 'Travel::Offer', foreign_key: :offer_temp_id

    include Travel::OfferAttributes
    include Travel::ActsAsTemp

    default_value_for(:app) { |o| o.supplier&.app || o.product&.app || o.activity_temp&.app }
    default_value_for(:name) { |o| o.generate_name }

    def demand_relation
      demand_temps.where(id: demand_temp&.id)
    end

    def generate_name
      [
        supplier&.name,
        demand_grp_temps.map(&:name).join(' '),
      ].join(' ')
    end

    # 成本金额
    def demands_offer_amount_calc_object
      Travel::Amount::Calc.new(:offer_amount, demand_temps)
    end

    def update_name(force: false)
      return update!(name: generate_name) if force

      self.name ||= generate_name
      save!
    end
  end

  class_methods do
  end
end
