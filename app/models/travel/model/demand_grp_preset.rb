module Travel::Model::DemandGrpPreset
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app

    has_many :demand_presets, dependent: :nullify

    # attribute
    attribute :name, :string, comment: '名称'
    attribute :desc, :text,   comment: '描述'
    attribute :state, :string, comment: '状态'
    attribute :form, :jsonb, comment: '表单'
    attribute :payload, :jsonb, comment: '其他字段'

    def demand_presets_count
      demand_presets.count
    end
  end
end