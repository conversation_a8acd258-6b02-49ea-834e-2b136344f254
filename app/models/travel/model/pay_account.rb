module Travel::Model::PayAccount
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    formable
    stiable

    belongs_to :app
    belongs_to :supplier

    has_many :payments, dependent: :nullify
    has_many :offers, through: :payments

    attribute :name,         :string, comment: '账户名称'
    attribute :state,        :string, comment: '状态'
    attribute :account_type, :string, comment: '账户类型'
    attribute :payload,      :jsonb,  comment: '字段'

    default_value_for(:app){ |o| o.supplier&.app }

    def account_info
      {
        payments_count: payments.count,
        # offers_count: offers.count,
      }
    end
  end
end