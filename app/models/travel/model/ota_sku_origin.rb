module Travel::Model::OtaSkuOrigin
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app, optional: true
    belongs_to :channel,       optional: true
    belongs_to :activity_temp, optional: true

    attribute :name,  :string, comment: '名称'
    attribute :code,  :string, comment: '编码'
    attribute :state, :string, comment: '状态'
    attribute :from,  :string, comment: '来源'
    attribute :payload,  :jsonb, comment: '其他字段'

    default_value_for(:app){ |o| o.channel&.app || o.activity_temp&.app }

    after_commit :reset_ota_sku_activity_temp!, on: :create

    # 创建 ota_sku_origin 后，重置 ota_sku 的 activity_temp
    def reset_ota_sku_activity_temp!
      ota_sku = Travel::OtaSku.find_by(ota_sku_no: code, channel: channel)
      ota_sku&.update(activity_temp: activity_temp) unless ota_sku&.activity_temp_id == activity_temp_id
    end
  end

  class_methods do
  end
end