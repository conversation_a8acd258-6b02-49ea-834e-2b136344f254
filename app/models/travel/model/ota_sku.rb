module Travel::Model::OtaSku
  # OTA 平台产品的 sku
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    stiable

    class Meta
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      class Product
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :name,  :string
        attr_json :seq,   :string
        attr_json :price, :float
      end

      class Service
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :price, :float
        attr_json :name,  :string
        attr_json :meta,  ActiveModel::Type::Value.new, default: {}
      end

      attr_json :name,      :string
      attr_json :price,     :float
      attr_json :seq,       :string
      attr_json :image_url, :string # 图片链接
      attr_json :url,       :string # 跳转链接
      attr_json :product,   Product.to_type, default: {}
      attr_json :meta,      ActiveModel::Type::Value.new, default: {}
      attr_json :services,  Service.to_type, array: true, default: []
    end

    # association
    belongs_to :app, class_name: '::App'
    belongs_to :channel, class_name: 'Travel::Channel'
    belongs_to :product, class_name: 'Travel::Product', optional: true
    belongs_to :ota_product, class_name: 'Travel::OtaProduct', optional: true
    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp', optional: true

    has_many :ota_snapshots, class_name: 'Travel::OtaSnapshot'

    # attribute
    attribute :name,           :string, comment: '名称'
    attribute :uid,            :string, comment: '套餐'
    attribute :ota_product_no, :string, comment: 'OTA 商品 ID'
    attribute :ota_sku_no,     :string, comment: 'OTA sku ID'
    attribute :meta,           :jsonb, comment: '元数据'
    attribute :rate,           :float, comment: '手续费率'
    serialize :meta, coder: Meta.to_serialization_coder, default: {}

    default_value_for(:app) { |o| o.channel&.app }
    default_value_for(:rate) { 0 }

    after_update :reset_activity_temp!, if: -> { saved_change_to_activity_temp_id? }

    def reset_activity_temp!
      return nil unless activity_temp
      ota_sku_origin = Travel::OtaSkuOrigin.find_or_initialize_by(code: ota_sku_no, channel: channel)
      ota_sku_origin&.update(activity_temp: activity_temp)
    end
  end

  class_methods do
  end
end
