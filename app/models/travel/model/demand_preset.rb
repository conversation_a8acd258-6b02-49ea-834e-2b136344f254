module Travel::Model::DemandPreset
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :demand_grp_preset, optional: true
    belongs_to :demand_define, optional: true
    belongs_to :supplier, optional: true

    include Travel::DemandAttributes
    default_value_for(:app){ |o| o.demand_define&.app }

    def touch_offer
    end
  end
end