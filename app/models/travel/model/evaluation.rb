module Travel::Model::Evaluation
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    stiable

    belongs_to :app
    belongs_to :user, class_name: '::User' # 提交人
    belongs_to :source, polymorphic: true, optional: true

    attribute :name, :string, comment: '评分项名称'
    attribute :score, :float, comment: '分值'
    attribute :options, :jsonb, comment: '评论的内容结构'

    default_value_for(:app) { |o| o.creator&.app }
  end
end
