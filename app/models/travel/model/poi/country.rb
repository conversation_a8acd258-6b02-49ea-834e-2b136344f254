module Travel::Model::Poi::Country
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable

    belongs_to :geo, class_name: 'Travel::Poi::Geo', optional: true

    has_many :app_countries, dependent: :destroy
    has_many :regions, dependent: :destroy

    attribute :name, :string, comment: '名称'
    attribute :en_name, :string, comment: '英文名称'
    attribute :continent, :string, comment: '大洲'
    attribute :detail, :jsonb, comment: '详情'
    attribute :cover_image, :jsonb, comment: '封面图'
    attribute :meta, :jsonb, comment: '元数据'
  end

  class_methods do
  end
end

# region_trans = {
#   'Africa' => '非洲',
#   'Americas' => '美洲',
#   'Asia' => '亚洲',
#   'Europe' => '欧洲',
#   'Oceania' => '大洋洲',
#   'Polar' => '南极洲',
# }

# a = YAML.load(File.read('public/countries+states+cities.yml'))
# a['country_state_city'].each do |c_json|
#   country = Travel::Poi::Country.find_or_initialize_by(en_name: c_json['name'])
#   country.model_payload ||= {}
#   country.meta = c_json.except('states')
#   country.name = c_json.dig('translations')&.dig('cn') || c_json['name']
#   country.continent = region_trans[c_json['region']] || c_json['region']

#   country.save!

#   c_json['states'].each do |s_json|
#     state = country.regions.find_or_initialize_by(en_name: s_json['name'])
#     state.model_payload ||= {}
#     state.meta = s_json.except('cities') || s_json['name']
#     state.name = s_json.dig('translations')&.dig('cn')
#     state.country = country
#     state.save!

#     s_json['cities'].each do |city_json|
#       city = state.children.find_or_initialize_by(en_name: city_json['name'])
#       city.model_payload ||= {}
#       city.meta = city_json
#       city.name = city_json.dig('translations')&.dig('cn') || city_json['name']
#       city.country = country
#       city.parent = state
#       p city
#       city.save!
#     end
#   end
# end
