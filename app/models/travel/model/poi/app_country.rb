module Travel::Model::Poi::AppCountry
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    acts_as_list scope: %i[app_id]

    include Favor::Ext::Markable
    acts_as_markable(require_folder: false)

    has_event :hot

    belongs_to :app
    belongs_to :country

    has_many :products, dependent: :nullify

    attribute :name, :string, comment: '国家名称'
    attribute :color, :string, comment: '颜色'
    attribute :position, :integer, comment: '位置'
    attribute :conf, :jsonb, comment: '其他配置'

    attribute :state,       :string,  comment: '状态'
    attribute :content,     :jsonb,   comment: '内容'
    attribute :icon,        :string,  comment: 'icon'
    attribute :cover_image, :jsonb,   comment: '封面图'
    attribute :score,       :float,   comment: '评分'
    attribute :used_count,  :integer, comment: '旅行人数'
    attribute :views_count, :integer, comment: '浏览人数'
    attribute :stars_count, :integer, comment: '收藏人数'
    attribute :payload,     :jsonb,   comment: '其他字段'

    validates_uniqueness_of :country_id, scope: :app_id

    default_value_for(:name) { |o| o.country&.name }

    # 最便宜的产品
    def cheapest_product
      products.onsale.order(price: :asc, position: :asc).first
    end
  end

  class_methods do
  end
end
