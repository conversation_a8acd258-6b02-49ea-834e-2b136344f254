module Travel::Model::Poi::Region
  extend ActiveSupport::Concern

  included do
    include Travel::Poi::Extrable

    self.track_migration = true

    closure_treeable order: 'position'

    stiable
    formable

    has_event :recommend

    # 不存在app，说明是公共库
    # 存在app，则是私有库
    belongs_to :app, class_name: '::App', optional: true

    # 所属国家，region都是在一个国家下面
    belongs_to :country, optional: true
    belongs_to :creator, class_name: '::User', optional: true

    attribute :name, :string, comment: '名称'
    attribute :en_name, :string, comment: '英文名称'
    attribute :address, :string, comment: '地址'
    attribute :detail, :jsonb, comment: '详情'
    attribute :cover_image, :jsonb, comment: '封面图'
    attribute :position, :integer, comment: '排序'
    attribute :longitude, :float, comment: '经度'
    attribute :latitude, :float, comment: '纬度'
    attribute :meta, :jsonb, comment: '元数据'

    default_value_for(:country) { |o| o.parent&.country }
  end

  class_methods do
  end
end
