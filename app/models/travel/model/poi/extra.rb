module Travel::Model::Poi::Extra
  # 对每个 app 的 poi 额外信息
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable

    belongs_to :app, class_name: '::App'
    # belongs_to :org, class_name: '::Org', optional: true
    belongs_to :source, polymorphic: true

    # 属性和poi region保持同步
    attribute :name, :string, comment: '名称'
    attribute :detail, :jsonb, comment: '详情'
  end

  class_methods do
  end
end
