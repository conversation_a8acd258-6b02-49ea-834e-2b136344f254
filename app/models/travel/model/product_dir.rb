module Travel::Model::ProductDir
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable

    belongs_to :app, class_name: '::App'
    has_many :products, class_name: 'Travel::Product'

    attribute :name, :string, comment: '名称'
    attribute :desc, :string, comment: '描述'

    before_destroy :product_count_should_be_zero

    def product_count
      products.count
    end

    private

    def product_count_should_be_zero
      return if product_count.zero?

      raise Errors::BaseError(message: '产品目录下还有产品，请先删除产品')
    end
  end

  class_methods do
  end
end
