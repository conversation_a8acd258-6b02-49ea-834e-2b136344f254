module Travel::Model::Ai::Activity
  extend ActiveSupport::Concern

  included do
    def self.fill_create_form(text, app: App.first)
      prompt = Travel::Model::Ai::Activity::FILL_CREATE_FORM_PROMPT.gsub(
        '{{today}}', Date.today.strftime('%F')
      ).gsub(
        '{{countries}}', app.travel_poi_app_countries.pluck(:name).compact.join('、')
      )

      body = Ai::OpenaiService.completions({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: text },
        ],
        response_format: { type: 'json_object' },
      })

      json = JSON.parse(body)

      matched_orders = []
      user_orders = []

      if json.dig('order_seqs').present? && json.dig('order_seqs').is_a?(Array)
        matched_orders = app.travel_orders.ransack(
          seq_or_ota_no_in: json.dig('order_seqs')
        ).result
      end

      if json.dig('contract_phone').present?
        user = app.users.find_by(account: json.dig('contract_phone'))
        if user.present?
          user.travel_orders.each do |order|
            user_orders << order unless order.activities.any?
          end
        end
      end

      # 取出订单中的最早出发日期和最大行程天数

      # order_first_start_date = nil
      # order_max_duration = nil

      # (matched_orders + user_orders).each do |order|
      #   start_at = order.trip_start_date
      #   end_at = order.trip_end_date

      #   if start_at.present?
      #     need_set = order_first_start_date.nil? || Date.prase(start_at) < Date.prase(order_first_start_date)
      #     order_first_start_date = start_at if need_set
      #   end

      #   if start_at.present? && end_at.present?
      #     duration = Date.parse(start_at) - Date.parse(end_at)
      #     order_max_duration = duration if order_max_duration.nil? || duration > order_max_duration
      #   end
      # end

      {
        title: json.dig('title'),
        start_at: json.dig('start_at') || Date.today.strftime('%F'),
        duration: json.dig('duration'),
        app_country_ids: app.travel_poi_app_countries.where(name: json.dig('countries')).pluck(:id),
        matched_orders: matched_orders.map do |o|
          {
            id: o.id,
            name: get_title(o.activity_temp&.name || o.name, order: o),
            channel_name: o.channel&.name,
            seq: o.seq,
            ota_no: o.ota_no,
            activity_temp_id: o.activity_temp_id,
            # meta: o.meta
            start_at: o.trip_start_date,
            end_at: o.trip_end_date,
          }
        end,
        user_orders: user_orders.map do |o|
          {
            id: o.id,
            name: get_title(o.activity_temp&.name || o.name, order: o),
            seq: o.seq,
            channel_name: o.channel&.name,
            ota_no: o.ota_no,
            activity_temp_id: o.activity_temp_id,
            # meta: o.meta
            start_at: o.trip_start_date,
            end_at: o.trip_end_date,
          }
        end,
        desc: json.dig('remark'),
        model_payload: {
          费用包含: json.dig('cost_includes'),
          费用不含: json.dig('cost_excludes'),
        }
      }
    end

    def self.fill_link_create_form(text, order_id: , app: App.first)
      order = app.travel_orders.find(order_id)
      result = {
        start_at: order.trip_start_date,
        duration: order.trip_start_date && order.trip_end_date ? (order.trip_end_date - order.trip_start_date).to_i + 1 : nil,
        import_activity_temp_id: order.activity_temp_id,
      }


      if (sku = order.ota_sku)
        result[:title] = get_title(sku.name, order: order)

        if (temp = sku.activity_temp)
          result[:title] = get_title(temp.name, order: order)
          result[:duration] ||= temp.duration
          result[:import_activity_temp_id] = temp.id
        end
      end

      if (text.present?)
        result.merge!(
          self.ai_fill_link_create_form(text, order_id: order_id, app: app)
        )
      end

      result
    end

    def self.ai_fill_link_create_form(text, order_id: , app: App.first)
      {}
    end

    def self.get_title(text, order:)
      [
        text,
        order.traveler_num ? "(#{order.traveler_num}人)" : nil,
        order.model_detail&.dig('contactor', 'name')
      ].compact.join('')
    end
  end

  FILL_CREATE_FORM_PROMPT = <<-PROMPT
    用户将提供一段行程描述，请提取出以下信息，按 JSON 格式返回，今天是{{today}}，没有相关属性时，返回 null。
    1. 行程名称(title)
    2. 出发日期(start_at)
    3. 行程天数(duration)
    4. 出发国家(countries)，可选值为{{countries}}
    5. 订单编号数组(order_seqs)，没有提到时返回空数组。样例数据：8411202409051831、4026088261316626118。
    6. 描述中提到的手机号(contract_phone)
    7. 描述中的"费用包含"部分(cost_includes)，字符串描述格式按照
      ```
      1.交通
      ...
      2.住宿
      ...
      3.用餐
      ...
      4.其他
      ...
      ```
    8. 描述中的"费用不含"部分(cost_excludes)，字符串描述，格式按照
      ```
      1.交通
      ...
      2.住宿
      ...
      3.用餐
      ...
      4.其他
      ...
      ```
    9. 描述总结，除去费用包含、费用不含的信息，作为"行程备注"部分(remark)，字符串描述。
  PROMPT
end
