module Travel::Model::Ai::Order
  extend ActiveSupport::Concern

  included do
    def self.fill_create_form(text, app: App.first)
      prompt = Travel::Model::Ai::Order::FILL_CREATE_FORM_PROMPT.gsub(
        '{{today}}', Date.today.strftime('%F')
      ).gsub(
        '{{countries}}', app.travel_poi_app_countries.pluck(:name).compact.join('、')
      ).gsub(
        '{{channels}}', app.travel_channels.pluck(:name).compact.join('、')
      )

      body = Ai::OpenaiService.completions({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: text },
        ],
        response_format: { type: 'json_object' },
      })

      json = JSON.parse(body)

      activity_temps = []
      activity_temp_result = []

      if json.dig('temp_info').present?
        embedding = Ai::OpenaiService.embeddings([json.dig('temp_info')]).first
        activity_temps = app.travel_activity_temps.nearest_neighbors(:name_embedding, embedding, distance: "euclidean").first(10)
        activity_temp_result = activity_temps.map { |p| { id: p.id, name: p.name } }
      end

      if activity_temps.present?
        prompt = Travel::Model::Ai::Order::RERANK_TEMPS_PROMPT.gsub(
          '{{keyword}}', json.dig('temp_info'),
        )

        logger.info("============#{activity_temp_result.to_json}")

        body = Ai::OpenaiService.completions({
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: prompt },
            { role: 'user', content: activity_temp_result.to_json },
          ],
          response_format: { type: 'json_object' },
        })

        json2 = JSON.parse(body)

        if json2.dig('activity_temp_ids').present?
          result = []
          json2.dig('activity_temp_ids').each do |id|
            activity_temp = activity_temps.find { |p| p[:id] == id }
            result << activity_temp if activity_temp
          end
          activity_temp_result = result
        end
      end

      user = nil

      if json.dig('contract_phone').present?
        user = app.users.find_or_initialize_by(account: json.dig('contract_phone'))
        if user.new_record?
          user.update!(name: json.dig('contract_name'))
          user.generate_customer_member!
        elsif user.members.where(member_identity: app.member_identities.find_by(member_type: 'Travel::Customer')).empty?
          user.generate_customer_member!
        end
      end

      {
        seq: json.dig('seq'),
        name: json.dig('name'),
        country_ids: app.travel_poi_app_countries.where(name: json.dig('countries')).pluck(:id),
        channel_id: app.travel_channels.find_by(name: json.dig('channel_name'))&.id || app.travel_channels.find_by(name: '其他')&.id,
        contract_name: json.dig('contract_name'),
        contract_phone: json.dig('contract_phone'),
        order_amount: json.dig('amount'),
        customer_ids: [user&.id].compact,
        nearest_activity_temps: activity_temp_result,
      }
    end

    FILL_CREATE_FORM_PROMPT = <<-PROMPT
      用户将提供一段旅游订单描述，请提取出以下信息，按 JSON 格式返回，今天是{{today}}，没有相关属性时，返回 null。
      1. 订单编号(seq)
      2. 订单来源渠道(channel_name)，可选值为{{channels}}
      3. 订单金额(amount)
      4. 出发国家(countries)，可选值为{{countries}}
      5. 客户姓名(contract_name)
      6. 客户电话(contract_phone)
      7. 相关旅游产品信息(temp_info)，必须输出，可做适当拓展，补充联想信息。
      8. 相关旅游产品总结名称(name)，必须输出，可做适当拓展，补充联想信息。
    PROMPT

    RERANK_TEMPS_PROMPT = <<-PROMPT
      用户将提供一组旅游产品与关键字，请对产品进行相关性重新排序，按 JSON 格式返回。
      关键字：{{keyword}}
      返回格式：{ "activity_temp_ids": [1, 2, 3, 4, 5] }
    PROMPT
  end
end
