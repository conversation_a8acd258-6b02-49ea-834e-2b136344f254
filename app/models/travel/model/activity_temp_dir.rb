module Travel::Model::ActivityTempDir
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable

    belongs_to :app, class_name: '::App'
    has_many :activity_temps, class_name: 'Travel::ActivityTemp'

    attribute :name, :string, comment: '名称'
    attribute :desc, :string, comment: '描述'

    before_destroy :activity_temp_count_should_be_zero

    def activity_temp_count
      activity_temps.count
    end

    private

    def activity_temp_count_should_be_zero
      return if activity_temp_count.zero?

      raise Errors::BaseError(message: '目录下还有模板，请先删除模板')
    end
  end

  class_methods do
  end
end
