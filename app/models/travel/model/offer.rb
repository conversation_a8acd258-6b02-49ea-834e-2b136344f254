module Travel::Model::Offer
  # 结算单
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    seqable prefix: 'O'

    include Travel::OfferAttributes

    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :payment, class_name: 'Travel::Payment', optional: true
    belongs_to :offer_temp, class_name: 'Travel::OfferTemp', optional: true

    has_many :demands, class_name: 'Travel::Demand', dependent: :nullify
    has_many :activities,  ->{ distinct }, through: :demands, source: :activity
    has_many :demand_grps, ->{ distinct }, through: :demands, source: :demand_grp

    has_one :demand, class_name: 'Travel::Demand', dependent: :nullify
    has_one :activity, through: :demand
    has_one :demand_grp, through: :demand
    has_many :orders, through: :activity, source: :orders
    # has_many :serves, class_name: 'Travel::Serve'

    attribute :state, :string, comment: '状态'
    attribute :pay_state, :string, comment: '支付状态'

    default_value_for(:app) { |o| o.supplier&.app || o.activities.first&.app }

    enum state: {
      # unspecify: 'unspecify',
      pending: 'pending',
      confirmed: 'confirmed',
      terminated: 'terminated',
    }

    enum pay_state: {
      unpaid: 'unpaid',
      paid: 'paid',
    }

    default_value_for(:state) { 'pending' }
    default_value_for(:pay_state) { 'unpaid' }
    default_value_for(:name) { |o| o.generate_name }

    # 确认结算单
    after_save :storage_demands_into_rmb, if: -> { saved_change_to_state? && state == 'confirmed' }
    # after_save :storage_amount, if: -> { saved_change_to_state? && state == 'confirmed' }

    after_save ->{ activities.each{ |a| a.reload.touch } }
    after_destroy ->{ activities.each{ |a| a.reload.touch } }

    validate :payment_should_be_pending_when_change_payment_id, if: -> { saved_change_to_payment_id? }

    before_destroy :validate_payment_state

    # def demands_around_amount_info
    #   Travel::Amount::Calc.new(:around_amount, demands).sum_origin_info
    # end

    def demand_relation
      demands.where(id: demand&.id)
    end

    def demands_amount_info
      Travel::Amount::Calc.new(:amount, demands).sum_origin_info
    end

    def serves_amount_info
      Travel::Amount::Calc.new(:amount, serves).sum_origin_info
    end

    def storage_demands_into_rmb
      # 存储并固定 demand 的汇率，计算 rmb
      demands.each(&:amount_storage_into_rmb)
    end

    def generate_name
      [
        supplier&.name,
        activities.map { |a| a.start_at.strftime('%m%d') }.join(' '),
        demand_grps.map(&:name).join(' '),
      ].join(' ')
    end

    def storage_amount
      # sum_objects = Travel::Amount::Calc.new(:around_amount, demands).sum_origin_objects

      # if sum_objects.length > 1
      #   raise Error::BaseError.new(message: '需求的币种不一致')
      # end

      # if sum_objects.length < 1
      #   raise Error::BaseError.new(message: '需求的币种未确定')
      # end

      # # 保存结算单的币种与金额
      # amount_object = sum_objects.first
      # update!(amount: amount_object.value, amount_unit: amount_object.unit)

      # amount_storage_into_rmb
    end

    def offer_info
      {
        activities_count: activities.count,
        demand_grps: demand_grps.count,
        demands_count: demands.count,
      }
    end

    def pay!
      update!(pay_state: 'paid')
    end

    def self.rebuild
      # 重建结算单，拆分为多个结算单
      # TODO: 重建结算单
      if pay_state == 'paid' || payment.present?
        raise ::Error::BaseError.new(message: '已开始支付的结算单不可拆解')
      end
    end

    # private

    def payment_should_be_pending_when_change_payment_id
      unless payment.state == 'pending' || payment.nil?
        raise ::Error::BaseError.new(message: '支付单状态必须为待支付')
      end
    end

    def validate_payment_state(msg: nil)
      if payment.present? && payment.state.in?(%w[paid unpaid])
        raise ::Error::BaseError.new(message: msg || '相关支付单已确认，结算单不可删除')
      end
    end

    def update_name(force: false)
      return update!(name: generate_name) if force

      self.name ||= generate_name
      save!
    end
  end

  class_methods do

  end
end
