module Travel::Model::ProductDay
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable

    acts_as_list scope: %i[product_id]

    attribute :name, :string, comment: '名称'
    attribute :position, :integer, comment: '排序'

    belongs_to :product, class_name: 'Travel::Product'
    belongs_to :app, class_name: '::App'

    default_value_for(:app) { |o| o.product&.app }

    after_create :update_activity_duration
    after_destroy :update_activity_duration

    include Travel::DayMoveSyncDemands

    define_sync_demands_methods(Travel::DemandTemp) do |product_day|
      product_day&.product&.demand_temps || Travel::DemandTemp.none
    end

    def update_activity_duration
      product.set_duration_by_product_days
    end
  end

  class_methods do
  end
end
