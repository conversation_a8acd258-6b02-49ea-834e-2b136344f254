module Travel::Model::NoticeGrp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    stiable

    belongs_to :app

    has_many :notice_relate_actions, -> { order(position: :asc) },dependent: :destroy
    accepts_nested_attributes_for :notice_relate_actions, allow_destroy: true, reject_if: :all_blank
    has_many :notices, through: :notice_relate_actions, source: :notice

    attribute :name, :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :payload, :jsonb, comment: '其他字段'
  end
end