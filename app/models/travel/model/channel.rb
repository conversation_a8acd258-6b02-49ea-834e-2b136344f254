module Travel::Model::Channel
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    formable
    stiable

    belongs_to :app, class_name: '::App'
    has_many :ota_skus, class_name: 'Travel::OtaSku', dependent: :nullify
    has_many :orders, class_name: 'Travel::Order', dependent: :nullify
    has_many :activities, through: :orders, class_name: 'Travel::Activity'

    attribute :name,  :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :config, :jsonb, comment: '配置'
    attribute :display_config, :jsonb, comment: '展示配置'
    attribute :form, :jsonb, comment: '拓展字段表单'
    serialize :form, coder: Forms::Attr::Item.to_serialization_coder

    enum state: { used: 'used', todo: 'todo' }
    default_value_for(:state) { 'used' }

    action_store(
      :relate,
      :customer,
      class_name: '::User',
      action_class_name: 'Travel::RelationAction',
      alias_name: 'customers',
      inverse_alias_name: 'travel_channels',
    )

    def ota_adapter
    end
  end
end
