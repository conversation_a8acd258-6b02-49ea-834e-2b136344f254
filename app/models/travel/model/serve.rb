module Travel::Model::Serve
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :offer, class_name: 'Travel::Offer', optional: true
    has_many :demands, through: :offer

    belongs_to :serve_product, class_name: 'Travel::ServeProduct', optional: true
    belongs_to :supplier, class_name: 'Travel::Supplier', optional: true

    default_value_for(:supplier) { |o| o.serve_product&.supplier }
  end

  class_methods do
  end
end
