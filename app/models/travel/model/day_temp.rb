module Travel::Model::DayTemp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    acts_as_list scope: %i[activity_temp_id]

    # validates_uniqueness_of :position, scope: :activity_temp_id

    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp'
    belongs_to :temp_parent, class_name: 'Travel::ActivityTemp', foreign_key: :activity_temp_id

    has_many :days, class_name: 'Travel::Day'
    has_many :temp_entities, class_name: 'Travel::Day', foreign_key: :day_temp_id

    after_create :set_activity_temp_duration
    after_destroy :set_activity_temp_duration

    default_value_for(:app) { |o| o.activity_temp&.app }

    include Travel::DayAttributes
    include Travel::ActsAsTemp

    alias_method :old_generate_entity, :generate_entity

    # 模板导入时，相同日期不重复生成
    # 模板导入时，路线、景点、备注 merge
    def generate_entity(import_start_offset: 0, temp_entity_parent: nil, **attrs)
      return unless temp_entity_parent

      exists_day = temp_entity_parent.days.find_by(position: position + import_start_offset)
      unless exists_day
        return old_generate_entity(import_start_offset: import_start_offset, temp_entity_parent: temp_entity_parent, **attrs)
      end

      # 合并 路线、景点、备注
      exists_day.update!(
        city_ids: (exists_day.city_ids + city_ids).uniq,
        scenic_spot_ids: (exists_day.scenic_spot_ids + scenic_spot_ids).uniq,
        desc: [exists_day.desc, desc].compact.join("\n")
      )
      exists_day
    end

    def set_activity_temp_duration
      activity_temp.set_duration_by_day_temps
    end
  end

  class_methods do
  end
end
