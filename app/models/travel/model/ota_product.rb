module Travel::Model::OtaProduct
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    stiable
    seqable

    # association
    belongs_to :app
    belongs_to :channel

    has_many :skus

    # attribute
    attribute :name,        :string,  comment: '名称'
    attribute :state,       :string,  comment: '状态'
    attribute :ota_no,      :string,  comment: 'ota编号'
    attribute :list_time,   :datetime,  comment: '上架时间'
    attribute :delist_time, :datetime,  comment: '下架时间'
    attribute :num,           :integer, comment: '数量'
    attribute :sold_quantity, :integer, comment: '销售数量'
    attribute :price,  :decimal, scale: 5, precision: 20, comment: '价格'
    attribute :payload, :jsonb,   comment: '额外数据'

    default_value_for(:app) { |o| o.channel&.app }
  end

end