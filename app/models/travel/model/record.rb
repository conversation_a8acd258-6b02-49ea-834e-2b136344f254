module Travel::Model::Record
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable

    include Favor::Ext::Markable
    acts_as_markable

    belongs_to :app
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :source, polymorphic: true, optional: true

    has_event :hot

    # attribute
    attribute :name,        :string,  comment: '名称'
    attribute :state,       :string,  comment: '状态'
    attribute :desc,        :string,  comment: '描述'
    attribute :icon,        :string,  comment: 'icon'
    attribute :content,     :string,  comment: '内容'
    attribute :cover_image, :jsonb,   comment: '封面图'
    attribute :position,    :integer, comment: '排序'
    attribute :price,       :float,   comment: '价格'
    attribute :score,       :float,   comment: '评分'
    attribute :used_count,  :integer, comment: '使用人数'
    attribute :stars_count, :integer, comment: '收藏人数'
    attribute :payload,     :jsonb,   comment: '其他字段'
  end
end