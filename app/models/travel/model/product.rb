# module ActiveRecord
#   module ConnectionAdapters
#     module PostgreSQL
#       module OID # :nodoc:
#         class Vector < Type::Value # :nodoc:
#           def initialize(delim, subtype = nil)
#             @delim   = delim
#             @subtype = subtype
#           end

#           def name
#             :vector
#           end
#         end
#       end
#     end
#   end
# end

module Travel::Model::Product
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    seqable
    stiable

    acts_as_list scope: [:app_id]
    # attribute :name_embedding, :vector, comment: '名称向量'
    has_event :recommend
    has_neighbors :name_embedding
    include Notify::Ext::Commentable

    include Favor::Ext::Markable
    acts_as_markable(require_folder: false)

    action_store(
      :relate,
      :country,
      class_name: 'Travel::Poi::AppCountry',
      action_class_name: 'Travel::RelationAction',
      alias_name: 'countries',
      inverse_alias_name: 'products',
    )

    has_many :ota_skus, class_name: 'Travel::OtaSku'
    has_many :skus, class_name: 'Travel::Sku', dependent: :destroy
    has_many :demand_temps, class_name: 'Travel::DemandTemp', dependent: :destroy
    has_many :offer_temps, class_name: 'Travel::OfferTemp'

    has_many :demand_defines, -> { distinct }, through: :demand_temps
    has_many :entity_demands, class_name: 'Travel::Demand'
    has_many :product_days, class_name: 'Travel::ProductDay', dependent: :destroy

    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :app, class_name: '::App'
    belongs_to :product_dir, class_name: 'Travel::ProductDir',  optional: true
    # 产品所属目的地
    belongs_to :destination, class_name: 'Travel::Destination', optional: true
    belongs_to :app_country, class_name: 'Travel::Poi::AppCountry', optional: true


    attribute :name,        :string, comment: '名称'
    attribute :state,       :string, comment: '状态'
    attribute :cover_image, :jsonb,  comment: '图片'
    attribute :content,     :jsonb,  comment: '内容'
    # attribute :days,        :float,  comment: '天数'
    attribute :duration,    :integer, comment: '天数'

    attribute :amount_sync, :boolean, comment: '将报价金额切换为同步需求之和的值'

    attribute :price,             :float,     comment: '商品价格'
    attribute :origin_price,      :float,     comment: '原始价格'
    attribute :position,          :integer,   comment: '商品排序'
    attribute :sale_count,        :integer,   comment: '销售数量'
    attribute :stars_count,       :integer,   comment: '收藏数量'
    attribute :score,             :float,     comment: '评分'
    attribute :origin_score,      :float,     comment: '原始评分'
    attribute :origin_sale_count, :integer,   comment: '原始销售数量'
    attribute :flag,              :string,    comment: '标识'

    default_value_for(:amount_sync) { true }

    delegate :name, to: :creator, prefix: true, allow_nil: true

    include Travel::HasAmount
    has_amount(:amount, '金额')

    after_create :create_days
    after_commit :generate_name_embedding, if: :saved_change_to_name?

    enum state: {
      draft: 'draft',
      onsale: 'onsale',
      canceled: 'canceled'
    }

    default_value_for(:state) { 'onsale' }
    default_value_for(:is_recommended) { false }

    def product_info
      {
        ota_skus_count: ota_skus.count,
        demands_count: entity_demands.count,
        demand_temps_count: demand_temps.count,
      }
    end

    # def demand_temps_around_amount_info
    #   Travel::Amount::Calc.new(:around_amount, demand_temps).sum_origin_info
    # end

    def demand_temps_amount_info
      Travel::Amount::Calc.new(:amount, demand_temps).sum_origin_info
    end

    include Travel::HasAmount
    has_amount(:amount, '报价金额')

    # 最后呈现的报价金额信息
    def demand_amount_computed_info
      amount_sync ? demand_temps_amount_info : [amount_info]
    end

    # 结算单模板成本金额
    def offers_amount_info
      Travel::Amount::Calc.new(:amount, offer_temps).sum_origin_info
    end

    def generate_into_demand_grps(demand_grp, start_position: 1)
      self.class.transaction do
        unless demand_grp.amount
          demand_grp.update!(
            amount: amount,
            amount_unit: amount_unit,
            amount_sync: amount_sync,
          )
        end
        # 生成 支付单 到 行程
        offer_temp_id_2_offer_id_mapping = offer_temps.reduce({}) do |out, offer_temp|
          new_offer = offer_temp.generate_entity(
            activity: demand_grp.activity,
          )
          out[offer_temp.id] = new_offer.id
          out
        end

        # 生成 需求 到 需求组
        demand_temps.each do |demand_temp|
          new_demand = demand_temp.generate_entity(
            product: self,
            demand_grp: demand_grp,
            start_position: start_position + demand_temp.start_offset,
          )
          if demand_temp.offer_temp_id
            new_demand.update!(offer_id: offer_temp_id_2_offer_id_mapping[demand_temp.offer_temp_id])
          end
        end
      end
    end

    def create_days
      duration.times { |i| product_days.create!(position: i + 1) }
    end

    def set_duration_by_product_days
      update_columns(duration: product_days.count)
    end

    def generate_name_embedding
      return unless Rails.env.production?

      # update!(name_embedding: Ai::OpenaiService.embeddings([name]).first)
    end
  end

  class_methods do
  end
end
