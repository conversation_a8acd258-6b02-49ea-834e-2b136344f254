module Travel::Model::DemandGrp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :activity, class_name: 'Travel::Activity'
    belongs_to :temp_entity_parent, class_name: 'Travel::Activity', foreign_key: :activity_id
    belongs_to :demand_grp_temp, class_name: 'Travel::DemandGrpTemp', optional: true

    has_many :demands, class_name: 'Travel::Demand', dependent: :destroy
    has_many :offers, -> { distinct }, through: :demands

    default_value_for(:app) { |o| o.activity&.app }

    include Travel::DemandGrpAttributes
    include Travel::ActsAsTempEntity

    # 报价金额
    def demands_amount_calc_object
      Travel::Amount::Calc.new(:amount, demands)
    end

    def demands_amount_info
      demands_amount_calc_object.sum_origin_info
    end

    def demands_amount_sum_rmb
      amount_sync ? demands_amount_calc_object.sum_rmb : amount_object.rmb
    end

    # 最后呈现的报价金额信息
    def demand_amount_computed_info
      amount_sync ? demands_amount_info : [amount_info]
    end

    # # 预估成本金额
    # def demands_around_amount_calc_object
    #   Travel::Amount::Calc.new(:around_amount, demands)
    # end

    # def demands_around_amount_info
    #   demands_around_amount_calc_object.sum_origin_info
    # end

    # def demands_around_amount_sum_rmb
    #   demands_around_amount_calc_object.sum_rmb
    # end

    # 结算金额
    def offers_amount_calc_object
      Travel::Amount::Calc.new(:amount, offers)
    end

    def offers_amount_info
      offers_amount_calc_object.sum_origin_info
    end

    def offer_state_info
      # 对应 offer state 的 demand 数量
      offers.group(:state).count.merge(
        {
          count: offers.count
        }
      )
    end

    def offer_pay_state_info
      # 对应 offer pay_state 的 demand 数量
      offers.group(:pay_state).count
    end
  end

  class_methods do
  end
end
