module Travel::Model::Activity
  extend ActiveSupport::Concern

  included do
    def temp_entity_parent
      nil
    end

    include Travel::ActivityAttributes
    include Travel::ActsAsTempEntity
    include Travel::Model::Ai::Activity

    self.track_migration = true

    belongs_to :app, class_name: '::App'
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp', optional: true

    has_one :weixin_chat, class_name: 'Weixin::Chat', as: :source

    has_many :demand_grps, class_name: 'Travel::DemandGrp', dependent: :destroy
    has_many :demands, class_name: 'Travel::Demand', dependent: :destroy
    has_many :days, -> { order(position: :asc) }, class_name: 'Travel::Day'
    has_many :offers, -> { distinct }, class_name: 'Travel::Offer', through: :demands, dependent: :nullify
    # has_many :offers, class_name: 'Travel::Offer', dependent: :nullify

    seqable
    effectable(effective_at: :start_at, invalid_at: :end_at, field_type: :date)

    action_store(
      :follow,
      :user,
      class_name: '::User',
      alias_name: 'follow_users',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_follow_activities',
    )

    # 关联的订单
    action_store(
      :relate,
      :order,
      class_name: 'Travel::Order',
      alias_name: 'orders',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'activities',
    )

    has_many :customers, through: :orders, class_name: 'Travel::Customer'
    has_many :channels, through: :orders, class_name: 'Travel::Channel'
    has_many :order_divides, through: :orders, class_name: 'Travel::OrderDivide'
    has_many :revenues, through: :orders, class_name: 'Travel::Revenue'

    # # 关联的国家
    # action_store(
    #   :relate,
    #   :country,
    #   class_name: 'Travel::Poi::Country',
    #   alias_name: 'countries',
    #   action_class_name: 'Travel::ActivityAction',
    #   inverse_alias_name: 'travel_activities',
    # )

    # 关联的私有库国家
    action_store(
      :relate,
      :app_country,
      class_name: 'Travel::Poi::AppCountry',
      alias_name: 'app_countries',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_activities',
    )

    # 关联的城市
    action_store(
      :relate,
      :city,
      class_name: 'Travel::Poi::City',
      alias_name: 'cities',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_activities',
    )

    # 关联的供应商
    action_store(
      :relate,
      :supplier,
      class_name: 'Travel::Supplier',
      alias_name: 'suppliers',
      action_class_name: 'Travel::ActivityAction',
      inverse_alias_name: 'travel_activities',
    )

    # 出行人
    action_store(
      :relate,
      :travellers,
      class_name: '::User',
      action_class_name: 'Travel::CustomerAction',
      alias_name: 'travellers',
      inverse_alias_name: 'travel_activities',
    )

    # 用户填写标题，自动生成name
    attribute :title, :string, comment: '标题'
    attribute :state, :string, comment: '状态'
    attribute :attachments, :jsonb, comment: '附件'
    attribute :remark, :text, comment: '备注'
    attribute :additional_amount, :decimal, scale: 5, precision: 20, comment: '额外成本'
    attribute :profit_amount, :decimal, scale: 5, precision: 20, comment: '利润'
    attribute :follow_state, :string, comment: '跟进状态'

    enum state: {
      pending: 'pending', # 待确认
      confirmed: 'confirmed', # 已确认
      completed: 'completed', # 已完成
      terminated: 'terminated', # 已终止
    }

    enum follow_state: {
      todo: 'todo', # 待跟进
      followed: 'followed', # 已跟进
      done: 'done', # 已完成
    }

    attribute :contact_info, :jsonb, comment: '合同信息'

    default_value_for(:state) { 'pending' }
    default_value_for(:follow_state) { 'done' }
    validates_presence_of :start_at

    before_create :set_default_follow_user
    before_save :set_name
    # 只 permit start_date, duration，编辑不允许修改 duration
    before_save :calc_offset_and_date, if: -> { will_save_change_to_start_at? || will_save_change_to_duration? }
    after_save :trigger_all_demand_and_days, if: -> { saved_change_to_start_at? || saved_change_to_duration? }
    after_create :async_order_customers!

    attr_accessor :import_product_ids,
      :import_activity_temp_id,
      :import_activity_temp_ids,
      :import_sku_ids,
      :import_start_position,
      # 模板导入时的数量（倍率）
      :import_count,
      # 套餐名称
      :import_service_name,
      :current_user,
      :import_demand_start_position,
      :import_demand_grp_id

    after_save :process_import_product_ids
    after_save :process_import_activity_temp_id
    after_save :process_import_activity_temp_ids
    after_save :process_import_sku_ids

    after_create :create_days
    after_create :sync_app_country!
    after_save :calc_profit_amount, if: -> { saved_change_to_additional_amount? }
    after_touch :calc_profit_amount
    after_save :set_desc_color, if: -> { saved_change_to_desc? }

    scope :during, ->(start_at, end_at) {
      where(
        'start_at BETWEEN ? AND ? OR end_at BETWEEN ? AND ? OR (start_at <= ? AND end_at >= ?)',
        start_at, end_at,
        start_at, end_at,
        start_at, end_at
      )
    }

    def item_num
      orders.map(&:item_num).map(&:to_i).sum
    end

    def has_weixin_chat?
      weixin_chat ? true :false
    end
    # 同步订单出行人信息
    def async_order_customers!
      traveller_ids = []
      orders.each{ |order| traveller_ids.concat(order.customer_ids) }
      self.traveller_ids =  traveller_ids.uniq.compact

      reload

      if self.travellers.count > 0
        traveller = self.travellers.first
        return if contact_info&.dig('name') == traveller.name
        update(
          contact_info: {
            name: traveller.name,
            mobile: traveller.mobile || traveller.account,
            email: traveller.email
          }
        )
      end
    end

    # 获取描述颜色
    def set_desc_color
      return nil if current_user.blank?
      member_identity = current_user.member_identities.find{ |m| m.model_payload&.dig('color') }
      color = member_identity&.model_detail&.dig('color')
      update_columns(model_flag: color)
    end

    def new_link_order_id=(id)
      app.travel_orders.find(id).tap do |order|
        order.activities = []
        self.orders << order
      end
    end

    def offers_info
      offers.group(:state).count.merge(
        count: offers.count,
        pay_state: offers.group(:pay_state).count,
      )
    end

    def offers_amount_info
      Travel::Amount::Calc.new(:amount, offers).sum_origin_info
    end

    def demands_amount_sum_rmb
      demand_grps.sum { |grp| grp.demands_amount_sum_rmb || 0 }
    end

    def demand_grps_amount_computed_info
      Travel::Amount::Calc.join_info(
        *demand_grps.map(&:demand_amount_computed_info).flatten
      )
    end

    def orders_info
      {
        count: orders.count,
        income_amount: orders.sum(&:income_amount),
        order_amount: orders.sum(&:order_amount),
        plus_amount: orders.sum(&:plus_amount),
        remain_amount: orders.sum(&:remain_amount),
      }
    end

    def demands_info
      {
        count: demands.count,
        no_offer_count: demands.where(offer_id: nil).count,
      }
    end

    def demand_grps_info
      {
        count: demand_grps.count,
      }
    end

    def revenues_info
      hash = {}
      hash[:refund] = revenues.where(mode: 'refund').group(:platform).sum(:amount_rmb)
      hash[:receive] = revenues.where.not(mode: ['refund', 'discount', 'fee']).group(:platform).sum(:amount_rmb)
      hash
    end

    def calc_offset_and_date
      self.end_at = start_at + (duration - 1).days
    end

    def trigger_all_demand_and_days
      demands.each(&:save!) if saved_change_to_start_at?
      days.each(&:save!) if saved_change_to_start_at?
    end

    def set_name
      country_name = app_countries.first&.name
      start_str = start_at.strftime("%m.%d")
      end_str = (start_at + (duration - 1).days).strftime("%m.%d")
      self.name = "#{country_name}#{start_str}-#{end_str}(#{duration}天)#{contact_info&.dig('name')}#{title}"
    end

    # 默认创建人是行程的跟进人
    def set_default_follow_user
      if follow_users.length == 0 && creator
        follow_users << creator
      end
    end

    def create_days
      unless by_temp_generate
        duration.times { |i| days.create!(position: i + 1) }
      end
    end

    def set_duration_by_days
      update_columns(duration: days.count)
    end

    def process_import_product_ids
      # return unless import_product_ids.present?

      # max_duration = self.duration
      # import_produuct_ids.each do |product_id|
      #   product = app.travel_products.find(product_id)
      #   demand_grp = self.demand_grps.create!(
      #     name: product.name,
      #     demand_define_ids: product.demand_defines.pluck(:id),
      #   )

      #   max_demand_offset = product.demand_temps.maximum(:end_offset) || 0
      #   max_duration = max_demand_offset + 1 if max_demand_offset + 1 > max_duration

      #   product.generate_into_demand_grps(demand_grp, start_position: 1)
      # end

      # self.duration = max_duration
      # update_columns(duration: max_duration)
    end

    def process_import_sku_ids
      return unless import_sku_ids.present?
      self.import_activity_temp_ids = app.travel_ota_skus.where(id: import_sku_ids).pluck(:activity_temp_id).uniq
      self.process_import_activity_temp_ids
    end

    def process_import_activity_temp_id
      return unless import_activity_temp_id.present?

      self.import_activity_temp_ids = [import_activity_temp_id]
    end

    def process_import_activity_temp_ids
      return unless import_activity_temp_ids.present?

      import_start_offset = (self.import_start_position || 1) - 1
      max_duration = self.duration

      import_activity_temp_ids.each do |import_activity_temp_id|
        activity_temp = app.travel_activity_temps.find(import_activity_temp_id)
        max_demand_offset = activity_temp.demand_temps.maximum(:end_offset) || 0
        max_duration = max_demand_offset + 1 if max_demand_offset + 1 > max_duration
        max_duration = activity_temp.duration + import_start_offset if activity_temp.duration + import_start_offset > max_duration
        activity_temp.generate_into_activity(
          self,
          import_start_offset: import_start_offset,
          import_count: import_count || 1,
          import_service_name: import_service_name,
        )
      end

      self.duration = max_duration
      update_columns(duration: max_duration)
    end

    def calc_profit_amount
      update_columns(
        # profit_amount: orders.sum(&:income_amount) - (additional_amount || 0) - (offers_amount_info.first&.[](:rmb) || 0) - orders.sum(&:fee_amount)
        profit_amount: orders.sum(&:order_amount) - (additional_amount || 0) - (offers_amount_info.map{ |o| o[:rmb] }.compact.sum || 0)
      )
    end

    # 重新生成需求
    def refresh_demands!
      exists_demand_grp_ids = demand_grps.pluck(:demand_grp_temp_id)
      activity_temp.demand_grp_temps.find_each do |demand_grp_temp|
        demand_grp = demand_grp_temp.demand_grps.find_or_initialize_by(activity: self)
        demand_grp.update(name: demand_grp_temp.name) unless demand_grp.persisted?

        demand_grp_temp.demand_temps.find_each do |demand_temp|
          demand = demand_temp.demands.find_or_initialize_by(activity: self, demand_grp: demand_grp)
          start_date = self.start_at + demand_temp.start_offset.days
          end_date = self.start_at + demand_temp.end_offset.days
          # 更新完整的demand信息
          demand.update(
            name: demand_temp.name,
            start_date: start_date,
            end_date: end_date,
            offer_amount: demand_temp.offer_amount,
            amount: demand_temp.amount,
            count: demand_temp.count,
            traveller_no: demand_temp.traveller_no,
            # 其他需要从模板复制的字段
          )
        end
      end
    end

    def sync_app_country!
      self.app_country_ids = activity_temp.app_country_ids if activity_temp
    end

    def self.extra_view_attributes(view_type)
      case view_type
      when 'simple'
        %i[
            order_ids
            offer_ids
            offers_amount_info
            app_country_ids
            city_ids
            city_names
            follow_user_ids
            supplier_ids
          ]
      end
    end

    def import_demand_preset_ids=(val)
      val.each do |demand_preset_id|
        demands.create(
          demand_preset_id: demand_preset_id,
          start_position: import_demand_start_position,
          demand_grp_id: import_demand_grp_id
        )
      end
    end
  end

  class_methods do
    def clear_unactive_activity(app_ids: [])
      self.where(app_id: app_ids).find_each do |activity|
        activity.destroy if activity.orders.count < 1
      end
    end
  end
end
