module Travel::Model::DemandTemp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :product, class_name: 'Travel::Product', optional: true
    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp', optional: true
    belongs_to :offer_temp, class_name: 'Travel::OfferTemp', optional: true, touch: true

    belongs_to :demand_preset, class_name: 'Travel::DemandPreset', optional: true

    has_many :demands, class_name: 'Travel::Demand'

    belongs_to :demand_grp_temp, class_name: 'Travel::DemandGrpTemp', optional: true
    belongs_to :temp_parent, class_name: 'Travel::DemandGrpTemp', optional: true, foreign_key: :demand_grp_temp_id
    has_many :temp_entities, class_name: 'Travel::Demand'

    include Travel::DemandAttributes

    delegate :name, to: :demand_grp_temp, prefix: true, allow_nil: true

    # default_value_for(:activity_temp) { |o| o.demand_grp_temp&.activity_temp }
    default_value_for(:app) { |o| o.product&.app || o.activity_temp&.app }

    include Travel::ActsAsTemp

    scope :offer_temp_null_or_eq, ->(offer_temp_id) { where(offer_temp_id: [nil, offer_temp_id == 'noOfferTemp' ? nil : offer_temp_id]) }

    ransackable_scope_defines.concat(%i[
      offer_temp_null_or_eq
    ])

    before_save :set_activity_temp

    after_commit -> { reload.offer_temp&.touch }, if: -> { saved_change_to_offer_temp_id? }
    after_commit :update_offer_temp_name, if: :new_offer_supplier_id

    after_create :generate_demand_info_by_preset!, if: :demand_preset

    # def process_new_offer_supplier_id
    #   old_offer_temp = offer_temp

    #   if new_offer_supplier_id == 0 # 0 代表不指定供应商
    #     self.offer_temp_id = nil
    #   else
    #     offer_temp = demand_grp_temp.offer_temps.find_by(supplier_id: new_offer_supplier_id)

    #     unless offer_temp
    #       offer_temp = activity_temp.offer_temps.create!(
    #         supplier_id: new_offer_supplier_id,
    #         amount_sync: true,
    #       )
    #     end

    #     self.offer_temp_id = offer_temp.id

    #     unless new_offer_supplier_id.in?(activity_temp.supplier_ids)
    #       activity_temp.supplier_ids = activity_temp.supplier_ids + [new_offer_supplier_id]
    #     end
    #   end

    #   if old_offer_temp.present? && old_offer_temp.id != offer_temp&.id && old_offer_temp.demand_temps.where.not(id: id).empty?
    #     old_offer_temp.destroy
    #   end
    # end

    def generate_demand_info_by_preset!
      # attr = demand_preset.attributes.symbolize_keys.slice(
      #   *demand_preset.class.clone_keys
      # ).merge(new_offer_supplier_id: demand_preset.supplier_id)
      # ta_update(attributes: attr)
      clone_keys = demand_preset.class.clone_keys - [:start_offset, :end_offset]
      attr = demand_preset.attributes.symbolize_keys.slice(
        *clone_keys
      ).merge(new_offer_supplier_id: demand_preset.supplier_id)
      ta_update(attributes: attr)
      # 重新计算偏移
      calc_offset
    end

    def process_new_offer_supplier_id
      old_offer_temp = offer_temp

      if new_offer_supplier_id == 0 # 0 代表不指定供应商
        self.offer_temp_id = nil
      else
        offer_temp = demand_grp_temp.offer_temps.find_by(supplier_id: new_offer_supplier_id)

        unless offer_temp
          offer_temp = activity_temp.offer_temps.create!(
            supplier_id: new_offer_supplier_id,
            amount_sync: true,
          )
        end

        # 如果 offer_temp 已经有关联的 demand_temp，需要创建新的 offer_temp
        if offer_temp.demand_temp.present? && offer_temp.demand_temp != self
          offer_temp = activity_temp.offer_temps.create!(
            supplier_id: new_offer_supplier_id,
            amount_sync: true,
          )
        end

        unless new_offer_supplier_id.in?(activity_temp.supplier_ids)
          activity_temp.supplier_ids = activity_temp.supplier_ids + [new_offer_supplier_id]
        end

        # 如果旧的 offer_temp 存在且不等于新的 offer_temp，直接删除旧的 offer_temp
        if old_offer_temp.present? && old_offer_temp.id != offer_temp&.id
          old_offer_temp.destroy
        end

        self.offer_temp_id = offer_temp.id
      end
    end

    alias_method :old_generate_entity, :generate_entity

    # 模板导入时，根据数量（倍率）生成需求数量与金额
    def generate_entity(import_count: 1, **attrs)
      old_generate_entity(
        **attrs,
        import_count: import_count,
        count: (count || 1) * import_count,
        amount: (amount || 0) * import_count,
        # 单价不变
        offer_amount: (offer_amount || 0),
      )
    end

    def touch_offer
      offer_temp&.touch
    end

    def update_offer_temp_name
      offer_temp.update_name(force: true)
    end

    def set_activity_temp
      self.activity_temp = demand_grp_temp&.activity_temp
    end
  end

  class_methods do
  end
end
