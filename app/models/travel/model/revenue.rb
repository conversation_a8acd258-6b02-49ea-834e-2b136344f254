module Travel::Model::Revenue
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    seqable
    stiable

    belongs_to :app
    # 类似于订单这种源
    belongs_to :source, polymorphic: true
    # 创建人
    belongs_to :creator, class_name: '::User', optional: true
    # 类似于ota payment这种具体的数据
    belongs_to :data, polymorphic: true, optional: true

    include Travel::HasAmount
    has_amount(:amount, '支付金额')

    attribute :mode, :string, comment: '模式，例如收款/补款等'
    attribute :remark, :string, comment: '备注信息'
    attribute :attachments, :jsonb, comment: '附件'
    attribute :create_time, :datetime, comment: '收款时间'
    attribute :platform, :string, comment: '平台'

    default_value_for(:app) { |o| o.source.try(:app) }
    default_value_for(:create_time) { Time.zone.now }

    before_save :amount_storage_into_rmb
    after_save :sync_revenue_source
    after_destroy :sync_revenue_source

    enum mode: {
      receive: 'receive', # 收款
      payment: 'payment', # 支付
      refund: 'refund', # 退款
      plus: 'plus', # 补款
      discount: 'discount', # 折扣
      fee: 'fee',
    }

    def refund_amount
      refund? ? amount : nil
    end

    def receive_amount
      refund? ? nil : amount
    end

    def sync_revenue_source
      source.sync_income_amount
    end
  end

  class_methods do
  end
end
