module Travel::Model::DemandGrpTemp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :activity_temp, class_name: 'Travel::ActivityTemp'
    belongs_to :temp_parent, class_name: 'Travel::ActivityTemp', foreign_key: :activity_temp_id

    has_many :demand_temps, class_name: 'Travel::DemandTemp'
    has_many :offer_temps, -> { distinct }, through: :demand_temps

    has_many :demand_grps, class_name: 'Travel::DemandGrp'
    has_many :temp_entities, class_name: 'Travel::DemandGrp', foreign_key: :demand_grp_temp_id

    include Travel::DemandGrpAttributes

    attribute :service_name, :string, comment: '套餐名称'

    default_value_for(:app) { |o| o.activity_temp&.app }

    def demand_temps_amount_info
      Travel::Amount::Calc.new(:amount, demand_temps).sum_origin_info
    end

    # 最后呈现的报价金额信息
    def demand_amount_computed_info
      amount_sync ? demand_temps_amount_info : [amount_info]
    end

    # 结算单模板成本金额
    def offers_amount_info
      Travel::Amount::Calc.new(:amount, offer_temps).sum_origin_info
    end

    include Travel::ActsAsTemp

    def nested_temps
      demand_temps
    end

    alias_method :old_generate_entity, :generate_entity

    # 模板导入时，根据数量（倍率）生成需求组金额
    def generate_entity(import_count: 1, import_service_name: nil, **attrs)
      return unless import_service_name == service_name || service_name.blank?

      old_generate_entity(
        **attrs,
        import_count: import_count,
        amount: (amount || 0) * import_count,
      )
    end
  end

  class_methods do
  end
end
