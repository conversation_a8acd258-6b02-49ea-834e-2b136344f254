class Travel::Supplier < Org
  with_options foreign_key: :supplier_id do
    has_many :travel_offers, class_name: 'Travel::Offer'
    has_many :travel_serve_products, class_name: 'Travel::ServeProduct'
    # has_many :travel_serves, class_name: 'Travel::Serve'
    has_many :travel_payments, class_name: 'Travel::Payment'
    has_many :travel_pay_accounts, class_name: 'Travel::PayAccount'
  end

  action_store(
    :relate,
    :country,
    class_name: 'Travel::Poi::AppCountry',
    action_class_name: 'Travel::RelationAction',
    alias_name: 'countries',
    inverse_alias_name: 'suppliers',
  )

  def supplier_info
    {
      pay_accounts_count: travel_pay_accounts.count,
      offers_count: travel_offers.count,
      payments_count: travel_payments.count,
    }
  end
end
