module Travel::Ext::RevenueSource
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    # 支付和退款
    # 使用多态来关联，可以支持ota收款 三方支付 线下支付等
    has_many :revenues, class_name: 'Travel::Revenue', as: :source, dependent: :destroy

    attribute :origin_amount, :decimal, scale: 5, precision: 20, comment: '原始金额'
    # 应收金额
    attribute :order_amount, :decimal, scale: 5, precision: 20, comment: '金额'
    # 实收金额
    attribute :receive_amount, :decimal, scale: 5, precision: 20, comment: '金额'
    # 补款金额
    attribute :plus_amount, :decimal, scale: 5, precision: 20, comment: '金额'
    # 折扣金额
    attribute :discount_amount, :decimal, scale: 5, precision: 20, comment: '金额'

    # 实际收入总金额
    attribute :income_amount, :decimal, scale: 5, precision: 20, comment: '金额'
    # 正数代表需要支付，负数代表需要付款
    attribute :remain_amount, :decimal, scale: 5, precision: 20, comment: '金额'
    # 手续费
    attribute :fee_amount, :decimal, scale: 5, precision: 20, comment: '手续费用'

    attribute :refund_amount, :decimal, scale: 5, precision: 20, comment: '退款费用'

    after_save :sync_income_amount, if: -> {
      !sync_income_amount_flag && (
        saved_change_to_order_amount? ||
        saved_change_to_plus_amount? ||
        saved_change_to_discount_amount? ||
        saved_change_to_fee_amount? ||
        saved_change_to_refund_amount?
      )
    }

    default_value_for(:order_amount) { 0 }
    default_value_for(:plus_amount) { 0 }
    default_value_for(:discount_amount) { 0 }
    default_value_for(:income_amount) { 0 }
    default_value_for(:remain_amount) { 0 }
    default_value_for(:fee_amount) { 0 }
    default_value_for(:refund_amount) { 0 }

    attr_accessor :sync_income_amount_flag

    # def total_amount
    #   # order_amount + plus_amount - discount_amount - fee_amount
    #   order_amount + plus_amount - discount_amount - refund_amount
    # rescue
    #   order_amount
    # end

    def total_amount
      order_amount
    end

    def sync_income_amount
      plus_amount = revenues.where(mode: 'plus').sum(:amount_rmb) || 0
      fee_amount = revenues.where(mode: 'fee').sum(:amount_rmb) || 0
      refund_amount = revenues.where(mode: 'refund').sum(:amount_rmb) || 0
      discount_amount = revenues.where(mode: 'discount').sum(:amount_rmb) || 0
      income_amount = revenues.where.not(mode: ['refund', 'discount', 'fee']).sum(:amount_rmb) - refund_amount - discount_amount
      receive_amount = revenues.where.not(mode: ['refund', 'discount', 'fee']).sum(:amount_rmb)

      update!(
        income_amount: income_amount,
        plus_amount: plus_amount,
        fee_amount: fee_amount,
        discount_amount: discount_amount,
        receive_amount: receive_amount,
        remain_amount: total_amount - income_amount,
        refund_amount: refund_amount,
        sync_income_amount_flag: true,
      )
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
    end


    scope :amount_state_eq, ->(state) {
      case state
      when '未收款'
        where(income_amount: [0, nil])
      when '部分收款'
        where('income_amount > 0 AND remain_amount > 0')
      when '待退款'
        where('income_amount > 0 AND remain_amount < 0')
      when '全额退款'
        where('order_amount = 0')
      when '已收款'
        where('income_amount > 0 AND remain_amount = 0')
      else
        all
      end
    }

    def amount_state
      if order_amount == 0
        '全额退款'
      elsif income_amount == 0 || income_amount.nil?
        '未收款'
      elsif remain_amount > 0
        '部分收款'
      elsif remain_amount < 0
        '待退款'
      else
        '已收款'
      end
    rescue
      '未知状态'
    end

    ransackable_scope_defines.concat(%i[
      amount_state_eq
    ])

    # 线上收款
    def online_amount
      total_amount = revenues.where(platform: 'ota').sum(:amount_rmb)
      refund_amount = revenues.where(platform: 'ota', mode: 'refund').sum(:amount_rmb)
      total_amount - refund_amount
    end

    #线下收款
    def offline_amount
      total_amount = revenues.where.not(platform: 'ota').where.not(mode: ['refund', 'discount', 'fee']).sum(:amount_rmb)
      refund_amount = revenues.where.not(platform: 'ota').where(mode: 'refund').sum(:amount_rmb)
      total_amount - refund_amount
    end

    def revenue_info
      hash = {}
      hash[:refund] = revenues.where(mode: 'refund').group(:platform).sum(:amount_rmb)
      hash[:receive] = revenues.where.not(mode: ['refund', 'discount', 'fee']).group(:platform).sum(:amount_rmb)
      hash
    end
  end

  class_methods do
  end
end
