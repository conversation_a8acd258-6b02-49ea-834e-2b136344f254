module Xhs::Model::Channel
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable

    belongs_to :app

    has_many :chats,    dependent: :destroy
    has_many :messages, dependent: :destroy

    attribute :name,    :string, comment: '名称'
    attribute :state,   :string, comment: '状态'
    attribute :code,    :string, comment: '标识'
    attribute :agentid, :string, comment: '客服ID'
    attribute :config,  :jsonb,  comment: '配置'
    attribute :payload, :jsonb,  comment: '其他字段'

    def async_all_chats!
      Xhs::ChatService.new.async_all_chats!(self)
    end
  end
end