module Xhs::Model::Message
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable
    seqable

    acts_as_list scope: [:chat]

    belongs_to :app
    belongs_to :user, class_name: '::User', optional: true
    belongs_to :chat,    optional: true
    belongs_to :channel, optional: true

    attribute :name,         :string,   comment: '名称'
    attribute :sender,       :string,   comment: '消息类型'
    attribute :state,        :string,   comment: '状态'
    attribute :message_type, :string,   comment: '消息类型'
    attribute :content,      :text,     comment: '内容'
    attribute :token,        :string,   comment: 'token'
    attribute :senderid,     :string,   comment: '消息发送人ID'
    attribute :receiverid,   :string,   comment: '接受者ID'
    attribute :operate_at,   :datetime, comment: '操作时间'
    attribute :position,     :integer,  comment: '位置'
    attribute :payload,      :jsonb,    comment: '其他字段'

    enum state: { unread: 'unread', readed: 'readed', replied: 'replied' }
    enum sender: { customer: 'customer', system: 'system' }
    default_value_for(:channel){ |o| o.chat&.channel }
    default_value_for(:app){ |o| o.chat&.app || o.channel&.app }

    # token存在表示从xhs同步过来的消息，不需要发送
    after_commit :send_message, on: :create, unless: :token

    def send_message!
      Xhs::MessageService.new.reply(self)
    end
  end
end
