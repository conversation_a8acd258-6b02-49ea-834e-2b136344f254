module Xhs::Model::Chat
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :channel
    belongs_to :user, class_name: '::User', optional: true
    has_many  :messages, dependent: :destroy

    has_event :reply

    attribute :name,       :string,   comment: '名称'
    attribute :position,   :integer,  comment: '位置'
    attribute :state,      :string,   comment: '状态'
    attribute :operate_at, :datetime, comment: '操作时间'
    attribute :agentid,    :string,   comment: '客户ID'
    attribute :nickname,   :string,   comment: '用户昵称'
    attribute :avatar,     :string,   comment: '用户头像'
    attribute :payload,    :jsonb,    comment: '其他字段'

    enum state: { replied: 'active', unreplied: 'unactive' }
    default_value_for(:app){ |o| o.channel&.app }

    def async_all_messages!
      Xhs::MessageService.new.async_all_messages(self)
    end
  end
end
