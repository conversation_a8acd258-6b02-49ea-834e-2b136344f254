module Travel::DemandGrpAttributes
  extend ActiveSupport::Concern

  included do
    formable
    mergeable :model_payload

    belongs_to :app, class_name: '::App'

    attribute :name, :string, comment: '名称'
    attribute :amount_sync, :boolean, comment: '将报价金额切换为同步需求之和的值', default: false

    default_value_for(:amount_sync) { true }

    action_store(
      :relate,
      :demand_define,
      class_name: 'Travel::DemandDefine',
      action_class_name: 'Travel::RelationAction',
      alias_name: 'demand_defines',
      inverse_alias_name: 'demand_grps',
    )

    include Travel::HasAmount
    has_amount(:amount, '报价金额')

    before_save :amount_storage_into_rmb, if: :will_save_change_to_amount?

    def self.clone_keys
      %i[
          name
          mode
          model_payload
          demand_define_ids
          amount
          amount_unit
          amount_sync
        ]
    end
  end

  class_methods do
  end
end
