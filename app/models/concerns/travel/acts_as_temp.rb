module Travel::ActsAsTemp
  extend ActiveSupport::Concern

  included do
    begin
      instance_method(:temp_entities)
    rescue NoMethod<PERSON>rror
      raise '请定义对应的 temp_entities'
    end

    def generate_entity(import_start_offset: 0, import_count: 1, **attrs)
      result = nil
      attributes.symbolize_keys.slice(
        *self.class.clone_keys
      ).merge(
        attrs.symbolize_keys
      ).reverse_merge(import_start_offset: import_start_offset).tap do |_attrs|
        result = temp_entities.ta_create(attributes: _attrs, extra: { app: app, by_temp_generate: true })
        nested_temps.each do |nested_temp|
          nested_temp.generate_entity(
            **attrs,
            temp_entity_parent: result,
            import_start_offset: import_start_offset,
            import_count: import_count,
          )
        end
      end
      result
    end

    def clone(**attrs)
      result = nil
      attributes.symbolize_keys.except(
        :id, :created_at, :updated_at, :deleted_at, :creator_id, :seq
      ).merge(
        attrs.symbolize_keys
      ).tap do |_attrs|
        result = self.class.ta_create(attributes: _attrs, extra: { app: app })
        nested_temps.each do |nested_temp|
          nested_temp.clone(temp_parent: result)
        end
      end
      result
    end

    def nested_temps
      []
    end
  end

  class_methods do
    def clone_keys
      []
    end
  end
end
