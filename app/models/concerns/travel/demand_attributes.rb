module Travel::DemandAttributes
  extend ActiveSupport::Concern

  included do
    formable
    mergeable :model_payload

    belongs_to :app, class_name: '::App'
    belongs_to :poi_region, class_name: 'Travel::Poi::Region', optional: true
    belongs_to :demand_define, class_name: 'Travel::DemandDefine', optional: true

    attribute :name, :string, comment: '名称'
    attribute :desc, :text, comment: '描述'
    attribute :mode, :string, comment: '分类' # 启用

    attribute :count, :integer, comment: '数量'
    attribute :duration, :integer, comment: '天数'
    attribute :start_offset, :integer, comment: '开始日期偏移量'
    attribute :end_offset, :integer, comment: '结束日期偏移量'
    attribute :traveller_no, :integer, comment: '人数'

    attribute :wait_for_move_to_start_offset, :integer, comment: '记录移动天数时的暂存字段'

    delegate :name, :form, to: :demand_define, prefix: true, allow_nil: true

    payloadable(form_method: :demand_define_form)

    include Travel::HasAmount
    has_amount(:amount, '报价金额')
    has_amount(:offer_amount, '成本金额')
    has_amount(:offer_total_amount, '成本金额 * 数量')

    default_value_for(:start_offset) { 0 }
    default_value_for(:duration) { 1 }
    default_value_for(:mode) { 'single' }

    attr_accessor :new_offer_supplier_id

    before_save :process_new_offer_supplier_id, if: :new_offer_supplier_id
    after_save :calc_offset, if: -> { new_record? || saved_change_to_start_offset? || saved_change_to_duration? }
    after_save :calc_offer_total_amount
    after_destroy :touch_offer

    def start_position
      start_offset + 1
    end

    def end_position
      start_offset + duration
    end

    def start_position=(val)
      # position 从 1 开始
      # offset 从 0 开始
      self.start_offset = val ? val - 1 : nil
    end

    # 只 permit start_offset, duration
    def calc_offset
      update_columns(
        end_offset: start_offset + duration - 1
      )
    end

    def process_new_offer_supplier_id
      raise '请实现 process_new_offer_supplier_id 方法'
    end

    def touch_offer
      raise '请实现 touch_offer 方法'
    end

    def self.clone_keys
      %i[
          name
          desc
          count
          duration
          start_offset
          end_offset
          model_payload
          amount
          amount_unit
          offer_amount
          offer_amount_unit
          traveller_no
          poi_region_id
        ]
    end

    def calc_offer_total_amount
      return nil if offer_amount_currency_id.blank?
      self.offer_total_amount_currency_id = offer_amount_currency_id
      data = {
        offer_total_amount_unit: self.offer_total_amount_unit,
        offer_total_amount_ratio: self.offer_total_amount_ratio,
      }
      if mode == 'total'
        data[:offer_amount] = count.to_i > 0 ? (offer_total_amount * 1.0 / count).round(2)  : nil
      else
        data[:offer_total_amount] = ((offer_amount || 0) * (count || 1)).round(2)
      end
      update_columns(data)
      reload
    end
  end

  class_methods do

  end
end
