module Travel::DayMoveSyncDemands
  extend ActiveSupport::Concern

  included do
    class << self
      alias :old_update_all_with_touch :update_all_with_touch

      def define_sync_demands_methods(demand_klass, &day_relate_all_demands_block)
        class_eval do

          define_singleton_method(:update_all_with_touch) do |updates|
            # 这里需要先收集，再进行操作，因为依次操作会导致 同一个需求 被多次编辑
            day_ids = pluck(:id)

            old_update_all_with_touch(updates)

            day_2_demand_ids_should_change = day_ids.reduce({}) do |out, day_id|
              is_plus = updates.include?(' + 1')
              day = unscoped.find(day_id)
              demand_ids = day.one_day_demands(is_plus ? day.position - 1 : day.position + 1).pluck(:id)
              out[day] = demand_ids
              out
            end

            day_2_demand_ids_should_change.each_pair do |day, demand_ids|
              demand_klass.where(id: demand_ids, wait_for_move_to_start_offset: nil).each do |demand|
                demand.update!(start_offset: day.position - 1)
              end
            end

            day_sample = day_2_demand_ids_should_change.keys.first
            if day_sample
              day_relate_all_demands_block.call(day_sample).where.not(
                wait_for_move_to_start_offset: nil
              ).each do |demand|
                demand.update!(start_offset: demand.wait_for_move_to_start_offset)
                demand.update_columns(wait_for_move_to_start_offset: nil)
              end
            end
          end

          define_method(:one_day_demands) do |at_position=position|
            day_relate_all_demands_block.call(self).where(start_offset: at_position - 1, duration: 1)
          end

          alias :old_position= :position=

          def position=(val)
            move_one_day_demand(position, val)
            self.old_position = val
          end

          def move_one_day_demand(old_position=position_before_last_save, new_position=position)
            if old_position
              one_day_demands(old_position).update_all(
                wait_for_move_to_start_offset: new_position - 1
              )
            end
          end
        end
      end
    end
  end

  class_methods do
  end
end
