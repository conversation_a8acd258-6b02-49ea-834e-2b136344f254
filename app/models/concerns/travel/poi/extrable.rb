module Travel::Poi::Extrable
  extend ActiveSupport::Concern

  included do
    has_many :extras, class_name: 'Travel::Poi::Extra', as: :source

    def current_extra(current_app)
      extras.find_by(app: current_app)
    end

    # 生成默认的
    def extra_info=(attribute_hash, app:)
      app_extra = extras.where(app: app).first_or_initialize
      app_extra.attributes = attribute_hash
      app_extra.save!
    end
  end

  class_methods do
  end
end
