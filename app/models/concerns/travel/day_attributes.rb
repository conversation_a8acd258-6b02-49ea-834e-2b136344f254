module Travel::DayAttributes
  extend ActiveSupport::Concern

  included do
    formable
    mergeable :model_payload

    belongs_to :app, class_name: '::App'

    attribute :name, :string, comment: '名称'
    attribute :desc, :text, comment: '描述'
    attribute :position, :integer, comment: '排序'

    action_store(
      :relate,
      :city,
      class_name: 'Travel::Poi::City',
      action_class_name: 'Travel::ActivityCityAction',
      alias_name: 'cities',
      inverse_alias_name: 'days',
    )

    action_store(
      :relate,
      :scenic_spot,
      class_name: 'Travel::Poi::ScenicSpot',
      action_class_name: 'Travel::ActivityScenicSpotAction',
      alias_name: 'scenic_spots',
      inverse_alias_name: 'days',
    )

    # 带上数组的顺序，可重复出现
    alias_method :old_city_ids, :city_ids
    alias_method :old_city_ids=, :city_ids=

    def city_ids
      ordered_ids = (model_payload&.dig('city_ids') || []).map(&:to_i)
      result = []
      ordered_ids.each do |id|
        result << id if old_city_ids.include?(id)
      end
      result
    end

    def city_ids=(ids)
      self.model_payload ||= {}
      self.model_payload['city_ids'] = ids
      self.old_city_ids = ids.uniq
    end

    alias_method :old_scenic_spot_ids, :scenic_spot_ids
    alias_method :old_scenic_spot_ids=, :scenic_spot_ids=

    def scenic_spot_ids
      ordered_ids = (model_payload&.dig('scenic_spot_ids') || []).map(&:to_i)
      result = []
      ordered_ids.each do |id|
        result << id if old_scenic_spot_ids.include?(id)
      end
      result
    end

    def scenic_spot_ids=(ids)
      self.model_payload ||= {}
      self.model_payload['scenic_spot_ids'] = ids
      self.old_scenic_spot_ids = ids.uniq
    end

    def self.clone_keys
      %i[
        name
        desc
        model_payload
        position
        city_ids
        scenic_spot_ids
      ]
    end
  end

  class_methods do
  end
end
