module Travel::ActivityAttributes
  extend ActiveSupport::Concern

  included do
    formable
    mergeable :model_payload

    belongs_to :app, class_name: '::App'

    attribute :name, :string, comment: '名称'
    attribute :desc, :text, comment: '描述'

    attribute :duration, :integer, comment: '天数'

    default_value_for(:duration) { 1 }

    def self.clone_keys
      %i[name desc duration model_payload app_country_ids city_ids]
    end

    def city_names
      cities.pluck(:name)
    end

    after_save :sync_app_country_ids_from_city_ids

    def sync_app_country_ids_from_city_ids
      return unless city_ids.present?

      city_country_ids = cities.pluck(:country_id).uniq
      self.app_country_ids = (app_country_ids + app.travel_poi_app_countries.where(country_id: city_country_ids).pluck(:id)).uniq
    end

  end

  class_methods do
  end
end
