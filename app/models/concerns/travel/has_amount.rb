module Travel::HasAmount
  extend ActiveSupport::Concern

  included do

  end

  class_methods do
    def has_amount(column_name, desc)
      names = Travel::Amount::AttributeName.new(column_name)

      attribute names.column, :decimal, scale: 5, precision: 20, comment: desc
      attribute names.unit, :string, comment: "#{desc}单位"
      attribute names.ratio, :decimal, scale: 5, precision: 20, comment: "#{desc}对人民币汇率"
      attribute names.rmb, :decimal, scale: 5, precision: 20, comment: "#{desc}兑换为人民币价格"

      default_value_for(names.unit) { 'CNY' }
      default_value_for(names.ratio) { 1 }

      define_method(names.object) do
        Travel::Amount::Column.new(self, names.column).amount_object
      end

      define_method(names.info) do
        send(names.object).to_json
      end

      define_method("#{names.column}_storage_into_rmb") do
        self["#{names.rmb}"] = nil if will_save_change_to_attribute?(names.column)
        send("#{names.ratio}=", send(names.info)[:ratio])
        send("#{names.rmb}=", send(names.info)[:rmb])
      end

      define_method(names.currency_id) do
        Travel::Currency.find_by_from_redis(unit: send(names.unit))&.id
      end

      define_method(names.currency_name) do
        Travel::Currency.find_by_from_redis(unit: send(names.unit))&.name
      end

      define_method("#{names.currency_id}=") do |id|
        currency = Travel::Currency.find(id)
        send("#{names.unit}=", currency.unit)
        send("#{names.ratio}=", currency.ratio)
      end
    end
  end
end
