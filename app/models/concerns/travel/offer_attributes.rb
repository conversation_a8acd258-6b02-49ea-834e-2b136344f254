module Travel::OfferAttributes
  extend ActiveSupport::Concern

  included do
    formable
    mergeable :model_payload

    belongs_to :app, class_name: '::App'
    # 供应商
    belongs_to :supplier, optional: true, class_name: 'Travel::Supplier'

    delegate :name, to: :supplier, prefix: true, allow_nil: true

    attribute :name, :string, comment: '名称'
    attribute :amount_sync, :boolean, comment: '将成本金额切换为同步需求之和的值', default: false

    default_value_for(:amount_sync) { true }

    include Travel::HasAmount

    has_amount(:amount, '金额')

    # validates_presence_of :amount

    # 成本金额，用回调存储，与报价不同算法
    def demands_offer_total_amount_calc_object
      Travel::Amount::Calc.new(:offer_total_amount, demand_relation)
    end

    def demands_offer_total_amount_info
      demands_offer_total_amount_calc_object.sum_origin_info
    end

    def self.clone_keys
      %i[
          name
          supplier_id
          model_payload
          amount
          amount_unit
          amount_sync
        ]
    end

    def demand_relation
      raise '请实现 demand_relation 方法'
    end

    after_touch :sync_amount_by_demand_offer_amount, if: :amount_sync
    after_touch :destroy_if_no_demand

    def sync_amount_by_demand_offer_amount
      return unless amount_sync

      try(:validate_payment_state, msg: '支付单已确认，需求不可调整价格')

      info = demands_offer_total_amount_info

      unless info.length <= 1
        raise Error::BaseError.new(message: '请检查相关需求的金额、币种')
      end

      amount_info = info.first

      update!(
        amount: amount_info[:value],
        amount_unit: amount_info[:unit],
        amount_ratio: amount_info[:ratio],
      ) if amount_info
    end

    def destroy_if_no_demand
      return if demand_relation.reload.present?

      destroy!
    end
  end

  class_methods do
  end
end
