class Ai::OpenaiService
  API_KEY = ENV['OPENAI_API_KEY']
  BASE_URL = ENV['OPENAI_BASE_URL']

  def self.completions(data)
    headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{API_KEY}"
    }

    # data = {
    #   model: "gpt-3.5-turbo",
    #   messages: [{ role: "user", content: "Say this is a test!" }],
    #   temperature: 0.7
    # }.to_json

    response = Typhoeus.post(
      File.join(BASE_URL, '/v1/chat/completions'),
      headers: headers,
      body: data.to_json
    )

    json = JSON.parse(response.body)
    Rails.logger.info("OpenAI response: #{json}")
    result = json.dig('choices', 0, 'message', 'content')
    result || (raise Error::BaseError.new(message: "请求失败，#{json}"))
  end

  # input array
  def self.embeddings(input)
    headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{API_KEY}"
    }

    data = {
      input: input,
      model: "text-embedding-3-small"
    }

    response = Typhoeus.post(
      File.join(BASE_URL, '/v1/embeddings'),
      headers: headers,
      body: data.to_json
    )

    JSON.parse(response.body)["data"].map { |v| v["embedding"] }
  end
end

