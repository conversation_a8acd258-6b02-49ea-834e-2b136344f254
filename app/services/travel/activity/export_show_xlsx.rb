class Travel::Activity::ExportShowXlsx
  def initialize(activity)
    @activity = activity
    @schedule_data = {
      grp_id_to_demand_ids: {},
      grp_id_to_max_count: {},
      demand_id_to_demand: {},
      demand_id_to_day_offset_ary: {},
      start_offset_to_demand_id_ary: {},
      length_to_demand_id_ary: {},
      grp_id_to_list_result: {}
    }
    @matrix = []
  end

  def export
    calc_schedule_data
    export_xlsx
  end

  def parse_value(value, flag: nil)
    case value
    when Travel::Day
      if flag == :date
        "D#{value.position}\n#{value.date.strftime('%m/%d')}"
      else
        value.desc
      end
    when Travel::Demand
      date_range = [value.start_date, value.end_date].map { |d| d.strftime('%m/%d') }.uniq.join(' ~ ')
      name = value.name
      amount = value.amount_object.try(:rmb)
      [name, date_range, amount].compact.join("\n")
    when Travel::DemandGrp
      if flag == :demands_amount
        "#{value.demands_amount_sum_rmb}元"
      else
        value.name
      end
    when Travel::Activity
      if flag == :remark
        result = []
        if (value.model_payload)
          result << "行程备注：\n#{value.model_payload.dig('行程备注')}" if value.model_payload.dig('行程备注').present?
          result << "费用包含：\n#{value.model_payload.dig('费用包含')}" if value.model_payload.dig('费用包含').present?
          result << "费用不含：\n#{value.model_payload.dig('费用不含')}" if value.model_payload.dig('费用不含').present?
          result << "注意事项：\n#{value.model_payload.dig('注意事项')}" if value.model_payload.dig('注意事项').present?
        end

        result.join("\n")
      elsif flag == :demands_amount
        "#{value.demands_amount_sum_rmb}元"
      else
        value.name
      end
    else
      value
    end
  end

  def reset_schedule_data
    @schedule_data[:grp_id_to_demand_ids] = {}
    @schedule_data[:grp_id_to_max_count] = {}
    @schedule_data[:demand_id_to_demand] = {}
    @schedule_data[:demand_id_to_day_offset_ary] = {}
    @schedule_data[:start_offset_to_demand_id_ary] = {}
    @schedule_data[:length_to_demand_id_ary] = {}
    @schedule_data[:grp_id_to_list_result] = {}
  end

  def calc_schedule_data
    reset_schedule_data
    grps = @activity.demand_grps
    days = @activity.days

    grps.each do |grp|
      @schedule_data[:grp_id_to_demand_ids][grp.id] = []
      grp_offset_to_demand_id_ary = {}

      grp.demands.each do |demand|
        @schedule_data[:grp_id_to_demand_ids][grp.id] << demand.id
        @schedule_data[:demand_id_to_demand][demand.id] = demand
        @schedule_data[:demand_id_to_day_offset_ary][demand.id] = get_demand_offset_ary(demand)

        push_item(@schedule_data[:length_to_demand_id_ary], demand.duration, demand.id)
        push_item(@schedule_data[:start_offset_to_demand_id_ary], demand.start_offset, demand.id)

        @schedule_data[:demand_id_to_day_offset_ary][demand.id].each do |offset|
          push_item(grp_offset_to_demand_id_ary, offset, demand.id)
        end
      end

      grp_all_offset = @schedule_data[:grp_id_to_demand_ids][grp.id]
                          .map { |demand_id| @schedule_data[:demand_id_to_day_offset_ary][demand_id] }
                          .reduce([], :concat)

      max_count = get_appeared_most_num(grp_all_offset)[:count]

      max_count = 1 if max_count == -Float::INFINITY

      @schedule_data[:grp_id_to_max_count][grp.id] = max_count

      list = Array.new(max_count) { [] }
      result_list = []
      mapping = grp_offset_to_demand_id_ary.clone

      max_count.times do |i|
        days.each do |day|
          day_offset = day.position - 1

          if mapping[day_offset].is_a?(Array) && !mapping[day_offset].empty?
            list[i] << mapping[day_offset].shift
          else
            list[i] << 0
          end
        end
      end

      list[0]&.each_with_index do |_, offset|
        list.each_with_index do |line, index|
          next if index >= list.length || list[index][offset].zero?

          ary = list.map { |i| i[offset + 1] }
          exist_in_next_col_index = ary.index(line[offset])

          if exist_in_next_col_index
            moved_val = list[index][offset + 1]
            list[exist_in_next_col_index][offset + 1] = moved_val
            list[index][offset + 1] = list[index][offset]
          end
        end
      end

      max_count.times do |i|
        offset = -1
        now_id = -1
        ary = []

        list[i].each_with_index do |id, index|
          if now_id == id
            ary << {}
            ary[offset][:length] += 1
          else
            ary << { id: id, length: 1 }
            now_id = id
            offset = index
          end
        end
        result_list[i] = ary
      end

      if result_list.empty?
        result_list << [{ id: 0, length: days.length }]
      end

      @schedule_data[:grp_id_to_list_result][grp.id] = result_list
    end
    @matrix = Matrix.new(@schedule_data, grps: grps, days: days, activity: @activity).generate_matrix
  end

  def export_xlsx
    result = nil
    Axlsx::Package.new do |pack|
      pack.use_shared_strings = true
      workbook = pack.workbook
      workbook.use_autowidth = true
      wrap_text = workbook.styles.add_style({alignment: { horizontal: :center, vertical: :center, wrap_text: true }})
      workbook.add_worksheet(name: '行程单') do |sheet|
        merge_attrs = []
        @matrix.each do |row|
          sheet.add_row(
            row.map do |cell|
              merge_attrs << cell.merge_attrs if cell.merge_attrs
              parse_value(cell.value, flag: cell.flag)
            end,
            style: wrap_text
          )
        end
        merge_attrs.each do |attrs|
          sheet.merge_cells("#{Axlsx::cell_r(*attrs.first(2))}:#{Axlsx::cell_r(*attrs.last(2))}")
        end
      end
      file_path = File.join(Rails.root, 'public', 'export')
      FileUtils.mkdir_p(file_path) unless Dir.exist?(file_path)
      file_name = "#{@activity.name}-#{@activity.id}.xlsx"
      file_path_with_name = File.join(file_path, file_name)
      pack.serialize(file_path_with_name)

      Rails.logger.info(file_path_with_name)
      result = file_path_with_name
    end

    result
  end

  def push_item(ary, key, value)
    ary[key] ||= []
    ary[key] << value
  end

  def get_demand_offset_ary(demand)
    (0...demand.duration).map { |i| i + demand.start_offset }
  end

  def get_appeared_most_num(ary)
    count = ary.tally
    max = count.values.max
    { count: max, value: count.key(max) }
  end

  class Cell
    attr_accessor :value, :row, :col, :rowspan, :colspan, :flag

    # row, col 从 0 开始
    def initialize(value, row = 0, col = 0, rowspan: 1, colspan: 1, flag: nil)
      @value = value
      @row = row
      @col = col
      @rowspan = rowspan
      @colspan = colspan
      @flag = flag
    end

    def merge_attrs
      return nil if rowspan == 1 && colspan == 1

      # xlsx.merge_cells(sheet_index, first_row, first_column, last_row, last_column)
      [col, row, col + colspan - 1, row + rowspan - 1]
    end

  end
  class Matrix
    def initialize(schedule_data, activity:, grps: [], days: [])
      @schedule_data = schedule_data
      @grps = grps
      @days = days
      @activity = activity
    end

    def header
      header = [Cell.new('日期'), Cell.new('行程', 0, 1)]

      @grps.each do |grp|
        @schedule_data[:grp_id_to_max_count][grp.id].times do |i|
          header << Cell.new(
            grp,
            0,
            header.length,
            rowspan: 1,
            colspan: i.zero? ? @schedule_data[:grp_id_to_max_count][grp.id] : 1,
            flag: :header
          )
        end
      end
      header
    end

    def footers(content_length)
      footer = [Cell.new('费用共：'), Cell.new(@activity, flag: :demands_amount)]
      @grps.each do |grp|
        @schedule_data[:grp_id_to_max_count][grp.id].times do |i|
          footer << Cell.new(
            grp,
            content_length,
            footer.length,
            rowspan: 1,
            colspan: i.zero? ? @schedule_data[:grp_id_to_max_count][grp.id] : 1,
            flag: :demands_amount
          )
        end
      end
      [
        footer,
        [Cell.new(@activity, content_length + 1, 0, flag: :remark)]
      ]
    end

    def generate_matrix
      matrix = []

      matrix[0] = header

      @days.each_with_index do |day, day_index|
        matrix[day_index + 1] = [
          Cell.new(day, day_index, 0, flag: :date),
          Cell.new(day, day_index, 1, flag: :desc)
        ]

        col = 2
        @grps.each do |grp|
          @schedule_data[:grp_id_to_max_count][grp.id].times do |i|
            result = @schedule_data[:grp_id_to_list_result].dig(grp.id, i - 1, day_index)
            matrix[day_index + 1] << Cell.new(
              result[:id].nil? || result[:id].zero? ? '' : @schedule_data[:demand_id_to_demand][result[:id]],
              day_index + 1,
              col,
              rowspan: result[:length] || 1,
              colspan: 1,
            )
            col += 1
          end
        end
      end

      matrix.concat(footers(matrix.length))

      matrix
    end
  end
end
