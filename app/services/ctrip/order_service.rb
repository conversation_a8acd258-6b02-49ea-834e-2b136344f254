class Ctrip::OrderService
  def perform(method: '', body: {})
  end

  def notice(body)
    channel = Travel::Channel::Ctrip.first
    config = channel.config.with_indifferent_access
    data = Ctrip::Helper::Client::Request.new(
      url: 'https://ttdopen.ctrip.com/api/order/notice.do',
      serviceName: 'OrderConsumedNotice',
      option: config,
      body: body
    ).post
    order = Travel::Order::Ctrip.find_by(oat_no: body[:otaOrderId])
    content = order.model_detail
    order.update(model_detail: content.merge(data['body']))
  end
end
