class Ctrip::Helper::Aes
  # 加密
  def self.encrypt(enc_data, secret_key=nil, iv=nil)
    unless secret_key
      config = ctrip_config
      secret_key = config[:secret_key]
      iv = config[:iv]
    end

    cipher = OpenSSL::Cipher.new('AES-128-CBC')
    cipher.encrypt
    cipher.key = secret_key
    cipher.iv = iv

    encrypted = cipher.update(enc_data) + cipher.final
    encode(encrypted)
  rescue Exception => e
    nil
  end

  def self.decrypt(dec_data, secret_key=nil, iv=nil)
    unless secret_key
      config = ctrip_config
      secret_key = config[:secret_key]
      iv = config[:iv]
    end

    cipher = OpenSSL::Cipher.new('AES-128-CBC')
    cipher.decrypt
    cipher.key = secret_key
    cipher.iv = iv

    data = decode(dec_data)

    decrypted = cipher.update(data) + cipher.final
    decrypted.force_encoding('utf-8')
  rescue Exception => e
    e.message
  end

  def self.encode(data)
    str = ''
    data.each_byte do |b|
      str << ((b >> 4) + 97).chr
      str << ((b & 0xf) + 97).chr
    end
    str
  end

  def self.decode(data)
    str = ''
    (0...data.length).step(2) do |i|
      str << ((data[i].ord - 97) << 4 | (data[i+1].ord - 97)).chr
    end
    str
  end

  def self.ctrip_config
    channel = Travel::Channel::Ctrip.first
    raise 'must set ctrip' unless channel
    channel.config.with_indifferent_access
  end
end


