class Ctrip::Helper::Client
  class Header
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :accountId,   :string
    attr_json :serviceName, :string
    attr_json :requestTime, :string, default: DateTime.now.strftime('%F %T')
    attr_json :version,     :string, default: '1.0'
    attr_json :sign,        :string
    attr_json :signKey,     :string
    attr_json :body,        :string

    # 签名
    def digest
      data = [accountId, serviceName, requestTime, body, version, signKey] * ''
      Digest::MD5.hexdigest(data).downcase
    end

    def request_headers
      {
        accountId: accountId,
        serviceName: serviceName,
        requestTime: requestTime,
        version: version,
        sign: digest,
      }
    end
  end

  class Request
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :url,    :string, default: ''
    attr_json :method, :string, default: 'post'
    attr_json :serviceName,  :string
    attr_json :headers, ActiveModel::Type::Value.new, default: { 'Content-Type': 'application/json' }
    attr_json :body,    ActiveModel::Type::Value.new, default: {}
    attr_json :option,  ActiveModel::Type::Value.new, default: {}

    def post
      response = Typhoeus.post(url, headers: headers, body: request_body.to_json)
      JSON.parse(response.body)
    end

    def request_body
      content = Ctrip::Helper::Aes.encrypt(body.to_json, option[:secret_key], option[:iv])
      header = Ctrip::Helper::Client::Header.new(
                 serviceName: serviceName,
                 accountId: option[:account_id],
                 signKey: option[:sign_key],
                 body: content
               )
      {
        header: header.request_headers,
        body: content
      }
    end
  end
end