class Ctrip::Helper::Order
  # 订单列表
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  attr_json :body,    ActiveModel::Type::Value.new, default: {}
  attr_json :request, ActiveModel::Type::Value.new, default: {}
  attr_json :channel, ActiveModel::Type::Value.new, default: {}

  ### response ###
  def verify
    ###
    # items = []
    # (body[:items] || []).each do |item|
    #   if item[:PLU].start_with?('test_')
    #     return {
    #       header: {
    #         resultCode: '1001',
    #         resultMessage: '产品不存在'
    #       }
    #     }
    #   end

    #   if item[:quantity] > 5
    #     items.push({ PLU: item[:PLU], inventorys: [{ useDate: item[:useStartDate], quantity: 100}]})
    #     return {
    #       header: {
    #         resultCode: '1003',
    #         resultMessage: '库存不足'
    #       },
    #       body: {
    #         items: items
    #       }
    #     }
    #   end

    #   item[:passengers].each do |passenger|
    #     return {
    #       header: {
    #         resultCode: '1006',
    #         resultMessage: '证件异常'
    #       }
    #     } if passenger[:cardNo].blank?
    #   end

    #   return {
    #     header: {
    #       resultCode: '1006',
    #       resultMessage: '缺少出行人'
    #     }
    #   } if item[:passengers].present? && item[:passengers].count < item[:quantity]
    # end

    {
      header: {
        resultCode: '0000',
        resultMessage: '验证成功'
      }
    }
  end

  def create_pre(app: App.first)
    channel = Travel::Channel::Ctrip.first
    order = Travel::Order::Ctrip.find_or_initialize_by(ota_no: body[:otaOrderId], channel: channel, app: app)

    # if order.persisted?
    #   return { header: { resultCode: '1102', resultMessage: '订单已预定' } }
    # end

    order.update!(model_detail: body, model_flag: 'pre', ota_state: 11)
    customer_ids = []

    # 关联用户
    if body[:contacts].present?
      body[:contacts].each do |info|
        contact = app.users.find_or_initialize_by(account: info[:mobile])
        contact.update(
          name: info[:name],
          email: info[:email],
          mobile: info[:mobile],
          model_payload: info,
          model_flag: 'travel',
        )
        customer_ids.push(contact.id)
      end
    end

    {
      header: {
        resultCode: '0000',
        resultMessage: 'success'
      },
      body: {
        otaOrderId: body[:otaOrderId],
        supplierOrderId: order.seq,
      }
    }
  end

  def create(app: App.first)
    channel = Travel::Channel::Ctrip.first
    order = Travel::Order::Ctrip.find_or_initialize_by(ota_no: body[:otaOrderId], channel: channel, app: app)

    # if order.persisted?
    #   return { header: { resultCode: '1102', resultMessage: '订单已预定' } }
    # end

    order.update(model_detail: body, ota_state: 2)
    customer_ids = []

    if body[:contacts].present?
      body[:contacts].each do |info|
        contact = app.users.find_or_initialize_by(account: info[:mobile])
        contact.update(
          name: info[:name],
          email: info[:email],
          mobile: info[:mobile],
          model_payload: info,
          model_flag: 'travel',
        )
        customer_ids.push(contact.id)
      end
    end

    items, vouchers = [], order.model_detail.dig('vouchers') || []
    if body[:items].present?
      body[:items].each do |item|
        # if item[:PLU].start_with?('test_')
        #   return {
        #     header: {
        #       resultCode: '1001',
        #       resultMessage: '产品不存在'
        #     }
        #   }
        # end

        # if item[:quantity] > 5
        #   items.push({ itemId: item[:itemId], PLU: item[:PLU], inventorys: [{ useDate: item[:useStartDate], quantity: 100}]})
        #   return {
        #     header: {
        #       resultCode: '1003',
        #       resultMessage: '库存不足'
        #     },
        #     body: {
        #       items: items
        #     }
        #   }
        # end

        # if item[:passengers].count < 0 || item[:passengers].count < item[:quantity]
        #   return {
        #     header: {
        #       resultCode: '1005',
        #       resultMessage: '缺少出行人'
        #     }
        #   }
        # end

        # item[:passengers].each do |passenger|
        #   return {
        #     header: {
        #       resultCode: '1006',
        #       resultMessage: '证件异常'
        #     }
        #   } if passenger[:cardNo].blank?
        # end

        items.push({ itemId: item[:itemId], isCredentialVouchers: 0 })
        if vouchers.blank?
          vouchers.push({ voucherId: [item[:itemId], order.id] * '', itemId: item[:itemId], voucherType: 1 })
        end
        (item[:passengers] || []).each do |info|
          passenger = app.users.find_or_initialize_by(account: info[:mobile])
          passenger.update(
            name: info[:name],
            email: info[:email],
            mobile: info[:mobile],
            model_paylod: info,
            identity_id: info[:cardNo],
            model_flag: 'travel',
            gender: info[:gender],
          )
          customer_ids.push(passenger.id)
        end
      end
    end

    # order.customer_ids = customer_ids
    order.update_columns(model_detail: body.merge(vouchers: vouchers))

    {
      header: {
        resultCode: '0000',
        resultMessage: 'success'
      },
      body: {
        otaOrderId: body[:otaOrderId],
        supplierOrderId: order.seq,
        supplierConfirmType: 1,
        voucherSender: 1,
        vouchers: vouchers,
        items: items
      }
    }
  end

  def pay_pre
    order = Travel::Order::Ctrip.find_by(ota_no: body[:otaOrderId])
    content = order.model_detail.with_indifferent_access
    items, vouchers = [], content[:vouchers] || []

    body[:items].each do |item|
      product = content[:items].find{ |i| i[:PLU] == item[:PLU] }
      product[:itemId] = item[:itemId] if product
      vouchers.push({
        voucherId: [item[:itemId], order.id] * '',
        itemId: item[:itemId],
        voucherType: 1
      }) unless vouchers.find{ |i| i['itemId'] == item[:itemId] }
      items.push({ itemId: item[:itemId], isCredentialVouchers: 0 })
    end

    order.ota_payments.create(type: 'Travel::OtaPayment::Income', amount: order.amount, meta: body)
    order.update(model_detail: content.merge(vouchers: vouchers), ota_state: 2)
    {
      header: {
        resultCode: '0000',
        resultMessage: 'success'
      },
      body: {
        otaOrderId: body[:otaOrderId],
        supplierOrderId: order.seq,
        supplierConfirmType: 1,
        voucherSender: 1,
        vouchers: vouchers,
        items: items,
      }
    }
  end

  def cancel_pre
    order = Travel::Order::Ctrip.find_by(ota_no: body[:otaOrderId])
    # if order && order.terminated?
    #   {
    #     header: {
    #       resultCode: '2102',
    #       resultMessage: '订单已退订'
    #     },
    #   }
    order&.update(state: 'terminated', ota_state: 14)
    {
      header: {
        resultCode: '0000',
        resultMessage: 'success'
      }
    }
  end

  # 取消订单
  def cancel
    order = Travel::Order::Ctrip.find_by(ota_no: body[:otaOrderId])
    unless order
      return {
        header: {
          resultCode: '2001',
          resultMessage: '订单号异常'
        }
      }
    end

    if order.ota_state.to_i == 8
      return {
        header: {
          resultCode: '2002',
          resultMessage: '该订单已经使用'
        }
      }
    end

    content = order.model_detail.with_indifferent_access
    items = []
    ota_state = 5
    body[:items].each do |item|
      return {
        header: {
          resultCode: '2004',
          resultMessage: '取消份数异常'
        }
      } if item[:quantity] > 2

      hash = { itemId: item[:itemId] }
      ota_state = 4 if item[:cancelType] == 2 || item[:cancelType] == 1

      product = content[:items].find{ |i| i[:PLU] == item[:PLU] }
      if product
        product[:cancelQuantity] = item[:quantity]
        passengers = product[:passengers] || []
        passengers.each do |passenger|
          u = (item[:passengers] || []).find{ |i| i[:passengerId] == passenger[:passengerId] }
          passenger[:passengerStatus] = 2 if item[:cancelType] == 0 || u
        end
      end

      voucher = (content[:vouchers] || []).find{ |i| i[:itemId] == item[:itemId] }
      hash[:vouchers] = [{ voucherId: voucher&.dig(:voucherId)}] if voucher&.dig(:voucherId)
      items.push(hash)
    end
    order.update(state: 'terminated', ota_state: ota_state, model_detail: content)
    {
      header: {
        resultCode: '0000',
        resultMessage: 'success'
      },
      body: {
        supplierConfirmType: 1,
        items: items
      }
    }
  end

  def query
    order = Travel::Order::Ctrip.find_by(ota_no: body[:otaOrderId])
    unless order
      return {
        header: {
          resultCode: '4001',
          resultMessage: '订单不存在'
        }
      }
    end

    content = order.model_detail.with_indifferent_access
    items = []

    content[:items].each do |item|
      hash = {
        itemId: item[:itemId] || 0,
        orderStatus: order.ota_state.to_i,
        quantity: item[:quantity],
        useQuantity: item[:useQuantity] || 0,
        cancelQuantity: item[:cancelQuantity] || 0,
      }
      if order.ota_state.to_i.in?([4, 5])
        hash[:passengers] = (item[:passengers] || []).map do |i|
          {
            passengerId: i[:passengerId],
            passengerStatus: i[:passengerStatus] || 0
          }
        end
      end
      items.push(hash)
    end

    {
      header: {
        resultCode: '0000',
        resultMessage: 'success',
      },
      body: {
        otaOrderId: order.ota_no,
        supplierOrderId: order.seq,
        items: items
      }
    }
  end

  def refund
    {
      header: {
        resultCode: '0000',
        resultMessage: 'success'
      },
      body: {
        supplierConfirmType: 1
      }
    }
  end
  ### response ###

  def execute
    request = self.request.with_indifferent_access
    self.body = JSON.parse(Ctrip::Helper::Aes.decrypt(request[:body])).with_indifferent_access
    result = case request[:header][:serviceName]
             when 'VerifyOrder'
               verify
             when 'CreatePreOrder'
               create_pre
             when 'CreateOrder'
                create
             when 'CancelOrder'
               cancel
             when 'CancelPreOrder'
               cancel_pre
             when 'PayPreOrder'
                pay_pre
             when 'QueryOrder'
               query
             when 'RefundOrder'
              refund
             else
              {
                header: { resultCode: '0000', resultMessage: 'success' }
              }
             end
    result[:body] = Ctrip::Helper::Aes.encrypt(result[:body].to_json) if result[:body]
    result
  end
end
