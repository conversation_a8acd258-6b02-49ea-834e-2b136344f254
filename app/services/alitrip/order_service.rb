class Alitrip::OrderService
  def perform(method: '', body: {}, app: App.first)
    raise 'error' unless self.respond_to?(method)
    send(method)
  end

  # 同步订单
  def async_order_list!(body: {})
    Alitrip::Helper::Order.async_list!(body: body)
  end

  # 同步商品
  def async_product_list!(body: {})
    Alitrip::Helper::Product.async_list!(body: body)
  end

  # 同步sku
  def async_sku_list!(body: {})
    Alitrip::Helper::Sku.async_list!(body: body)
  end

  def generate_access_token!(body: {})
    Alitrip::Helper::Oauth::Token.new(**body).request_body
  end

  # 刷新
  def refresh_access_token!(body: {})
    Alitrip::Helper::Oauth::RefreshToken.new(**body).request_body
  end
end