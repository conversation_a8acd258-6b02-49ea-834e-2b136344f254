class Alitrip::Helper::Client
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    # 公共参数
    attr_json :app_key,     :string,  default: ENV['ALITRIP_APP_KEY']
    attr_json :session,     :string,  default: ENV['ALITRIP_SESSION']
    attr_json :timestamp,   :string
    attr_json :v,           :string,  default: '2.0'
    attr_json :sign_method, :string,  default: 'md5'
    attr_json :sign,        :string
    attr_json :format,      :string,  default: 'json'
    attr_json :simplify,    :boolean, default: false
    # 其他参数
    attr_json :secret,      :string,  default: ENV['ALITRIP_APP_SECRET']
    attr_json :body,        ActiveModel::Type::Value.new, default: {}

    # 签名
    def signature
      # 合并参数
      body = attributes.as_json
      query = body.slice!('body', 'secret')

      str = query.merge(body['body']).compact.sort.map{ |k, v| "#{k}#{v}" }
      data = [secret, str, secret] * ''
      Digest::MD5.hexdigest(data).upcase
    end

    # 请求query
    def request_body
      self.timestamp = Time.zone.now.strftime('%F %T')
      self.sign = signature
      body = self.attributes.as_json
      query = body.slice!('body', 'secret')
      query.merge(body['body'])
    end
  end

  class Request
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :url,    :string, default: ENV['ALITRIP_URL']
    attr_json :method, :string, default: 'post'
    attr_json :headers, ActiveModel::Type::Value.new, default: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' }
    attr_json :body,    ActiveModel::Type::Value.new, default: {}
    attr_json :response_key, :string

    def result_key
      response_key.presence || "#{body['method'].gsub('taobao.', '')}_response".gsub('.', '_')
    end

    def post
      response = Typhoeus.post(url, headers: headers, body: body)
      data = JSON.parse(response.body).with_indifferent_access
      data[:error_response] ? data : data[result_key]
    end

    def get url: nil, headers: {}, params: {}
      response = Typhoeus.get(url, headers: headers, params: params)
      JSON.parse response.body
    end
  end
end
