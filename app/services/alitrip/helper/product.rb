class Alitrip::Helper::Product
  # 商品列表
  class Search
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'taobao.items.onsale.get'
    attr_json :fields, :string, default: 'approve_status,num_iid,title,nick,type,cid,pic_url,num,props,valid_thru,list_time,price,has_discount,has_invoice,has_warranty,has_showcase,modified,delist_time,postage_id,seller_cids,outer_id,sold_quantity,skus'
    attr_json :q,               :string
    attr_json :cid,             :integer
    attr_json :seller_cids,     :string
    attr_json :page_no,         :integer, default: 1
    attr_json :page_size,       :integer, default: 100
    attr_json :order_by,        :string
    attr_json :has_discount,    :boolean
    attr_json :has_showcase,    :boolean
    attr_json :is_taobao,       :boolean
    attr_json :is_ex,           :boolean
    attr_json :start_modified,  :string
    attr_json :end_modified,    :string
    attr_json :is_cspu,	        :boolean
    attr_json :is_combine,      :boolean
    attr_json :auction_type,	  :string

    def request_body
      attributes.as_json
    end
  end

  # 商品详情
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method,     :string, default: 'taobao.item.seller.get'
    attr_json :fields,     :string, default: 'num_iid,title,nick,price,approve_status,sku'
    attr_json :num_iid,    :integer

    def request_body
      attributes.as_json
    end
  end

  class << self
    def async_list!(body: {}, channel: nil)
      body = Alitrip::Helper::Product::Search.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post

      if response[:error_response].nil? && response[:items]
        products = response[:items][:item] || []
        products.each do |product|
          travel_product = Travel::OtaProduct.find_or_initialize_by(seq: product[:num_iid], channel: channel)
          attribute = {
            name: product[:title],
            state: product[:approve_status],
            num: product[:num],
            list_time: product[:list_time],
            delist_time: product[:delist_time],
            sold_quantity: product[:sold_quantity],
            price: product[:price],
            updated_at: product[:modified],
            payload: product,
          }
          attribute.merge!(created_at: product[:list_time]) unless travel_product.persisted?
          travel_product.update(attribute)
        end
      end
    end

    def async_all_records!(body: {}, channel: nil)
      total = body[:total] || channel.config.dig('total') || 10
      total.times do |i|
        params = body.deep_dup.merge(page_no: i + 1)
        async_list!(body: params, channel: channel)
      end
    end

    def async_record!(body: {}, channel: nil)
      body = Alitrip::Helper::Product::Query.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post

      unless response[:error_response]
        products = response[:item]
        products.each do |product|
          travel_product = Travel::Product.find_or_initialize_by(seq: product[:num_iid], channel: channel)
          travel_product.update!(
            name: product[:title],
            state: product[:approve_status],
            num: product[:num],
            list_time: product[:list_time],
            delist_time: product[:delist_time],
            sold_quantity: product[:sold_quantity],
            price: product[:price],
            updated_at: product[:modified],
            payload: product,
          )
        end
      end
    end
  end
end
