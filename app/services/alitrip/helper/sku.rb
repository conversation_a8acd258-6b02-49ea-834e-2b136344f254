class Alitrip::Helper::Sku
  # sku列表
  class Search
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method,   :string, default: 'taobao.item.skus.get'
    attr_json :fields,   :string, default: 'sku_id,num_iid,quantity,price,properties_name,created,modified,outer_id'
    attr_json :num_iids, :string

    def request_body
      attributes.as_json
    end
  end

  # sku详情
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'taobao.item.sku.get'
    attr_json :fields, :string, default: 'sku_id,iid,properties,properties_name,quantity,price,outer_id,created,modified,status,sku_spec_id,outer_id'
    attr_json :sku_id, :integer
    attr_json :num_iid, :integer

    def request_body
      attributes.as_json
    end
  end

  class << self
    def async_list!(body: {}, ota_product: nil, channel: nil)
      body = Alitrip::Helper::Sku::Search.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post

      unless response[:error_response]
        skus = response[:skus]&.dig(:sku) || []
        Travel::OtaSku.where(ota_sku_no: skus.map{ |sku| sku[:outer_id] }).update_all(meta: {})
        skus.each do |sku|
          next if sku[:outer_id].blank?
          sku_meta = (sku[:properties_name].to_s + ';').scan(/\d+:\d+:(.*?):(.*?);/)
                                          .filter{|a| a.length == 2}
                                          .to_h
          sku_meta.keys.each{|key| sku_meta.delete(key) if key.include?('出发日期') }
          travel_sku = Travel::OtaSku.find_or_initialize_by(
            channel: channel,
            ota_sku_no: sku[:outer_id],
          )

          sku_service_name = config['sku_service_name'] || '出游人群'
          services = travel_sku.meta&.services&.as_json || []
          services.push({ price: sku[:price], meta: sku_meta, name: sku_meta[sku_service_name] })

          travel_sku.update!(
            name: sku_meta['套餐类型'],
            ota_product_no: sku[:num_iid],
            ota_product: ota_product,
            model_detail: sku,
            uid: ota_product.name,
            meta: {
              name: ota_product.name,
              seq: sku[:outer_id],
              price: sku[:price],
              image_url: ota_product.payload.dig('pic_url'),
              url: "https://traveldetail.fliggy.com/item.htm?id=#{sku[:num_iid]}",
              meta: sku_meta,
              product: {
                price: ota_product.price,
                title: ota_product.name
              },
              services: services,
            },
            created_at: sku[:created],
            updated_at: sku[:modified],
          )
        end
      end
    end

    def async_record!(body: {}, channel: nil)
      body = Alitrip::Helper::Sku::Query.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post

      unless response[:error_response]
        sku = response[:sku]
        travel_sku = Travel::OtaSku.find_or_initialize_by(
          ota_sku_no: sku[:sku_id],
          ota_product_no: sku[:iid],
          channel: channel,
        )
        travel_sku.update!(
          product: Travel::OtaProduct.find_by!(seq: sku[:iid]),
          model_payload: sku,
          created_at: sku[:created],
          updated_at: sku[:modified],
        )
      end
    end

    def async_sku_list!(body: {}, channel: nil)
      # Travel::Product.find_each do |product|
      #   async_list!(body: { num_iids: product.seq })
      # end

      body = Alitrip::Helper::Product::Search.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post

      unless response[:error_response]
        products = response[:items]&.dig(:item) || []
        products.each do |product|
          async_list!(body: { num_iids: product[:num_iid] }, product: product)
        end
      end
    end

    def async_sku_by_product!
      Travel::OtaProduct.find_each do |product|
        Alitrip::Helper::Sku.async_list!(body: { num_iids: product.seq }, ota_product: product, channel: product.channel)
      end
    end
  end
end
