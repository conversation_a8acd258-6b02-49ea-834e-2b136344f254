class Alitrip::Helper::Item
  # 订单详情
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method,   :string, default: 'taobao.alitrip.travel.item.single.query'
    attr_json :item_id, :integer

    def request_body
      attributes.as_json
    end
  end

  class << self
    # { order_id: 淘宝订单id }
    def async_record!(body: {}, channel: nil)
      body = Alitrip::Helper::Item::Query.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post
    end
  end
end