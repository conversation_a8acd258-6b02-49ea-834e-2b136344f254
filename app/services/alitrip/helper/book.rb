class Alitrip::Helper::Book

  class Search
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'alitrip.travel.bookinfos.search'
    attr_json :apply_time_start, :string, default: 1.months.before.strftime('%F %T')
    attr_json :apply_time_end,   :string, default: Time.zone.now.strftime('%F %T')
    attr_json :current_page, :integer, default: 1
    attr_json :page_size,    :integer, default: 100

    def request_body
      attributes.as_json
    end
  end

    # 订单列表
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'alitrip.travel.bookinfo.query'
    attr_json :bookinfo_id, :integer

    def request_body
      attributes.as_json
    end
  end

  class << self
    def async_all_records!(body: {}, total: 1, channel: nil)
      total.times do |page|
        page = page + 1
        async_list!(body: body.dup.merge(current_page: page), channel: channel)
      end
    end

    def async_list!(body: {}, channel: nil)
      params = body.deep_dup
      params = Alitrip::Helper::Book::Search.new(**params).request_body
      config = channel.config
      params = Alitrip::Helper::Client::Query.new(
        body: params,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: params).post
    end

    def async_record!(body: {}, channel: nil)
      body = Alitrip::Helper::Book::Query.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post
    end
  end
end
