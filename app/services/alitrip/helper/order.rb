class Alitrip::Helper::Order
  # 订单列表
  class Search
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'alitrip.travel.trades.search'
    attr_json :current_page, :integer, default: 1
    attr_json :page_size,    :integer, default: 100
    # 订单创建开始/结束时间
    attr_json :start_created_time, :string, default: 1.months.before.strftime('%F %T')
    attr_json :end_created_time,   :string, default: Time.zone.now.strftime('%F %T')
    # 1-等待买家付款，2-等待卖家发货（买家已付款），3-等待买家确认收货，4-交易关闭（买家发起的退款），6-交易成功，8-交易关闭（订单超时 自动关单）
    attr_json :order_status, :integer
    # 类目筛选, 1、旅行购，旅行购定制专用字段，表示搜索旅行购订单。
    attr_json :category, :integer
    attr_json :item_id,  :integer # 商品id

    def request_body
      attributes.as_json
    end
  end

  # 订单详情
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method,   :string, default: 'alitrip.travel.trade.query'
    attr_json :order_id, :integer

    def request_body
      attributes.as_json
    end
  end

  class << self
    def async_list!(body: {}, channel: nil)
      params = body.deep_dup
      params = Alitrip::Helper::Order::Search.new(**params).request_body
      config = channel.config
      params = Alitrip::Helper::Client::Query.new(
        body: params,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      Rails.logger.info "=========async_list==start==========="
      response = Alitrip::Helper::Client::Request.new(body: params).post
      Rails.logger.info "=========async_list==#{response[:error_response]}==========="

      unless response[:error_response]
        order_ids = response[:order_list][:number] || []
        Rails.logger.info "=========order_ids_count===#{order_ids.count}==========="
        order_ids.each do |order_id|
          async_record!(body: { order_id: order_id }, channel: channel)
        end
      end
    end

    def async_all_records!(body: {}, total: 1, channel: nil)
      total.times do |page|
        page = page + 1
        async_list!(body: body.dup.merge(current_page: page), channel: channel)
      end
    end

    # { order_id: 淘宝订单id }
    def async_record!(body: {}, channel: nil)
      body = Alitrip::Helper::Order::Query.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post

      unless response[:error_response]
        data = response[:first_result]
        # 创建订单 主订单不在同步
        # order = Travel::Order::Alitrip.find_or_initialize_by(ota_no: body['order_id'], channel: channel)
        # amount, total_amount = 0, 0
        # attribute = {
        #   seq: body['order_id'],
        #   created_at: data[:created_time],
        #   updated_at: data[:modified_time],
        #   ota_state: data[:status],
        #   model_detail: data,
        #   state: change_state(data[:status]),
        # }
        # order.update!(attribute)

        # 同步买家信息
        buyer_info = data[:buyer_info]
        buyer_id = nil
        info = buyer_info['buyer_message'].to_s.gsub('+86- ', '').gsub('+86-', '').split(' ')
                    .map{|s| s.gsub('：', ':').split(':')}
                    .filter{|arr| arr.length == 2}
                    .to_h
        if info['电话'].present?
          account = info['电话'].gsub(/\p{C}/, '').strip
          buyer = channel.app.users.find_or_initialize_by(account: account)
          buyer.update(
            name: info['联系人'] || buyer.name,
            email: info['邮箱'] || buyer.email,
            model_payload: buyer_info,
            model_flag: 'travel',
          )
          buyer.travel_channel_ids = [channel.id]
          buyer.members.find_or_create_by(type: 'Travel::Customer')
          buyer_id = buyer.id
        end

        # 同步元数据/子订单
        data[:sub_orders][:sub_order_info].each do |sub_order|
          customer_ids = [buyer_id].compact
          ota_sku_id = nil
          ota_product_id = nil
          buy_item_info = sub_order[:buy_item_info]
          # 同步购买商品信息
          if buy_item_info.present?
            sku_meta = (buy_item_info[:sku_properties].to_s + ';').scan(/(.*?):(.*?);/)
                                             .filter{|a| a.length == 2}
                                             .to_h
            sku_meta.keys.each{|key| sku_meta.delete(key) if key.include?('出发日期') }
            if buy_item_info[:out_sku_id]
              sku = Travel::OtaSku.find_by(ota_product_no: buy_item_info[:item_id], ota_sku_no: buy_item_info[:out_sku_id])
              ota_product_id = Travel::OtaProduct.find_by(seq: buy_item_info[:item_id])&.id

              unless sku
                image_url = buy_item_info[:item_image] ? File.join('https://img.alicdn.com/bao/uploaded', buy_item_info[:item_image]) : nil
                sku = Travel::OtaSku.create!(
                  channel: channel,
                  ota_sku_no: buy_item_info[:out_sku_id],
                  ota_product_no: buy_item_info[:item_id],
                  ota_product_id: ota_product_id,
                  name: sku_meta['套餐类型'],
                  uid: buy_item_info[:item_title],
                  model_detail: buy_item_info,
                  meta: {
                    name: buy_item_info[:item_title],
                    price: buy_item_info[:price].to_f / 100,
                    seq: buy_item_info[:out_sku_id],
                    image_url: image_url,
                    url: "https://traveldetail.fliggy.com/item.htm?id=#{buy_item_info[:item_id]}",
                    meta: sku_meta,
                    product: {
                      name: buy_item_info[:item_title],
                      seq: buy_item_info[:item_id],
                    },
                  }
                )
              end
              ota_sku_id = sku.id
            end
          end
          # 同步子订单
          order = Travel::Order::Alitrip.find_or_initialize_by(
                    ota_no: body['order_id'],
                    seq: sub_order[:sub_order_id],
                    channel: channel
                  )
          attribute = {
            model_detail: sub_order,
            ota_state: sub_order[:status],
            state: change_state(sub_order[:status]),
            traveler_num: sub_order[:travellers] && sub_order[:travellers][:traveller_info] ?  sub_order[:travellers][:traveller_info].count : nil,
            order_amount: order.order_amount.to_f > 0 ? order.order_amount.to_f : sub_order[:total_fee].to_f / 100.0,
            origin_amount: sub_order[:total_fee].to_f / 100.0,
            sku_no: buy_item_info&.dig('out_sku_id'),
            order_type: sub_order[:order_type] == 3 ? 'sub' : 'normal',
            # discount_amount: sub_order[:discount_fee].to_f / 100.0,
            ota_sku_id: ota_sku_id,
            ota_product_id: ota_product_id,
            created_at: data[:created_time],
            updated_at: data[:modified_time],
          }
          attribute[:state] = 'todo' if sub_order[:order_type] == 3 && attribute[:state] == 'processing'
          trip_start_date = buy_item_info[:trip_start_date] || order.trip_start_date
          if trip_start_date.blank?
            sku_properties = buy_item_info[:sku_properties] || ''

            month = sku_properties[/出发日期（月）:(.*?);/, 1]&.gsub(/\D/, '')
            day = sku_properties[/出发日期（日）:(.*?)(;|$)/, 1]&.gsub(/\D/, '')

            now = Time.zone.now
            year = now.year

            if month && day
              date_str = "#{year}-#{month}-#{day}"
              date = Date.parse(date_str) rescue nil
              if date && date < now.to_date
                year += 1
                date_str = "#{year}-#{month}-#{day}"
              end
              trip_start_date = date_str
            end
          end

          attribute.merge!(
            trip_start_date: trip_start_date,
            trip_end_date: buy_item_info[:trip_end_date],
            item_num: buy_item_info[:num]
          ) if buy_item_info.present?
          order.update(attribute)

          ### 同步用户收款
          if sub_order[:status].to_s.in? ['WAIT_SELLER_SEND_GOODS', 'WAIT_BUYER_CONFIRM_GOODS', 'TRADE_FINISHED', 'TRADE_CLOSED']
            ota_payment = order.ota_payments.find_or_create_by(type: 'Travel::OtaPayment::Income')
            ota_payment.update(amount: sub_order[:payment].to_f / 100.0)
            seq = "PAY#{order.seq}"
            revenue = order.revenues.find_or_initialize_by(type: 'Travel::OtaRevenue', seq: seq, app: order.app)
            revenue.update(amount: sub_order[:payment].to_f / 100.0, data: ota_payment, mode: 'payment', platform: 'ota')
          end

          # 计算手续费
          # if order.ota_sku && order.ota_sku.rate.to_f > 0
          #   seq = "FEE#{order.seq}"
          #   revenue = order.revenues.find_or_initialize_by(type: 'Travel::OtaRevenue', seq: seq, app: order.app)
          #   revenue.update(amount: sub_order[:payment].to_f * order.ota_sku.rate.to_f / 100.0, data: ota_payment, mode: 'fee')
          # end

          ### 同步用户退款
          # if sub_order[:refund_id] && sub_order[:refund_status] == 'SUCCESS'
          #   ota_payment = order.ota_payments.find_or_create_by(type: 'Travel::OtaPayment::Refund')
          #   ota_payment.update(amount: sub_order[:total_fee].to_f / 100.0)

          #   response = Alitrip::Helper::Refund.query(body: { refund_id: sub_order[:refund_id] }, channel: channel)
          #   if response[:refund] && response[:refund][:refund_fee].to_f > 0
          #     sub_order[:refund] = response[:refund]
          #     order.update(model_detail: sub_order)
          #     seq = "REFUND#{order.seq}"
          #     revenue = order.revenues.find_or_initialize_by(type: 'Travel::OtaRevenue', seq: seq, app: order.app)
          #     revenue.update(amount: response[:refund][:refund_fee].to_f, data: ota_payment, mode: 'refund')
          #   end
          # end
          # # 同步出行人信息
          # travellers = sub_order[:travellers] || []
          # travellers.each do |t|
          #   traveller = app.users.find_or_initialize_by(account: contactor_info['phone'])
          #   traveller.update!(
          #     name: t[:name],
          #     email: t[:email],
          #     identity_id: t[:credential_no],
          #     gender: t[:sex],
          #     model_payload: t,
          #   )
          #   traveller.members.find_or_create_by(type: 'Travel::Traveller')
          # end

          # 同步联系人信息
          contactor_info = sub_order[:contactor]
          if contactor_info && contactor_info['phone'] && contactor_info['phone'].strip.present?
            account = contactor_info['phone'].gsub(/\p{C}/, '').strip
            contactor = channel.app.users.find_or_initialize_by(account: account)
            contactor.update!(
              name: contactor_info[:name],
              email: contactor.email || contactor_info[:email],
              identity_id: contactor_info[:credential_no],
              gender: contactor_info[:sex],
              model_payload: contactor_info,
              model_flag: 'travel',
            )
            contactor.travel_channel_ids = [channel.id]
            contactor.members.find_or_create_by(type: 'Travel::Customer')
            customer_ids.push(contactor.id) unless customer_ids.include?(contactor.id)
          end
          order.customer_ids = customer_ids unless order.customer_ids == customer_ids
          Rails.logger.info "=========order_id=======#{order.seq}======="
        end
      end
    end

    def change_state(state)
      {
        TRADE_NO_CREATE_PAY: 'pending',
        WAIT_BUYER_PAY: 'pending',
        PAY_PENDING: 'pending',
        SELLER_CONSIGNED_PART: 'processing',
        WAIT_SELLER_SEND_GOODS: 'processing',
        WAIT_BUYER_CONFIRM_GOODS: 'processing',
        TRADE_FINISHED: 'finished',
        TRADE_CLOSED: 'terminated',
        TRADE_CLOSED_BY_TAOBAO: 'terminated',
      }.with_indifferent_access.dig(state)
    end
  end
end