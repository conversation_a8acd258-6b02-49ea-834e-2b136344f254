class Alitrip::Helper::Refund
  # sku详情
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'taobao.refund.get'
    attr_json :fields, :string, default: 'refund_fee,payment,buyer_nick'
    attr_json :refund_id, :integer

    def request_body
      attributes.as_json
    end
  end

  class << self
    def query(body: {}, channel: nil)
      body = Alitrip::Helper::Refund::Query.new(**body).request_body
      config = channel.config
      body = Alitrip::Helper::Client::Query.new(
        body: body,
        app_key: config['app_key'],
        session: config['session'],
        secret: config['app_secret'],
      ).request_body
      Alitrip::Helper::Client::Request.new(body: body).post
    end
  end
end
