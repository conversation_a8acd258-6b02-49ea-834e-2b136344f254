class Alitrip::Helper::Itemcat
  class Query
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method,    :string, default: 'taobao.itemcats.get'
    attr_json :cids,      :string
    attr_json :datetime,  :string
    attr_json :fileds,    :string
    attr_json :parent_id, :integer
    # attr_json :fields, :string, default: 'brand.vid,brand.name,item_cat.cid,item_cat.name,item_cat.status,item_cat.sort_order,item_cat.parent_cid,item_cat.is_parent,xinpin_item_cat.cid, xinpin_item_cat.name, xinpin_item_cat.status,xinpin_item_cat.sort_order,xinpin_item_cat.parent_cid,xinpin_item_cat.is_parent'

    def request_body
      attributes.as_json
    end
  end

  # body = { cids: '5919063,136230027' }
  class << self
    def async_record!(body: {})
      body = Alitrip::Helper::Itemcat::Query.new(**body).request_body
      body = Alitrip::Helper::Client::Query.new(body: body).request_body
      response = Alitrip::Helper::Client::Request.new(body: body).post
    end
  end
end