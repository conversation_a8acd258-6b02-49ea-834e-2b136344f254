class Alitrip::Helper::Oauth
  class Token
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'taobao.top.auth.token.create'
    attr_json :code, :string

    def request_body
      attributes.as_json
    end
  end

  class RefreshToken
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :method, :string, default: 'taobao.top.auth.token.refresh'
    attr_json :refresh_token, :string

    def request_body
      attributes.as_json
    end
  end
end
