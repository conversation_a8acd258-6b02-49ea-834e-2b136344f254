class Xiaohongshu::BaseService
  TOKEN_KEY = 'XIAOHONGSHU_ACCESS_TOKEN'.freeze
  BASE_URL = 'https://pro.xiaohongshu.com'


  def initialize(app_key = nil, app_secret = nil)
    @app_key = app_key || ENV['HR_APP_KEY']
    @app_secret = app_secret || ENV['HR_APP_SECRET']
  end

  def fetch_access_token
    url = "#{BASE_URL}/getToken"
    response = Typhoeus.post(url, params: {
      appKey: @app_key,
      appSecret: @app_secret
    })
    body = JSON.parse(response.body)

    if body['code'] == '0'
      token = body['data']['accessToken']
      storage_access_token(token)
      token
    else
      raise "获取访问令牌失败: #{body['msg']}"
    end
  end

  def storage_access_token(token)
    $redis.setex(TOKEN_KEY, 3600 * 24 * 30, token)
  end

  def get_access_token
    token = $redis.get(TOKEN_KEY)
    token || fetch_access_token
  end

  def handle_request(url, method: 'get', params: {}, body: {}, channel: nil)
		authorization = channel.config&.dig('authorization')
		headers = {
			'Authorization' => authorization,
			'Accept' => 'application/json',
			'X-Subsystem' => 'ares'
		}

    if method.to_s.downcase == 'get'
      response = Typhoeus.get("#{BASE_URL}#{url}", headers: headers, params: params)
    else
      response = Typhoeus.post("#{BASE_URL}#{url}", headers: headers, params: params, body: body)
    end

    response = JSON.parse(response.body).with_indifferent_access
    if response[:code] == 0
      response[:data]
    else
      raise "请求失败: #{response['msg']}"
    end
  end
end
