class Xiaohongshu::ChatService < Xiaohongshu::BaseService
  def get_chat_list(channel=nil)
    url = "/api/edith/ads/pro/chat/chatline/chat"
    porch_user_id = channel.config&.dig('porch_user_id')
    limit = channel.config&.dig('chat_limit') || 80
    is_active = true
    params = {
      porch_user_id: porch_user_id,
      limit: limit,
      is_active: is_active
    }

    handle_request(url, method: 'get', params: params, channel: channel)
  end

  def sync_all_chats!(channel)
    channel.chats.update_all(state: 'unactive')
    response = get_chat_list(channel)
    response[:chat_list].each do |chat_data|
      save_chat!(chat_data, channel)
    end
  end

  # 产生聊天记录
  def save_chat! chat_data, channel
    sixin_chat = chat_data[:sixin_chat]
    session = chat_data[:session]
    long_chat_view = chat_data[:long_chat_view]

    # 查找或创建聊天
    chat = channel.chats.find_or_initialize_by(
      agentid: sixin_chat[:user_id]
    )

    # 更新聊天信息
    chat.update!(
      state: 'active',
      nickname: sixin_chat[:nickname],
      avatar: sixin_chat[:avatar],
      is_replied: session[:is_replied],
      operate_at: Time.at(sixin_chat[:last_msg_ts]/1000),
    )
  end
end