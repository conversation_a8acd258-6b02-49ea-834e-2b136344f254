class Xiaohongshu::MessageService < Xiaohongshu::BaseService
  def reply(message)
    # 获取聊天信息# 构建发送消息的参数
    channel = message.channel
    body = {
      c_user_id: channel.config['c_user_id'],
      content: message.content,
      message_type: message.message_type,
      platform: 3,
      receiver_id: message.chat.agentid,
      sender_porch_id: channel.config['porch_user_id'],
      uuid: "#{Time.now.to_i * 1000}-#{rand(10000000..99999999).to_s}",
    }

    params = { porch_user_id: channel.config['porch_user_id'] }

    # 发送消息到小红书API
    url = '/api/edith/ads/pro/chat/chatline/msg'
    response = handle_request(url,
                             method: 'post',
                             params: params,
                             body: body,
                             channel: channel)

  end

  # 同步所有信息
  def async_all_messages!(chat)
    chanel = chat.channel
    params = {
      porch_user_id: chanel.config['porch_user_id'],
      customer_user_id: chat.agentid,
      limit: chanel.config&.dig('message_limit') || 20,
    }

    url = "/api/edith/ads/pro/chat/chatline/msg"
    response = handle_request(url, method: 'get', params: params, channel: chanel)
    response[:messages].each do |data|
      save_message!(data, chat)
    end
  end

  def save_message!(data, chat)
    sixin_message = data[:sixin_message]
    message = chat.messages.find_or_initialize_by(position: sixin_message[:store_id])
    message.update(
      content: sixin_message[:content],
      state: 'readed',
      message_type: sixin_message[:message_type],
      senderid: sixin_message[:sender_id],
      receiverid: sixin_message[:receiver_id],
      token: sixin_message[:id],
      sender: sixin_message[:sender_id] == chat.agentid ? 'customer' : 'system',
      operate_at: Time.at(sixin_message[:created_at]/1000),
    )
  end

  private

  # 解析不同类型的消息内容
  def parse_message_content(content_data)
    # 根据消息类型解析内容
    # 这里需要根据实际情况实现，可能包括文本、图片、商品卡片等不同类型
    return content_data if content_data.is_a?(String)

    if content_data.is_a?(Hash)
      case content_data[:type]
      when 'text'
        content_data[:text]
      when 'image'
        "[图片]"
      when 'card'
        "[商品卡片]"
      else
        content_data.to_json
      end
    else
      content_data.to_json
    end
  end
end
