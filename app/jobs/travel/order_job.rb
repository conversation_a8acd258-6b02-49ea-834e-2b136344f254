class Travel::OrderJob < ApplicationJob
  queue_as :default

  def perform(*args)
    start = ENV['TRAVEL_ALITRIP_ORDER_START'].presence || 60 * 24 * 15
    body = {
      start_created_time: start.to_i.minutes.ago.strftime('%F %T'),
      end_created_time: Time.zone.now.strftime('%F %T')
    }
    Rails.logger.info "execute_time: #{Time.zone.now.strftime('%F %T')}"

    Travel::Channel::Alitrip.used.find_each do |channel|
      Alitrip::Helper::Order.async_all_records!(body: body, total: 10, channel: channel)
    end
  end
end
