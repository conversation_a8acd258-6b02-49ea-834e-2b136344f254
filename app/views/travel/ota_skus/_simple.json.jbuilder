json.partial! 'travel/ota_skus/single', ota_sku: ota_sku
json.extract!(
  ota_sku,
  *ota_sku.class.try(:extra_view_attributes, 'simple'),
)

json.app ota_sku.app, partial: 'apps/single', as: :app
# json.product ota_sku.product, partial: 'travel/products/single', as: :product
json.activity_temp ota_sku.activity_temp, partial: 'travel/activity_temps/single', as: :activity_temp

json.ta_statistics ota_sku.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
