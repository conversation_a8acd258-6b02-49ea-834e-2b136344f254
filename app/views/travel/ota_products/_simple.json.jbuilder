json.partial! 'travel/ota_products/single', ota_product: ota_product
json.extract!(
  ota_product,
  *ota_product.class.try(:extra_view_attributes, 'simple'),
)

json.app ota_product.app, partial: 'apps/single', as: :app
json.channel ota_product.channel, partial: 'travel/channels/single', as: :channel

json.ta_statistics ota_product.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
