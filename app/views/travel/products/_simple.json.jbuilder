json.partial! 'travel/products/single', product: product
json.extract!(
  product,
  *product.class.try(:extra_view_attributes, 'simple'),
  :country_ids,
  :product_info,
  :demand_amount_computed_info,
  :offers_amount_info,
)

# json.app product.app, partial: 'apps/single', as: :app
json.product_dir product.product_dir, partial: 'travel/product_dirs/single', as: :product_dir
json.countries product.countries, partial: 'travel/poi/app_countries/single', as: :app_country
# json.destination product.destination, partial: 'travel/destinations/single', as: :destination
json.app_country product.app_country, partial: 'travel/poi/app_countries/single', as: :app_country
json.has_star product.has_star?(@current_user)

json.ta_statistics product.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
