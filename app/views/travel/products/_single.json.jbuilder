json.extract!(
  product,
  *product.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :product_dir_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :seq,
  :name,
  :state,
  :app_id,
  :type,
  :cover_image,
  :content,
  :amount,
  :amount_unit,
  :amount_ratio,
  :amount_rmb,
  :duration,
  :creator_name,
  :amount_sync,
  :price,
  :origin_price,
  :position,
  :sale_count,
  :stars_count,
  :score,
  :origin_score,
  :origin_sale_count,
  :flag,
  :destination_id,
  :is_recommended,
  :recommended_at,
  :app_country_id,
)
