json.extract!(
  demand_preset,
  *demand_preset.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :demand_grp_preset_id,
  :app_id,
  :poi_region_id,
  :demand_define_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :name,
  :desc,
  :mode,
  :count,
  :duration,
  :start_offset,
  :end_offset,
  :wait_for_move_to_start_offset,
  :payload,
  :payload_summary,
  :amount,
  :amount_unit,
  :amount_ratio,
  :amount_rmb,
  :offer_amount,
  :offer_amount_unit,
  :offer_amount_ratio,
  :offer_amount_rmb,
  :offer_amount_currency_name,
  :offer_total_amount,
  :offer_total_amount_unit,
  :offer_total_amount_ratio,
  :offer_total_amount_rmb,
  :supplier_id,
  :traveller_no,
  :offer_amount_currency_id,
)
