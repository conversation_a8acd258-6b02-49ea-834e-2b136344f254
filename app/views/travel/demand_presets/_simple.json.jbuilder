json.partial! 'travel/demand_presets/single', demand_preset: demand_preset
json.extract!(
  demand_preset,
  *demand_preset.class.try(:extra_view_attributes, 'simple'),
)

json.demand_grp_preset demand_preset.demand_grp_preset, partial: 'travel/demand_grp_presets/single', as: :demand_grp_preset
json.supplier demand_preset.supplier, partial: 'travel/suppliers/single', as: :supplier
json.poi_region demand_preset.poi_region, partial: 'travel/poi/regions/single', as: :poi_region
json.demand_define demand_preset.demand_define, partial: 'travel/demand_defines/single', as: :demand_define

json.ta_statistics demand_preset.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
