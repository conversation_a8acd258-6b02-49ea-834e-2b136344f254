json.partial! 'travel/receipts/single', receipt: receipt
json.extract!(
  receipt,
  *receipt.class.try(:extra_view_attributes, 'simple'),
)

json.app receipt.app, partial: 'apps/single', as: :app
json.payment receipt.payment, partial: 'travel/payments/single', as: :payment
json.creator receipt.creator, partial: 'users/single', as: :user

json.ta_statistics receipt.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
