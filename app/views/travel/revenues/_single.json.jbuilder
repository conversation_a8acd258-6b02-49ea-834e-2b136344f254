json.extract!(
  revenue,
  *revenue.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :source_type,
  :source_id,
  :data_type,
  :data_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :seq,
  :amount,
  :amount_unit,
  :amount_ratio,
  :amount_rmb,
  :mode,
  :remark,
  :type,
  :attachments,
  :create_time,
  :refund_amount,
  :receive_amount,
  :platform
)
