json.extract!(
  demand,
  *demand.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :demand_grp_id,
  :activity_id,
  :demand_temp_id,
  :offer_id,
  :app_id,
  :poi_region_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :name,
  :mode,
  :desc,
  :count,
  :duration,
  :start_offset,
  :end_offset,
  :offer_amount,
  :offer_amount_unit,
  :offer_amount_ratio,
  :offer_amount_rmb,
  :offer_amount_info,
  :offer_amount_currency_id,
  :offer_total_amount,
  :offer_total_amount_unit,
  :offer_total_amount_ratio,
  :offer_total_amount_rmb,
  :offer_total_amount_info,
  :offer_total_amount_currency_id,
  :offer_total_amount_info,
  :amount,
  :amount_unit,
  :amount_ratio,
  :amount_rmb,
  :amount_info,
  :amount_currency_id,
  :start_date,
  :end_date,
  :demand_define_id,
  :start_position,
  :product_id,
  :demand_preset_id,
  :traveller_no,
  :state,
)
