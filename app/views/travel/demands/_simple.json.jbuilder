json.partial! 'travel/demands/single', demand: demand
json.extract!(
  demand,
  *demand.class.try(:extra_view_attributes, 'simple'),
)

json.extract!(
  demand,
  :payload_summary,
  :demand_grp_name,
)


# json.demand_grp demand.demand_grp, partial: 'travel/demand_grps/single', as: :demand_grp
# json.temp_entity_parent demand.temp_entity_parent, partial: 'travel/demand_grps/single', as: :temp_entity_parent
json.activity demand.activity, partial: 'travel/activities/simple', as: :activity
# json.demand_temp demand.demand_temp, partial: 'travel/demand_temps/single', as: :demand_temp
json.offer demand.offer, partial: 'travel/offers/single', as: :offer
json.demand_define demand.demand_define, partial: 'travel/demand_defines/single', as: :demand_define
json.orders demand.orders, partial: 'travel/orders/simple', as: :order
json.payment demand.payment, partial: 'travel/payments/single', as: :payment
# json.app demand.app, partial: 'apps/single', as: :app
# json.poi_region demand.poi_region, partial: 'travel/poi/regions/single', as: :poi_region

json.ta_statistics demand.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
