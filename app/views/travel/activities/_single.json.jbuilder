json.extract!(
  activity,
  *activity.class.try(:extra_view_attributes, 'single'),
  :id,
  :contact_info,
  :created_at,
  :updated_at,
  :creator_id,
  :activity_temp_id,
  :app_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :title,
  :name,
  :desc,
  :duration,
  :state,
  :start_at,
  :end_at,
  :seq,
  :order_ids,
  :offer_ids,
  :offers_amount_info,
  :offers_info,
  :additional_amount,
  :profit_amount,
  :follow_state,
)
