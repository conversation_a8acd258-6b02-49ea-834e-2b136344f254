json.partial! 'travel/activities/single', activity: activity
json.extract!(
  activity,
  *activity.class.try(:extra_view_attributes, 'simple'),
  :app_country_ids,
  :city_ids,
  :city_names,
  :follow_user_ids,
  :supplier_ids,
)

json.has_weixin_chat activity.has_weixin_chat?
json.creator activity.creator, partial: 'users/lite', as: :user
json.app_countries activity.app_countries, partial: 'travel/poi/app_countries/single', as: :app_country
json.orders activity.orders, partial: 'travel/orders/single', as: :order
json.channels activity.channels, partial: 'travel/channels/single', as: :channel
json.order_divides activity.order_divides, partial: 'travel/order_divides/simple', as: :order_divide
json.travellers  activity.travellers,  partial: 'travel/users/lite', as: :user

json.ta_statistics activity.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
