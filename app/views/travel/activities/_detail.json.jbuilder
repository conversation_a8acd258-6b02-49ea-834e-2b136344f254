json.partial! 'travel/activities/simple', activity: activity
json.extract!(
  activity,
  *activity.class.try(:extra_view_attributes, 'detail'),
)

json.extract!(
  activity,
  :order_ids,
  :offer_ids,
  :follow_user_ids,
  :offers_amount_info,
  :orders_info,
  :demands_info,
  :demand_grps_info,
  :demand_grps_amount_computed_info,
  :revenues_info,
  :attachments,
  :remark,
  :item_num,
)

json.follow_users activity.follow_users, partial: 'users/lite', as: :user
json.suppliers activity.suppliers, partial: 'travel/suppliers/single', as: :supplier
# json.demand_grps activity.demand_grps, partial: 'demand_grps/detail', as: :user

