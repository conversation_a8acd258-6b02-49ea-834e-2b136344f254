json.partial! 'travel/offer_temps/single', offer_temp: offer_temp
json.extract!(
  offer_temp,
  *offer_temp.class.try(:extra_view_attributes, 'simple'),
  :demand_temp_ids,
  :demands_offer_total_amount_info,
)

json.product offer_temp.product, partial: 'travel/products/single', as: :product
json.activity_temp offer_temp.activity_temp, partial: 'travel/activity_temps/single', as: :activity_temp
json.app offer_temp.app, partial: 'apps/single', as: :app
json.supplier offer_temp.supplier, partial: 'travel/suppliers/single', as: :supplier

json.ta_statistics offer_temp.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
