json.partial! 'travel/destinations/single', destination: destination
json.extract!(
  destination,
  *destination.class.try(:extra_view_attributes, 'simple'),
  :record_ids,
)

# json.app destination.app, partial: 'apps/single', as: :app
# json.creator destination.creator, partial: 'users/single', as: :user
json.country destination.country, partial: 'travel/poi/countries/single', as: :country

json.ta_statistics destination.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
