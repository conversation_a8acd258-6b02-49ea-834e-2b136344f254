json.extract!(
  demand_temp,
  *demand_temp.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :product_id,
  :demand_grp_temp_id,
  # :demand_grp_id,
  :app_id,
  :poi_region_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :name,
  :desc,
  :count,
  :duration,
  :start_offset,
  :start_position,
  :end_position,
  :end_offset,
  :offer_temp_id,
  :offer_amount,
  :offer_amount_unit,
  :offer_amount_currency_id,
  :offer_amount_currency_name,
  :offer_amount_ratio,
  :offer_amount_rmb,
  :offer_amount_info,
  :offer_total_amount,
  :offer_total_amount_unit,
  :offer_total_amount_ratio,
  :offer_total_amount_rmb,
  :offer_total_amount_info,
  :offer_total_amount_currency_id,
  :offer_total_amount_info,
  :amount,
  :amount_unit,
  :amount_currency_id,
  :amount_ratio,
  :amount_rmb,
  :amount_info,
  :mode,
  :demand_define_id,
  :payload,
  :payload_summary,
  :wait_for_move_to_start_offset,
  :traveller_no,
)
