json.partial! 'travel/demand_temps/single', demand_temp: demand_temp
json.extract!(
  demand_temp,
  *demand_temp.class.try(:extra_view_attributes, 'simple'),
  :demand_grp_temp_name,
)

# json.product demand_temp.product, partial: 'travel/products/single', as: :product
# json.demand_grp_temp demand_temp.demand_grp_temp, partial: 'travel/demand_grp_temps/single', as: :demand_grp_temp
# json.temp_parent demand_temp.temp_parent, partial: 'travel/demand_grp_temps/single', as: :temp_parent
json.app demand_temp.app, partial: 'apps/single', as: :app
json.poi_region demand_temp.poi_region, partial: 'travel/poi/regions/single', as: :poi_region
json.demand_define demand_temp.demand_define, partial: 'travel/demand_defines/single', as: :demand_define
json.offer_temp demand_temp.offer_temp, partial: 'travel/offer_temps/single', as: :offer_temp

json.ta_statistics demand_temp.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
