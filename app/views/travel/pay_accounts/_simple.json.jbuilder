json.partial! 'travel/pay_accounts/single', pay_account: pay_account
json.extract!(
  pay_account,
  *pay_account.class.try(:extra_view_attributes, 'simple'),
  :account_info
)

json.app pay_account.app, partial: 'apps/single', as: :app
json.supplier pay_account.supplier, partial: 'travel/suppliers/simple', as: :supplier

json.ta_statistics pay_account.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
