json.partial! 'travel/poi/regions/single', region: region
json.extract!(
  region,
  *region.class.try(:extra_view_attributes, 'simple'),
)
json.parent region.parent, partial: 'travel/poi/regions/single', as: :region
json.app region.app, partial: 'apps/single', as: :app
json.country_name region.country&.name
json.country region.country, partial: 'travel/poi/countries/single', as: :country
json.creator region.creator, partial: 'users/single', as: :user

json.ta_statistics region.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
