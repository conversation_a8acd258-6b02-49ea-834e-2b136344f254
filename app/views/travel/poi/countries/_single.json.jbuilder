json.extract!(
  country,
  *country.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :name,
  :en_name,
  :cover_image,
  :continent,
)

# if @current_app && (app_country = @current_app.travel_poi_app_countries.find_by(country_id: country.id))
#   json.app_country app_country, partial: 'travel/poi/app_countries/single', as: :app_country
# end
