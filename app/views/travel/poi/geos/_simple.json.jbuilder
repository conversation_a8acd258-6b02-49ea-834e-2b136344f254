json.partial! 'travel/poi/geos/single', geo: geo
json.extract!(
  geo,
  *geo.class.try(:extra_view_attributes, 'simple'),
)

# json.parent geo.parent, partial: 'travel/poi/regions/single', as: :parent
# json.app geo.app, partial: 'apps/single', as: :app
# json.country geo.country, partial: 'travel/poi/countries/single', as: :country
# json.creator geo.creator, partial: 'users/single', as: :user

json.ta_statistics geo.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
