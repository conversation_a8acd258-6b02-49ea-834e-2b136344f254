json.partial! 'travel/poi/app_countries/single', app_country: app_country
json.extract!(
  app_country,
  *app_country.class.try(:extra_view_attributes, 'simple'),
  :cheapest_product,
)

# json.app app_country.app, partial: 'apps/single', as: :app
json.country app_country.country, partial: 'travel/poi/countries/single', as: :country
json.has_star app_country.has_star?(@current_user)

json.ta_statistics app_country.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
