json.partial! 'travel/order_divides/single', order_divide: order_divide
json.extract!(
  order_divide,
  *order_divide.class.try(:extra_view_attributes, 'simple'),
)

json.order order_divide.order, partial: 'travel/orders/single', as: :order
json.user order_divide.user, partial: 'users/single', as: :user

json.ta_statistics order_divide.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
