json.partial! 'travel/offers/single', offer: offer
json.extract!(
  offer,
  *offer.class.try(:extra_view_attributes, 'simple'),
  :offer_info,
  :demand_ids,
  :demands_offer_total_amount_info,
)

json.supplier offer.supplier, partial: 'travel/suppliers/single', as: :supplier
json.payment offer.payment, partial: 'travel/payments/single', as: :payment
json.creator offer.creator, partial: 'users/single', as: :user
# json.activities offer.activities, partial: 'travel/activities/single', as: :activity
json.activity offer.activity, partial: 'travel/activities/single', as: :activity
json.demand_grps offer.demand_grps, partial: 'travel/demand_grps/single', as: :demand_grp
json.orders offer.orders, partial: 'travel/orders/simple', as: :order
json.demands offer.demands, partial: 'travel/demands/single', as: :demand

json.ta_statistics offer.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
