json.extract!(
  order,
  *order.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :product_id,
  :activity_temp_id,
  :channel_id,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :seq,
  :type,
  :name,
  :ota_no,
  :ota_state,
  :state,
  :meta,
  :traveler_num,
  :trip_start_date,
  :trip_end_date,
  :trip_days,
  :item_num,
  :remark,
  :attachments,
  :total_amount,
  :order_amount,
  :plus_amount,
  :discount_amount,
  :income_amount,
  :remain_amount,
  :amount_state,
  :ota_sku_id,
  :is_invoiced,
  :origin,
  :order_type,
  :sub_order_id,
  :fee_amount,
  :refund_amount,
  :origin_amount,
  :receive_amount,
  :online_amount,
  :offline_amount,
)
