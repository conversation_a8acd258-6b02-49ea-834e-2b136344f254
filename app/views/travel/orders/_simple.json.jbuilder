json.partial! 'travel/orders/single', order: order
json.extract!(
  order,
  *order.class.try(:extra_view_attributes, 'simple'),
  :activity_ids,
  :country_ids,
  :ota_state_zh,
  :customer_ids,
  :customers_name,
  :customers_pinyin,
  :payload,
  :channel_payload,
  :maven_ids,
)

# json.app order.app, partial: 'apps/single', as: :app
json.channel    order.channel,    partial: 'travel/channels/lite', as: :channel
json.sub_order  order.sub_order,  partial: 'travel/orders/single', as: :order
# json.product    order.product,    partial: 'travel/products/single', as: :product
json.customers  order.customers,  partial: 'travel/users/lite', as: :user
json.mavens  order.mavens,  partial: 'travel/users/lite', as: :user
json.creator  order.creator,  partial: 'users/lite', as: :user
json.activities order.activities, partial: 'travel/activities/simple', as: :activity
json.countries  order.countries,  partial: 'travel/poi/app_countries/single', as: :app_country
json.order_divides order.order_divides, partial: 'travel/order_divides/single', as: :order_divide
json.activity_temp order.activity_temp, partial: 'travel/activity_temps/single', as: :activity_temp

json.ta_statistics order.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
