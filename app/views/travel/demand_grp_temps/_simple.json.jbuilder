json.partial! 'travel/demand_grp_temps/single', demand_grp_temp: demand_grp_temp
json.extract!(
  demand_grp_temp,
  *demand_grp_temp.class.try(:extra_view_attributes, 'simple'),
)

json.activity_temp demand_grp_temp.activity_temp, partial: 'travel/activity_temps/single', as: :activity_temp
# json.app demand_grp_temp.app, partial: 'apps/single', as: :app

json.ta_statistics demand_grp_temp.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?


json.extract!(
  demand_grp_temp,
  :demand_define_ids,
  :demand_amount_computed_info,
  :offers_amount_info,
)

json.demand_defines demand_grp_temp.demand_defines, partial: 'travel/demand_defines/single', as: :demand_define

json.demand_temps demand_grp_temp.demand_temps, partial: 'travel/demand_temps/simple', as: :demand_temp
