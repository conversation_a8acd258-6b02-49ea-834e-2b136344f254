json.partial! 'travel/product_days/single', product_day: product_day
json.extract!(
  product_day,
  *product_day.class.try(:extra_view_attributes, 'simple'),
)

# json.product product_day.product, partial: 'travel/products/single', as: :product
# json.app product_day.app, partial: 'apps/single', as: :app

json.ta_statistics product_day.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
