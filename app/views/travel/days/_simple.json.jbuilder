json.partial! 'travel/days/single', day: day
json.extract!(
  day,
  *day.class.try(:extra_view_attributes, 'simple'),
)

json.activity day.activity, partial: 'travel/activities/single', as: :activity
# json.temp_entity_parent day.temp_entity_parent, partial: 'travel/activities/single', as: :temp_entity_parent
# json.day_temp day.day_temp, partial: 'travel/day_temps/single', as: :day_temp
# json.app day.app, partial: 'apps/single', as: :app

json.ta_statistics day.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

json.cities day.cities, partial: 'travel/poi/regions/simple', as: :region
json.city_ids day.city_ids
json.scenic_spots day.scenic_spots, partial: 'travel/poi/regions/simple', as: :region
json.scenic_spot_ids day.scenic_spot_ids

json.demand_suppliers_info day.demand_suppliers_info
