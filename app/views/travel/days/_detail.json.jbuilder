json.partial! 'travel/days/simple', day: day
json.extract!(
  day,
  *day.class.try(:extra_view_attributes, 'detail'),
)

json.extract!(
  day,
  :model_detail,
)

json.cities day.cities, partial: 'travel/poi/regions/simple', as: :region
json.city_ids day.city_ids
json.scenic_spots day.scenic_spots, partial: 'travel/poi/regions/simple', as: :region
json.scenic_spot_ids day.scenic_spot_ids

json.demands day.demands_during_day, partial: 'travel/demands/simple', as: :demand
