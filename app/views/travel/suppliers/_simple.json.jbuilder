json.partial! 'travel/suppliers/single', supplier: supplier
json.extract!(
  supplier,
  *supplier.class.try(:extra_view_attributes, 'simple'),
  :supplier_info,
  :country_ids,
)

# json.parent supplier.parent, partial: 'orgs/single', as: :parent
json.app supplier.app, partial: 'apps/single', as: :app
json.countries supplier.countries, partial: 'travel/poi/app_countries/single', as: :app_country
# json.org_identity supplier.org_identity, partial: 'org_identities/single', as: :org_identity

json.ta_statistics supplier.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
