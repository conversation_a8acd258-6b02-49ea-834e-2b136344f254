json.partial! 'travel/users/single', user: user
json.extract!(
  user,
  *user.class.try(:extra_view_attributes, 'simple'),
  :travel_info,
  :travel_channel_ids,
  :res_tag_ids,
)

json.channels user.travel_channels, partial: 'travel/channels/lite', as: :channel
json.tags user.res_tags, partial: 'res/tags/single', as: :tag
json.ta_statistics user.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
