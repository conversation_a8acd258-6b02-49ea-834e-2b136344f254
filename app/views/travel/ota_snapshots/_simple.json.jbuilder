json.partial! 'travel/ota_snapshots/single', ota_snapshot: ota_snapshot
json.extract!(
  ota_snapshot,
  *ota_snapshot.class.try(:extra_view_attributes, 'simple'),
)

json.ota_sku ota_snapshot.ota_sku, partial: 'travel/ota_skus/single', as: :ota_sku
json.order ota_snapshot.order, partial: 'travel/orders/single', as: :order

json.ta_statistics ota_snapshot.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
