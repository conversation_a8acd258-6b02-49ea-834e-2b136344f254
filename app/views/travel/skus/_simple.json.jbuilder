json.partial! 'travel/skus/single', sku: sku
json.extract!(
  sku,
  *sku.class.try(:extra_view_attributes, 'simple'),
)

# json.app sku.app, partial: 'apps/single', as: :app
json.product sku.product, partial: 'travel/products/single', as: :product
# json.creator sku.creator, partial: 'users/single', as: :creator

json.ta_statistics sku.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
