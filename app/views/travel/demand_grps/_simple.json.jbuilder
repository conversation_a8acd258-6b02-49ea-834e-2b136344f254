json.partial! 'travel/demand_grps/single', demand_grp: demand_grp
json.extract!(
  demand_grp,
  *demand_grp.class.try(:extra_view_attributes, 'simple'),
)

json.extract!(
  demand_grp,
  :demand_define_ids,
  :demand_amount_computed_info,
)

json.demand_defines demand_grp.demand_defines, partial: 'travel/demand_defines/single', as: :demand_define

# json.activity demand_grp.activity, partial: 'travel/activities/single', as: :activity
# json.temp_entity_parent demand_grp.temp_entity_parent, partial: 'travel/activities/single', as: :temp_entity_parent
# json.demand_grp_temp demand_grp.demand_grp_temp, partial: 'travel/demand_grp_temps/single', as: :demand_grp_temp
# json.app demand_grp.app, partial: 'apps/single', as: :app

json.ta_statistics demand_grp.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

json.demands demand_grp.demands, partial: 'travel/demands/simple', as: :demand
