json.partial! 'travel/payments/single', payment: payment
json.extract!(
  payment,
  *payment.class.try(:extra_view_attributes, 'simple'),
  :offer_ids,
  :offers_amount_info,
  :remain_amount_info,
)

# json.app payment.app, partial: 'apps/single', as: :app
json.supplier payment.supplier, partial: 'travel/suppliers/single', as: :supplier
json.creator payment.creator, partial: 'users/single', as: :user
json.pay_account payment.pay_account, partial: 'travel/pay_accounts/single', as: :pay_account
json.demand_grps payment.demand_grps, partial: 'travel/demand_grps/single', as: :demand_grp

json.ta_statistics payment.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
