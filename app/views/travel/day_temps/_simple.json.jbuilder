json.partial! 'travel/day_temps/single', day_temp: day_temp
json.extract!(
  day_temp,
  *day_temp.class.try(:extra_view_attributes, 'simple'),
)

json.activity_temp day_temp.activity_temp, partial: 'travel/activity_temps/single', as: :activity_temp
# json.app day_temp.app, partial: 'apps/single', as: :app

json.ta_statistics day_temp.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

json.cities day_temp.cities, partial: 'travel/poi/regions/simple', as: :region
json.city_ids day_temp.city_ids
json.scenic_spots day_temp.scenic_spots, partial: 'travel/poi/regions/simple', as: :region
json.scenic_spot_ids day_temp.scenic_spot_ids
