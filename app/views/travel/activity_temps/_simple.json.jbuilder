json.partial! 'travel/activity_temps/single', activity_temp: activity_temp
json.extract!(
  activity_temp,
  *activity_temp.class.try(:extra_view_attributes, 'simple'),
  :app_country_ids,
  :city_ids,
  :city_names,
  :supplier_ids,
  :service_names,
  :ota_sku_origin_codes,
)

json.creator activity_temp.creator, partial: 'users/single', as: :user
# json.app activity_temp.app, partial: 'apps/single', as: :app
json.app_countries activity_temp.app_countries, partial: 'travel/poi/app_countries/single', as: :app_country
json.activity_temp_dir activity_temp.activity_temp_dir, partial: 'travel/activity_temp_dirs/single', as: :activity_temp_dir

json.ta_statistics activity_temp.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
