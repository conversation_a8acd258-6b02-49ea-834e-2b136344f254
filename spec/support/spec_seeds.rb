
RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    create(:travel_currency, name: 'CNY', unit: 'CNY', ratio: 1)
    create(:travel_currency, name: 'USD', unit: 'USD', ratio: 2)

    @modules = example.metadata[:tags]&.first&.split(' ') || []
    @app = create(:app, code: 'code')
    @user = create(:user, app: @app)
    user_account_info = {
      account_type: 'User',
      account: @user.account,
    }

    allow(User).to receive(:auth!).and_return(user_account_info)

    @member_identity = MemberIdentity.create(
      app: @app,
      member_type: 'Travel::Customer',
      name: 'customer'
    )
    @member = Travel::Customer.create(app: @app, user: @user, member_identity: @member_identity)

    create_list :travel_order, 2, app: @app, customer_ids: [@user.id]
    @travel_orders = Travel::Order.all
    @travel_order = @travel_orders.first
    @travel_order_count = @travel_orders.count

    create_list :travel_poi_country, 2
    @travel_poi_countries = Travel::Poi::Country.all
    @travel_poi_country = @travel_poi_countries.first
    @travel_poi_country_count = @travel_poi_countries.count

    create :travel_poi_app_country, app: @app, country: @travel_poi_country
    @travel_poi_app_countries = @app.travel_poi_app_countries.all
    @travel_poi_app_country = @travel_poi_app_countries.first
    @travel_poi_app_country_count = @travel_poi_app_countries.count

    create_list :travel_poi_region, 2, country: @travel_poi_country
    @travel_poi_regions = Travel::Poi::Region.all
    @travel_poi_region = @travel_poi_regions.first
    @travel_poi_region_count = @travel_poi_regions.count

    @travel_activity_temp = create(:travel_activity_temp, app: @app, duration: 2)
    @travel_activity = create(:travel_activity, app: @app, duration: 2, follow_user_ids: [@user.id])
    @travel_activities = Travel::Activity.all
    @travel_activity_count = @travel_activities.count

    # expect(@travel_activity.days.count).to eq(10)

    @travel_demand_grp_temp = create(:travel_demand_grp_temp, app: @app, activity_temp: @travel_activity_temp)
    @travel_demand_grps = create_list(:travel_demand_grp, 2, app: @app, activity: @travel_activity)
    @travel_demand_grp = @travel_demand_grps.first
    @travel_demand_grp_count = @travel_demand_grps.count

    @travel_day_temp_1 = create(:travel_day_temp, app: @app, activity_temp: @travel_activity_temp, position: 1)
    @travel_day_temp_2 = create(:travel_day_temp, app: @app, activity_temp: @travel_activity_temp, position: 2)

    # expect(@travel_activity_temp.duration).to eq(12)

    @travel_demand_temp = create(:travel_demand_temp, app: @app, demand_grp_temp: @travel_demand_grp_temp, duration: 2, activity_temp: @travel_activity_temp)
    @travel_demands = create_list(
      :travel_demand, 3,
      app: @app,
      demand_grp: @travel_demand_grps.first,
      activity: @travel_activity,
      start_offset: 0,
      duration: 2
    )

    @travel_demand = @travel_demands.first
    @travel_demand_count = @travel_demands.count

  #   expect(@travel_activity.end_at).to eq(@travel_activity.start_at + 9.days)
  #   expect(@travel_demands.first.start_date).to eq(@travel_activity.start_at)
  #   expect(@travel_demands.first.end_date).to eq(@travel_activity.start_at + 1.days)\

    # 渠道
    create_list :travel_channel, 2, app: @app
    @travel_channels = Travel::Channel.all
    @travel_channel = @travel_channels.first
    @travel_channel_count = @travel_channels.count

    # 客户
    @travel_customers = @app.travel_customers
    @travel_customer = @travel_customers.first
    @travel_customer.travel_channel_ids = @travel_channels.pluck(:id)
    @travel_customer_count = @travel_customers.count

    # 供应商
    create_list :travel_supplier, 2, app: @app, country_ids: [@travel_poi_app_country.id]
    @travel_suppliers = Travel::Supplier.all
    @travel_supplier = @travel_suppliers.first
    @travel_supplier_count = @travel_suppliers.count

    # 账户
    create_list :travel_pay_account, 2, app: @app, supplier: @travel_supplier
    @travel_pay_accounts = Travel::PayAccount.all
    @travel_pay_account = @travel_pay_accounts.first
    @travel_pay_account_count = @travel_pay_accounts.count

    create_list :travel_payment, 2, app: @app, supplier: @travel_supplier, offers: @travel_offers
    @travel_payments = Travel::Payment.all
    @travel_payment = @travel_payments.first
    @travel_payment_count = @travel_payments.count

    create_list :travel_receipt, 2, app: @app, creator: @user, payment_id: @travel_payment.id
    @travel_receipts = Travel::Receipt.all
    @travel_receipt = @travel_receipts.first
    @travel_receipt_count = @travel_receipts.count

    create :travel_offer, app: @app, supplier: @travel_supplier, demands: @travel_demands, payment: @travel_payment
    @travel_offers = Travel::Offer.all
    @travel_offer = @travel_offers.first
    @travel_offer_count = @travel_offers.count

    create_list :travel_product_dir, 2, app: @app
    @travel_product_dirs = Travel::ProductDir.all
    @travel_product_dir = @travel_product_dirs.first
    @travel_product_dir_count = @travel_product_dirs.count

    create_list :travel_product, 2, app: @app, product_dir: @travel_product_dir
    @travel_products = Travel::Product.all
    @travel_product = @travel_products.first
    @travel_product_count = @travel_products.count

    create_list :travel_sku, 2, app: @app, product: @travel_product
    @travel_skus = Travel::Sku.all
    @travel_sku = @travel_skus.first
    @travel_sku_count = @travel_skus.count

    @travel_days = Travel::Day.all
    @travel_day = @travel_days.first
    @travel_day_count = @travel_days.count

    create_list :travel_ota_product, 2, app: @app, channel: @travel_channel
    @travel_ota_products = Travel::OtaProduct.all
    @travel_ota_product = @travel_ota_products.first
    @travel_ota_product_count = @travel_ota_products.count

    @travel_currencies = Travel::Currency.all
    @travel_currency = @travel_currencies.first
    @travel_currency_count = @travel_currencies.count

    create_list :travel_demand_grp_preset, 2, app: @app
    @travel_demand_grp_presets = Travel::DemandGrpPreset.all
    @travel_demand_grp_preset = @travel_demand_grp_presets.first
    @travel_demand_grp_preset_count = @travel_demand_grp_presets.count

    create_list :travel_demand_preset, 2, app: @app, demand_grp_preset: @travel_demand_grp_preset
    @travel_demand_presets = Travel::DemandPreset.all
    @travel_demand_preset = @travel_demand_presets.first
    @travel_demand_preset_count = @travel_demand_presets.count

    create_list :travel_destination, 2, app: @app
    @travel_destinations = Travel::Destination.all
    @travel_destination = @travel_destinations.first
    @travel_destination_count = @travel_destinations.count

    create_list :travel_record, 2, app: @app
    @travel_records = Travel::Record.all
    @travel_record = @travel_records.first
    @travel_record_count = @travel_records.count

    create_list :travel_poi_geo, 2, app: @app
    @travel_poi_geos = Travel::Poi::Geo.all
    @travel_poi_geo = @travel_poi_geos.first
    @travel_poi_geo_count = @travel_poi_geos.count

    create_list :travel_notice, 2, app: @app, user: @user
    @travel_notices = Travel::Notice.all
    @travel_notice = @travel_notices.first
    @travel_notice_count = @travel_notices.count

    create_list :travel_notice_grp, 2, app: @app
    @travel_notice_grps = Travel::NoticeGrp.all
    @travel_notice_grp = @travel_notice_grps.first
    @travel_notice_grp_count = @travel_notice_grps.count
  end
end
