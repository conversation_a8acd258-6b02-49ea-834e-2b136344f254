require 'rails_helper'

RSpec.describe Travel::Offer, type: :model do
  it { should belong_to :app }
  it { should belong_to(:creator).optional(true) }
  it { should belong_to(:payment).optional(true) }
  it { should have_many :demands }
  it { should have_many :activities }
  it { should have_many :demand_grps }
  it { should have_one :demand }
  it { should have_one :activity }
  it { should have_one :demand_grp }

  describe 'sync_amount_by_demands' do
    before do
      @offer = create(:travel_offer, app: @app, amount_sync: true)
      @demand = create(
        :travel_demand,
        offer: @offer,
        offer_amount: 100,
        offer_amount_unit: 'CNY',
        demand_grp: @travel_demand_grp
      )
    end

    it 'should update amount and amount_unit' do
      expect(@offer.amount).to eq(100)
      expect(@offer.amount_unit).to eq('CNY')

      @demand.update(offer_amount: 200, offer_amount_unit: 'USD')
      expect(@offer.amount).to eq(200)
      expect(@offer.amount_unit).to eq('USD')

      @offer.update(amount_sync: false)
      @demand.update(offer_amount: 300, offer_amount_unit: 'JPY')
      expect(@offer.amount).to eq(200)
      expect(@offer.amount_unit).to eq('USD')
    end

    it 'should destroy offer when demands is empty' do
      @demand.destroy

      expect(Travel::Offer.exists?(@offer.id)).to be_falsey
    end
  end
end
