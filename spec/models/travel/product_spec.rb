require 'rails_helper'

RSpec.describe Travel::Product, type: :model do
  it { should belong_to :app }
  it { should belong_to(:product_dir).optional(true) }
  it { should have_many :ota_skus }
  it { should have_many :demand_temps }
  it { should have_many :entity_demands }

  it {
    @product = create(:travel_product, app: @app)
    @activity_temp = create(:travel_activity_temp, duration: 10, app: @app)
    @offer_temp = create(:travel_offer_temp, product: @product, temp_parent: @activity_temp)

    @demand_temp_1 = create(:travel_demand_temp, product: @product, offer_temp: @offer_temp)
    @demand_temp_2 = create(:travel_demand_temp, product: @product)

    @demand_grp = create(:travel_demand_grp, activity: @travel_activity)

    @product.generate_into_demand_grps(@demand_grp)

    expect(@demand_grp.demands.count).to eq(2)
    expect(@travel_activity.offers.find_by(offer_temp: @offer_temp)).not_to be_nil
    expect(@demand_grp.demands.find_by(demand_temp: @demand_temp_1).offer_id).to eq(@offer_temp.offers.last.id)
    expect(@demand_grp.demands.find_by(demand_temp: @demand_temp_2).offer_id).to be_nil
  }
end
