require 'rails_helper'

RSpec.describe Travel::DemandTemp, type: :model do
  before(:each) do
    @supplier_1 = create(:travel_supplier, app: @app)
    @supplier_2 = create(:travel_supplier, app: @app)
    @activity_temp = create(:travel_activity_temp, duration: 10, app: @app)
    # @activity = create(:travel_activity, app: @app, activity_temp: @activity_temp)

    @offer_temp_1 = create(:travel_offer_temp, app: @app, supplier: @supplier_1, temp_parent: @activity_temp)
    @offer_temp_2 = create(:travel_offer_temp, app: @app, supplier: @supplier_1, temp_parent: @activity_temp)

    @demand_grp_temp = create(:travel_demand_grp_temp, app: @app, activity_temp: @activity_temp)
    @demand_temp_1 = create(:travel_demand_temp, offer_temp: nil,  start_offset: 0, duration: 1, app: @app, demand_grp_temp: @demand_grp_temp, activity_temp: @activity_temp)
    @demand_temp_2 = create(:travel_demand_temp, offer_temp: @offer_temp_1, start_offset: 1, duration: 1, app: @app, demand_grp_temp: @demand_grp_temp, activity_temp: @activity_temp)
  end

  it 'update new_offer_supplier_id' do
    @demand_temp_1.update!(new_offer_supplier_id: @supplier_2.id)

    expect(@demand_temp_1.offer_temp_id).not_to be_nil
    expect(@demand_temp_1.offer_temp.supplier).to eq(@supplier_2)
    expect(@demand_temp_1.offer_temp.id.in?([@offer_temp_1.id, @offer_temp_2.id])).to eq(false)

    @new_offer_temp_id = @demand_temp_1.offer_temp_id

    # 传入原有一样的 supplier，不会创建新的 offer
    @demand_temp_1.update!(new_offer_supplier_id: @supplier_2.id)
    expect(@demand_temp_1.reload.offer_temp_id).not_to be_nil

    # 新的 supplier，原有的 offer
    @demand_temp_2.update!(new_offer_supplier_id: @supplier_2.id)
    expect(@demand_temp_2.offer_temp_id).not_to be_nil

    # 删除空的 offer
    expect { @offer_temp_1.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end
end
