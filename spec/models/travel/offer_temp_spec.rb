require 'rails_helper'

RSpec.describe Travel::OfferTemp, type: :model do
  it { should have_one :demand_temp }
  it { should have_one :demand_grp_temp }
  it { should have_many :offers }
  it { should have_many :temp_entities }

  describe 'sync_amount_by_demands' do
    before do
      @activity_temp = create(:travel_activity_temp, duration: 10, app: @app)
      @offer_temp = create(:travel_offer_temp, app: @app, amount_sync: true, temp_parent: @activity_temp)
      @demand_temp = create(
        :travel_demand_temp,
        offer_temp: @offer_temp,
        offer_amount: 100,
        offer_amount_unit: 'CNY',
        app: @app,
      )
    end

    it 'should update amount and amount_unit' do
      expect(@offer_temp.amount).to eq(100)
      expect(@offer_temp.amount_unit).to eq('CNY')

      @demand_temp.update(offer_amount: 200, offer_amount_unit: 'USD')
      expect(@offer_temp.amount).to eq(200)
      expect(@offer_temp.amount_unit).to eq('USD')

      @offer_temp.update(amount_sync: false)
      @demand_temp.update(offer_amount: 300, offer_amount_unit: 'JPY')
      expect(@offer_temp.amount).to eq(200)
      expect(@offer_temp.amount_unit).to eq('USD')
    end
  end
end
