require 'rails_helper'

RSpec.describe Travel::Activity, type: :model do
  it { should belong_to :app }
  it { should belong_to(:creator).optional(true) }
  it { should belong_to(:activity_temp).optional(true) }
  it { should have_many :demand_grps }
  it { should have_many :demands }
  it { should have_many :days }
  it { should have_many :offers }

  before do
    @product = @travel_products.first
    expect(@product.demand_temps.count).to eq(0)

    @demand_temp = create(
      :travel_demand_temp,
      product: @product,
      start_offset: 1,
      end_offset: 2,
      duration: 2,
      app: @app
    )

    expect(@product.demand_temps.count).not_to eq(0)
  end

  it 'import_product_ids= successful' do
    @activity = @app.travel_activities.create!(
      start_at: Date.today,
      duration: 1,
      import_activity_temp_ids: [@travel_activity_temp.id],
    )

    expect(@activity.demand_grps.count).to eq(@travel_activity_temp.demand_grp_temps.count)
    expect(@activity.demands.count).to eq(@product.demand_temps.count)
    expect(@activity.reload.duration).to eq(@travel_activity_temp.duration)
    expect(@activity.days.count).to eq(@travel_activity_temp.duration)
  end

  describe 'import_activity_temp_ids=' do
    before do
      @activity_temp = create(
        :travel_activity_temp,
        duration: 4,
        app: @app,
      )

      expect(@activity_temp.day_temps.count).to eq(4)

      @demand_grp_temp = create(
        :travel_demand_grp_temp,
        activity_temp: @activity_temp,
        app: @app,
      )

      @demand_temp_1 = create(
        :travel_demand_temp,
        demand_grp_temp: @demand_grp_temp,
        start_position: 2,
        duration: 2,
        app: @app,
        count: 2,
        amount: 100,
        offer_amount: 50,
      )

      @supplier = create(:travel_supplier, app: @app)

      @offer_temp_1 = create(
        :travel_offer_temp,
        app: @app,
        activity_temp: @activity_temp,
        supplier: @supplier,
        amount_sync: true,
      )

      @demand_temp_1.update!(offer_temp: @offer_temp_1)
    end

    it 'successful' do
      @activity = @app.travel_activities.create!(
        start_at: Date.today,
        import_activity_temp_ids: [@activity_temp.id],
        import_start_position: 1,
        import_count: 2,
      )

      expect(@activity.demand_grps.count).to eq(1)
      expect(@activity.demands.count).to eq(1)

      @demand = @activity.demands.reload.first
      expect(@demand.start_position).to eq(2)
      expect(@demand.end_position).to eq(3)
      expect(@demand.count).to eq(2 * 2)
      expect(@demand.amount).to eq(2 * 100)
      # 单价
      expect(@demand.offer_amount).to eq(50)
      expect(@demand.offer_total_amount).to eq(2 * 2 * 50)

      expect(@demand.offer.amount_sync).to eq(true)
      expect(@demand.offer.amount).to eq(2 * 2 * 50)

      expect(@activity.reload.duration).to eq(4)
      expect(@activity.days.count).to eq(4)

      @activity.update!(
        import_activity_temp_ids: [@activity_temp.id],
        import_start_position: 2,
      )

      expect(@activity.reload.duration).to eq(5)
      expect(@activity.days.count).to eq(5)
      expect(@activity.demand_grps.count).to eq(2)
      expect(@activity.demands.count).to eq(2)
      @demand_grp = @activity.demand_grps.reload.unscope(:order).order(id: :asc).last
      @demand = @demand_grp.demands.reload.first

      expect(@demand.start_position).to eq(3)
      expect(@demand.end_position).to eq(4)

      expect(@activity.offers.reload.count).to eq(2)
      expect(@demand.offer.supplier_id).to eq(@supplier.id)

      @activity.update!(additional_amount: 1000)

      create(:travel_order, app: @app, activity_ids: [@activity.id], order_amount: 1000)

      @activity.offers.each do |offer|
        offer.update!(state: 'confirmed')
      end

      expect(@activity.reload.profit_amount).to eq(1000 - 1000 - 50 * (2 + 1) * 2 * 2)
    end

    it 'import_activity_temp_id= service_name exist successful' do
      @demand_grp_temp.update!(service_name: '成人')

      @activity = @app.travel_activities.create!(
        start_at: Date.today,
        import_activity_temp_ids: [@activity_temp.id],
        import_start_position: 1,
        import_count: 2,
      )

      expect(@activity.demand_grps.count).to eq(0)
      expect(@activity.demands.count).to eq(0)
      expect(@activity.offers.count).to eq(0)

      @demand_grp_temp.update!(service_name: '')

      @activity = @app.travel_activities.create!(
        start_at: Date.today,
        import_activity_temp_ids: [@activity_temp.id],
        import_start_position: 1,
        import_count: 2,
      )

      expect(@activity.demand_grps.count).not_to eq(0)
      expect(@activity.demands.count).not_to eq(0)
      expect(@activity.offers.count).not_to eq(0)
    end
  end
end
