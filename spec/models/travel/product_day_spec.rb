require 'rails_helper'

RSpec.describe Travel::ProductDay, type: :model do
  before(:each) do
    @product = create(:travel_product, duration: 10, app: @app)
    @product_day_1, @product_day_2, @product_day_3 = @product.product_days.first(3)
    @demand_temp_1 = create(:travel_demand_temp, start_offset: 0, duration: 1, app: @app, product: @product)
    @demand_temp_2 = create(:travel_demand_temp, start_offset: 1, duration: 1, app: @app, product: @product)
    @demand_temp_3 = create(:travel_demand_temp, start_offset: 2, duration: 1, app: @app, product: @product)
  end

  it 'move_one_product_day_demand_temp [1,2,3] => [2,1,3]' do
    @product_day_1.update!(position: 2)
    expect(@product_day_1.reload.position).to eq(2)
    expect(@product_day_2.reload.position).to eq(1)
    expect(@product_day_3.reload.position).to eq(3)
    # 移动 单日需求 成功
    expect(@demand_temp_1.reload.start_offset).to eq(1)
    # 移动 单日需求 成功
    expect(@demand_temp_2.reload.start_offset).to eq(0)
    # 不移动 单日需求 成功
    expect(@demand_temp_3.reload.start_offset).to eq(2)
  end

  it 'move_one_product_day_demand_temp [1,2,3] => [3,1,2]' do
    @product_day_3.update!(position: 1)
    expect(@product_day_1.reload.position).to eq(2)
    expect(@product_day_2.reload.position).to eq(3)
    expect(@product_day_3.reload.position).to eq(1)
    # 移动 单日需求 成功
    expect(@demand_temp_1.reload.start_offset).to eq(1)
    # 移动 单日需求 成功
    expect(@demand_temp_2.reload.start_offset).to eq(2)
    # 移动 单日需求 成功
    expect(@demand_temp_3.reload.start_offset).to eq(0)
  end

end
