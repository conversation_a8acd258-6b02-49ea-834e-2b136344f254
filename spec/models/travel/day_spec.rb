require 'rails_helper'

RSpec.describe Travel::Day, type: :model do
  before(:each) do
    @activity = create(:travel_activity, duration: 10, start_at: Date.today, app: @app)
    @day_1, @day_2, @day_3 = @activity.days.first(3)
    @demand_grp = create(:travel_demand_grp, app: @app, activity: @activity)
    @demand_1 = create(:travel_demand, start_offset: 0, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
    @demand_2 = create(:travel_demand, start_offset: 1, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
    @demand_3 = create(:travel_demand, start_offset: 2, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
  end

  it 'move_one_day_demand [1,2,3] => [2,1,3]' do
    @day_1.update!(position: 2)
    expect(@day_1.reload.date).to eq(Date.today + 1.day)
    expect(@day_2.reload.date).to eq(Date.today)
    expect(@day_3.reload.date).to eq(Date.today + 2.day)
    # 移动 单日需求 成功
    expect(@demand_1.reload.start_date).to eq(Date.today + 1.day)
    # 移动 单日需求 成功
    expect(@demand_2.reload.start_date).to eq(Date.today)
    # 不移动 单日需求 成功
    expect(@demand_3.reload.start_date).to eq(Date.today + 2.day)
  end

  it 'move_one_day_demand [1,2,3] => [3,1,2]' do
    @day_3.update!(position: 1)
    expect(@day_1.reload.date).to eq(Date.today + 1.day)
    expect(@day_2.reload.date).to eq(Date.today + 2.day)
    expect(@day_3.reload.date).to eq(Date.today + 0.day)
    # 移动 单日需求 成功
    expect(@demand_1.reload.start_date).to eq(Date.today + 1.day)
    # 移动 单日需求 成功
    expect(@demand_2.reload.start_date).to eq(Date.today + 2.day)
    # 移动 单日需求 成功
    expect(@demand_3.reload.start_date).to eq(Date.today + 0.day)
  end

  it 'city_ids & city_ids=' do
    @country = create(:travel_poi_country)
    @city_1 = create(:travel_poi_region, type: 'Travel::Poi::City', country: @country)
    @city_2 = create(:travel_poi_region, type: 'Travel::Poi::City', country: @country)
    ids = [@city_1.id, @city_2.id, @city_1.id]
    @day_1.city_ids = ids
    @day_1.save!
    expect(@day_1.reload.city_ids).to eq(ids)
  end
end
