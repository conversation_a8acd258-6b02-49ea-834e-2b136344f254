require 'rails_helper'

RSpec.describe Travel::Order, type: :model do
  it { should belong_to :app }
  it { should belong_to(:channel).optional(true) }
  it { should belong_to(:creator).optional(true) }
  it { should belong_to(:product).optional(true) }
  it { should have_many :order_divides }
  it { should have_many :ota_snapshots }
  it { should have_many :ota_skus }
  it { should have_many :revenues }
  it { should have_many :manual_revenues }
end
