require 'rails_helper'

RSpec.describe Travel::ActivityTemp, type: :model do
  it '模版 clone' do
    @activity_temp = create(:travel_activity_temp, app: @app, duration: 10)
    @demand_grp_temp = create(:travel_demand_grp_temp, app: @app, activity_temp: @activity_temp)

    @supplier_1 = create(:travel_supplier, app: @app)
    @supplier_2 = create(:travel_supplier, app: @app)

    @offer_temp_1 = create(:travel_offer_temp, name: 'offest_1', app: @app, activity_temp: @activity_temp, supplier: @supplier_1)
    @offer_temp_2 = create(:travel_offer_temp, name: 'offset_2', app: @app, activity_temp: @activity_temp, supplier: @supplier_2)

    @demand_temp_1 = create(
      :travel_demand_temp,
      app: @app,
      demand_grp_temp: @demand_grp_temp,
      start_offset: 0,
      duration: 2,
      offer_temp: @offer_temp_1,
    )

    @demand_temp_2 = create(
      :travel_demand_temp,
      app: @app,
      demand_grp_temp: @demand_grp_temp,
      start_offset: 8,
      duration: 2,
      offer_temp: @offer_temp_2,
      count: 2,
      amount: 100,
      offer_amount: 50,
    )

    @new_temp = @activity_temp.clone

    expect(@new_temp.duration).to eq(@activity_temp.duration)
    expect(@new_temp.demand_grp_temps.count).to eq(@activity_temp.demand_grp_temps.count)
    expect(@new_temp.offer_temps.count).to eq(@activity_temp.offer_temps.count)
    expect(@new_temp.demand_temps.count).to eq(@activity_temp.demand_temps.count)
    expect(@new_temp.demand_temps.count).to eq(2)
    @new_demand_temp_1 = @new_temp.demand_temps.first
    @new_demand_temp_2 = @new_temp.demand_temps.second

    expect(@new_demand_temp_1.start_offset).to eq(@demand_temp_1.start_offset)
    expect(@new_demand_temp_1.end_offset).to eq(@demand_temp_1.end_offset)
    expect(@new_demand_temp_1.offer_temp.name).to eq(@offer_temp_1.name)
    expect(@new_demand_temp_1.offer_temp.id).not_to eq(@offer_temp_1.id)
    expect(@new_demand_temp_1.count).to eq(@demand_temp_1.count)
    expect(@new_demand_temp_1.amount).to eq(@demand_temp_1.amount)
    expect(@new_demand_temp_1.offer_amount).to eq(@demand_temp_1.offer_amount)

    expect(@new_demand_temp_2.offer_temp.name).to eq(@offer_temp_2.name)
    expect(@new_demand_temp_2.offer_temp.id).not_to eq(@offer_temp_2.id)
  end

  # it '模版生成具体内容' do
  #   @activity_temp = create(:travel_activity_temp, app: @app, duration: 10)
  #   @demand_grp_temp = create(:travel_demand_grp_temp, app: @app, activity_temp: @activity_temp)

  #   @offer_temp_1 = create(:travel_offer_temp, app: @app, activity_temp: @activity_temp)
  #   @offer_temp_2 = create(:travel_offer_temp, app: @app, activity_temp: @activity_temp)

  #   @demand_temp_1 = create(
  #     :travel_demand_temp,
  #     app: @app,
  #     demand_grp_temp: @demand_grp_temp,
  #     start_offset: 0,
  #     duration: 2,
  #     offer_temp: @offer_temp_1,
  #   )

  #   @demand_temp_2 = create(
  #     :travel_demand_temp,
  #     app: @app,
  #     demand_grp_temp: @demand_grp_temp,
  #     start_offset: 8,
  #     duration: 2,
  #     offer_temp: @offer_temp_2,
  #     count: 2,
  #     amount: 100,
  #     offer_amount: 50,
  #   )

  #   @activity = @activity_temp.generate_entity(start_at: '2024-01-01', import_count: 2)

  #   expect(@activity.end_at).to eq('2024-01-10'.to_date)
  #   @new_demand_1 = @activity.demands.find_by(demand_temp: @demand_temp_1)
  #   @new_demand_2 = @activity.demands.find_by(demand_temp: @demand_temp_2)

  #   expect(@new_demand_1.start_date).to eq('2024-01-01'.to_date)
  #   expect(@new_demand_1.end_date).to eq('2024-01-02'.to_date)
  #   expect(@offer_temp_1.offers.last.id).not_to be_nil
  #   expect(@new_demand_1.offer_id).to eq(@offer_temp_1.offers.last.id)
  #   expect(@new_demand_1.count).to eq(0)
  #   expect(@new_demand_1.amount).to eq(0)
  #   expect(@new_demand_1.offer_amount).to eq(0)

  #   expect(@new_demand_2.start_date).to eq('2024-01-9'.to_date)
  #   expect(@new_demand_2.end_date).to eq('2024-01-10'.to_date)
  #   expect(@offer_temp_2.offers.last.id).not_to be_nil
  #   expect(@new_demand_2.offer_id).to eq(@offer_temp_2.offers.last.id)
  #   expect(@new_demand_2.count).to eq(2 * 2)
  #   expect(@new_demand_2.amount).to eq(100 * 2)
  #   expect(@new_demand_2.offer_amount).to eq(50 * 2)
  # end

  # it '模版生成具体内容' do
  #   @new_activity = @travel_activity_temp.generate_entity(
  #     start_at: Date.today,
  #   )
  #   expect(@new_activity.duration).to eq(@travel_activity_temp.duration)
  #   expect(@new_activity.start_at).to eq(Date.today)
  #   expect(@new_activity.end_at).to eq(Date.today + (@travel_activity_temp.duration - 1).days)

  #   expect(@new_activity.demand_grps.count).to eq(@travel_activity_temp.demand_grp_temps.count)
  #   expect(@new_activity.demands.count).to eq(@travel_activity_temp.demand_temps.count)

  #   expect(@new_activity.demands.first.start_date).to eq(@new_activity.start_at)
  #   expect(@new_activity.demands.first.end_date).to eq(@new_activity.start_at + 1.days)

  #   expect(@new_activity.days.count).to eq(12)
  #   @day_1, @day_2 = @new_activity.days.first(2)
  #   expect(@day_1.date).to eq(@new_activity.start_at)
  #   expect(@day_2.date).to eq(@new_activity.start_at + 1.day)

  #   @day_2.update!(position: 1)

  #   expect(@day_1.reload.position).to eq(2)
  #   expect(@day_1.reload.date).to eq(@new_activity.start_at + 1.day)

  #   expect(@day_2.reload.position).to eq(1)
  #   expect(@day_2.reload.date).to eq(@new_activity.start_at)
  # end
end
