require 'rails_helper'

RSpec.describe Travel::Demand, type: :model do
  before(:each) do
    @supplier_1 = create(:travel_supplier, app: @app)
    @supplier_2 = create(:travel_supplier, app: @app)
    @activity = create(:travel_activity, duration: 10, start_at: Date.today, app: @app)

    @payment_1 = create(:travel_payment, app: @app, state: 'pending', supplier: @supplier_1)
    @payment_2 = create(:travel_payment, app: @app, state: 'pending', supplier: @supplier_1)

    @offer_1 = create(:travel_offer, app: @app, state: 'pending', pay_state: 'unpaid', supplier: @supplier_1, payment: @payment_1)
    @offer_2 = create(:travel_offer, app: @app, state: 'pending', pay_state: 'unpaid', supplier: @supplier_1, payment: @payment_2)

    @demand_grp = create(:travel_demand_grp, app: @app, activity: @activity)
    @demand_1 = create(:travel_demand, offer: nil,  start_offset: 0, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
    @demand_2 = create(:travel_demand, offer: @offer_1, start_offset: 1, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
    @demand_3 = create(:travel_demand, offer: @offer_2, start_offset: 2, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
  end

  it 'update new_offer_supplier_id' do
    # 新的 supplier，新的 offer
    @demand_1.update!(new_offer_supplier_id: @supplier_2.id)

    expect(@demand_1.offer_id).not_to be_nil
    expect(@demand_1.offer.supplier).to eq(@supplier_2)
    expect(@demand_1.offer.id.in?([@offer_1.id, @offer_2.id])).to eq(false)

    @new_offer_id = @demand_1.offer_id

    # 传入原有一样的 supplier，不会创建新的 offer
    @demand_1.update!(new_offer_supplier_id: @supplier_2.id)
    expect(@demand_1.reload.offer_id).not_to be_nil

    # 新的 supplier，原有的 offer
    @demand_2.update!(new_offer_supplier_id: @supplier_2.id)
    expect(@demand_2.offer_id).not_to be_nil

    # 删除空的 offer
    expect { @offer_1.reload }.to raise_error(ActiveRecord::RecordNotFound)
    # offer 的支付单已确认，不可删除
    @payment_2.update!(state: 'unpaid')
    expect { @demand_3.update!(new_offer_supplier_id: @supplier_2.id) }.to raise_error(Error::BaseError)
  end
end
