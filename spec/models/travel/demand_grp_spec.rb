require 'rails_helper'

RSpec.describe Travel::DemandGrp, type: :model do
  before(:each) do
    @supplier = @app.orgs.first_or_create!(name: 'supplier')
    @activity = create(:travel_activity, duration: 10, start_at: Date.today, app: @app)
    @offer_1 = create(:travel_offer, app: @app, state: 'pending', pay_state: 'unpaid', supplier: @supplier)
    @offer_2 = create(:travel_offer, app: @app, state: 'confirmed', pay_state: 'unpaid', supplier: @supplier)
    @offer_3 = create(:travel_offer, app: @app, state: 'confirmed', pay_state: 'paid', supplier: @supplier)

    @demand_grp = create(:travel_demand_grp, app: @app, activity: @activity)
    @demand_1 = create(:travel_demand, offer: @offer_1, start_offset: 0, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
    @demand_2 = create(:travel_demand, offer: @offer_2, start_offset: 2, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
    @demand_3 = create(:travel_demand, offer: @offer_3, start_offset: 2, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)
  end

  it 'demand_grp offer_state_info' do
    expect(@demand_grp.offer_state_info).to eq({
      'pending' => 1,
      'confirmed' => 2,
      :count => 3
    })
  end

  it 'demand_grp offer_pay_state_info' do
    expect(@demand_grp.offer_pay_state_info).to eq({
      'unpaid' => 2,
      'paid' => 1,
    })
  end

  it 'new_offer_supplier_id=' do
    @demand_6 = create(:travel_demand, start_offset: 2, duration: 1, app: @app, demand_grp: @demand_grp, activity: @activity)

    @demand_6.update!(new_offer_supplier_id: @supplier.id)
    @demand_6 = @demand_6.reload

    expect(@demand_6.offer_id).not_to be_nil
    expect(@demand_6.offer.supplier).to eq(@supplier)

    @supplier_2 = @app.orgs.first_or_create!(name: 'supplier_2')
    @demand_6.update!(new_offer_supplier_id: @supplier_2.id)

    expect(@demand_6.offer.supplier).to eq(@supplier_2)
  end
end
