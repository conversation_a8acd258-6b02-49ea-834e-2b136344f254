# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_06_17_094933) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "vector"

  create_table "actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_actions_on_app_id"
    t.index ["real_user_id"], name: "index_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_actions_on_user"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "record_type"
    t.bigint "record_id"
    t.bigint "blob_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "idx_on_record_type_record_id_name_blob_id_0be5805727", unique: true
    t.index ["record_type", "record_id"], name: "index_active_storage_attachments_on_record"
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.jsonb "metadata", comment: "额外信息"
    t.string "app_code", comment: "app标识"
    t.string "key", comment: "key"
    t.string "filename", comment: "文件名称"
    t.string "content_type", comment: "文件类型"
    t.string "service_name", comment: "服务名称"
    t.integer "byte_size", comment: "文件大小"
    t.string "checksum", comment: "校验位"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "api_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.bigint "app_id"
    t.string "klass", comment: "对应的active record class name"
    t.string "action", comment: "对应controller的action"
    t.string "uid", comment: "自动生成的唯一标识"
    t.jsonb "extract_conf", comment: "数据抽取配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_api_settings_on_app_id"
    t.index ["model_define_id"], name: "index_api_settings_on_model_define_id"
  end

  create_table "apps", force: :cascade do |t|
    t.string "code", comment: "应用标识"
    t.string "name", comment: "应用的名称"
    t.jsonb "settings", comment: "配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "async_tasks", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "taskable_type"
    t.bigint "taskable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI属性"
    t.string "flag", comment: "程序使用参数，唯一标识，前端配合使用"
    t.string "name", comment: "任务名称"
    t.integer "progress", comment: "进度(取整数)"
    t.string "state"
    t.string "perform_args", comment: "执行参数"
    t.jsonb "options", comment: "启动执行参数"
    t.jsonb "payload", comment: "处理信息"
    t.jsonb "result", comment: "异步处理的结果信息"
    t.jsonb "meta", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_async_tasks_on_app_id"
    t.index ["taskable_type", "taskable_id"], name: "index_async_tasks_on_taskable"
    t.index ["user_id"], name: "index_async_tasks_on_user_id"
  end

  create_table "bpm_catalogs", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "icon", comment: "图标"
    t.integer "position", comment: "排序"
    t.boolean "published", comment: "是否发布"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_catalogs_on_app_id"
  end

  create_table "bpm_instance_relations", force: :cascade do |t|
    t.bigint "instance_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "随机数"
    t.string "model_setting_flag", comment: "对应模型的flag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["instance_id"], name: "index_bpm_instance_relations_on_instance_id"
    t.index ["source_type", "source_id"], name: "index_bpm_instance_relations_on_source"
  end

  create_table "bpm_instances", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "creator_id"
    t.string "flowable_type"
    t.bigint "flowable_id"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI"
    t.jsonb "payload", comment: "流程表单"
    t.jsonb "storage", comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容"
    t.jsonb "summary", comment: "instance在列表页显示的内容"
    t.string "state", comment: "流程状态"
    t.string "flowable_flag", comment: "flowable不同流程的flag"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "cache_payload", comment: "额外存储的结构，根据场合可以作为payload的存储"
    t.datetime "action_at", comment: "激活时间"
    t.jsonb "last_token_attr", comment: "最新token信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "comments_count", comment: "评论数量"
    t.string "comment_conf"
    t.index ["app_id"], name: "index_bpm_instances_on_app_id"
    t.index ["creator_id"], name: "index_bpm_instances_on_creator_id"
    t.index ["flowable_type", "flowable_id"], name: "index_bpm_instances_on_flowable"
    t.index ["workflow_id"], name: "index_bpm_instances_on_workflow_id"
  end

  create_table "bpm_place_relations", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_id"], name: "index_bpm_place_relations_on_source_id"
    t.index ["target_id"], name: "index_bpm_place_relations_on_target_id"
    t.index ["workflow_id"], name: "index_bpm_place_relations_on_workflow_id"
  end

  create_table "bpm_places", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "type", comment: "STI"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "desc", comment: "节点描述"
    t.integer "position", comment: "根据 tree 边生成的 position"
    t.boolean "is_summary", comment: "是否快捷引用"
    t.jsonb "fields", comment: "workflow form字段在这个place的权限，读写/可见"
    t.jsonb "place_form", comment: "动态表单的json字段"
    t.jsonb "options", comment: "节点的配置信息"
    t.jsonb "timer_options", comment: "节点时间配置"
    t.jsonb "trigger_options", comment: "token进出节点时候可能需要额外操作的内容"
    t.jsonb "token_actions", comment: "操作菜单配置"
    t.jsonb "layout_options", comment: "前端页面使用的配置"
    t.jsonb "activate_options", comment: "同步回调配置"
    t.jsonb "token_source_options", comment: "token place相关配置"
    t.jsonb "form_setting", comment: "表单配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_places_on_workflow_id"
  end

  create_table "bpm_rules", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "name", comment: "规则名称"
    t.integer "time_in_second", comment: "设定时间范围"
    t.string "type", comment: "STI"
    t.jsonb "options", comment: "具体配置内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_rules_on_workflow_id"
  end

  create_table "bpm_stars", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "workflow_id"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bpm_stars_on_user_id"
    t.index ["workflow_id"], name: "index_bpm_stars_on_workflow_id"
  end

  create_table "bpm_tokens", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "place_id"
    t.bigint "previous_token_id"
    t.bigint "operator_id"
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "instance_id"
    t.string "activate_source_type"
    t.bigint "activate_source_id"
    t.string "token_source_type"
    t.bigint "token_source_id"
    t.string "type", comment: "STI"
    t.string "place_type", comment: "Place的类型"
    t.string "transition_type", comment: "Transition的类型"
    t.string "name", comment: "Token的名称，默认取自Place"
    t.string "state", comment: "Token状态"
    t.text "comment", comment: "审批备注"
    t.jsonb "options", comment: "Token的额外信息JSON"
    t.jsonb "token_payload", comment: "对应place的place_payload，存储审批时候存储在tokne中的信息"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "operate_logs", comment: "相关的操作日志"
    t.string "action_key", comment: "保存上一个action的操作"
    t.string "action_flag", comment: "保存action的操作flag，action key有可能是重复的，通过action_flag来做区分"
    t.integer "timestamp", comment: "时间戳，当前批次"
    t.datetime "action_at", comment: "激活时间"
    t.index ["activate_source_type", "activate_source_id"], name: "index_bpm_tokens_on_activate_source"
    t.index ["app_id"], name: "index_bpm_tokens_on_app_id"
    t.index ["instance_id"], name: "index_bpm_tokens_on_instance_id"
    t.index ["operator_id"], name: "index_bpm_tokens_on_operator_id"
    t.index ["place_id"], name: "index_bpm_tokens_on_place_id"
    t.index ["previous_token_id"], name: "index_bpm_tokens_on_previous_token_id"
    t.index ["token_source_type", "token_source_id"], name: "index_bpm_tokens_on_token_source"
    t.index ["workflow_id"], name: "index_bpm_tokens_on_workflow_id"
  end

  create_table "bpm_transitions", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "place_id"
    t.string "type", comment: "STI"
    t.jsonb "callback_options", comment: "回调设置"
    t.jsonb "options", comment: "transition跳转的额外设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["place_id"], name: "index_bpm_transitions_on_place_id"
    t.index ["workflow_id"], name: "index_bpm_transitions_on_workflow_id"
  end

  create_table "bpm_workflow_relations", force: :cascade do |t|
    t.string "workflowable_type"
    t.bigint "workflowable_id"
    t.bigint "workflow_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_workflow_relations_on_workflow_id"
    t.index ["workflowable_type", "workflowable_id"], name: "index_bpm_workflow_relations_on_workflowable"
  end

  create_table "bpm_workflows", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "mod_id"
    t.bigint "catalog_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "流程名称"
    t.text "desc", comment: "流程描述"
    t.jsonb "icon", comment: "流程图标"
    t.jsonb "cover_image", comment: "流程封面"
    t.string "state"
    t.integer "position", comment: "catalog内排序"
    t.string "instance_type", comment: "自动生成的instance_type"
    t.string "classify"
    t.jsonb "form", comment: "表单配置 "
    t.jsonb "meta", comment: "工作流额外配置信息 "
    t.jsonb "token_actions", comment: "操作菜单配置"
    t.jsonb "trigger_options", comment: "instance状态改变时候需要额外操作的内容"
    t.boolean "auto_complete_same_handle_token", comment: "是否跳过连续相同的审批人"
    t.jsonb "submit_options", comment: "限制条件"
    t.jsonb "form_setting", comment: "表单配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_workflows_on_app_id"
    t.index ["catalog_id"], name: "index_bpm_workflows_on_catalog_id"
    t.index ["creator_id"], name: "index_bpm_workflows_on_creator_id"
    t.index ["mod_id"], name: "index_bpm_workflows_on_mod_id"
  end

  create_table "com_private_policies", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "条款名称"
    t.string "key", comment: "关键字，可能有不同业务模块需要使用的关键字"
    t.jsonb "content", comment: "隐私条款内容"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_private_policies_on_app_id"
  end

  create_table "com_record_storages", force: :cascade do |t|
    t.bigint "user_id"
    t.string "key", comment: "缓存区key"
    t.jsonb "storage", comment: "属性暂存区"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_com_record_storages_on_user_id"
  end

  create_table "com_search_items", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "搜索条件 "
    t.integer "position", comment: "位置"
    t.string "group_name", comment: "分组标识"
    t.boolean "enabled", comment: "是否启用"
    t.jsonb "conditions", comment: "具体ransack搜索条件"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_search_items_on_app_id"
  end

  create_table "com_themes", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "主题名称"
    t.jsonb "conf", comment: "主题配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_themes_on_app_id"
  end

  create_table "com_version_histories", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "版本发布名称"
    t.string "version", comment: "版本号"
    t.jsonb "content", comment: "发布说明"
    t.integer "position", comment: "发布顺序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_version_histories_on_app_id"
    t.index ["creator_id"], name: "index_com_version_histories_on_creator_id"
  end

  create_table "comments", force: :cascade do |t|
    t.string "commentable_type"
    t.bigint "commentable_id"
    t.bigint "user_id"
    t.string "title", comment: "标题"
    t.text "subject", comment: "简述"
    t.text "body", comment: "内容"
    t.jsonb "attachments", comment: "附件"
    t.integer "parent_id", comment: "父节点"
    t.integer "lft", comment: "左节点"
    t.integer "rgt", comment: "右节点"
    t.integer "depth", comment: "层级"
    t.integer "children_count", comment: "子评论数量"
    t.integer "position", comment: "排序"
    t.integer "likes_count", comment: "点赞次数"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "component_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组件配置名称"
    t.string "component_klass", comment: "组件类名称"
    t.string "component_path", comment: "组件类路径"
    t.jsonb "conf", comment: "组件配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_component_settings_on_app_id"
  end

  create_table "data_counter_stats", force: :cascade do |t|
    t.string "countable_type"
    t.bigint "countable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.date "date", comment: "日期，如果是年、月则存第一天"
    t.integer "hour", comment: "小时"
    t.string "period"
    t.integer "view_count", comment: "浏览量"
    t.integer "action_count", comment: "使用量"
    t.integer "user_count", comment: "用户量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["countable_type", "countable_id"], name: "index_data_counter_stats_on_countable"
  end

  create_table "data_forms", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "create_user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "record_type"
    t.bigint "record_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "可用作同一 source 下不同的关联关系的区分"
    t.string "source_flag", comment: "关联source的flag"
    t.string "state", comment: "数据状态"
    t.jsonb "payload", comment: "存储的信息"
    t.jsonb "summary", comment: "通过form生成的缩略信息"
    t.jsonb "form_conf", comment: "表单的配置，里面支持多态的方式"
    t.jsonb "options", comment: "额外的数据信息"
    t.jsonb "meta", comment: "预留后续的数据存储"
    t.string "form_conf_seq", comment: "表单配置的seq，方便进行检索"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_forms_on_app_id"
    t.index ["create_user_id"], name: "index_data_forms_on_create_user_id"
    t.index ["record_type", "record_id"], name: "index_data_forms_on_record"
    t.index ["source_type", "source_id"], name: "index_data_forms_on_source"
  end

  create_table "data_scopes", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.jsonb "config", comment: "配置"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_scopes_on_app_id"
    t.index ["tanent_id"], name: "index_data_scopes_on_tanent_id"
    t.index ["user_id"], name: "index_data_scopes_on_user_id"
  end

  create_table "data_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "op", comment: "操作"
    t.jsonb "infos", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_transfers_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_transfers_on_source"
    t.index ["target_type", "target_id"], name: "index_data_transfers_on_target"
  end

  create_table "data_view_logs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "view_at", comment: "最新访问时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_view_logs_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_view_logs_on_source"
    t.index ["user_id"], name: "index_data_view_logs_on_user_id"
  end

  create_table "department_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "department_anc_desc_idx", unique: true
  end

  create_table "department_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "部门类型名称"
    t.string "department_type", comment: "Department的类型，可能会关系到Department的STI"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_department_identities_on_app_id"
  end

  create_table "departments", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "root_org_id"
    t.bigint "department_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_identity_id"], name: "index_departments_on_department_identity_id"
    t.index ["org_id"], name: "index_departments_on_org_id"
    t.index ["root_org_id"], name: "index_departments_on_root_org_id"
  end

  create_table "duties", force: :cascade do |t|
    t.bigint "duty_group_id"
    t.bigint "org_id"
    t.bigint "department_id"
    t.string "name", comment: "职务名称"
    t.string "rank", comment: "职务等级"
    t.integer "position", comment: "排序"
    t.string "code", comment: "岗位标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_duties_on_department_id"
    t.index ["duty_group_id"], name: "index_duties_on_duty_group_id"
    t.index ["org_id"], name: "index_duties_on_org_id"
  end

  create_table "duty_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "name", comment: "角色组名称"
    t.integer "position", comment: "排序"
    t.string "code", comment: "岗位组标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_duty_groups_on_app_id"
    t.index ["org_id"], name: "index_duty_groups_on_org_id"
  end

  create_table "favor_folders", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "收藏夹名称"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "content", comment: "详情"
    t.integer "position", comment: "排序"
    t.jsonb "option", comment: "额外配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_favor_folders_on_app_id"
    t.index ["user_id"], name: "index_favor_folders_on_user_id"
  end

  create_table "favor_mark_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "favor_mark_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_favor_mark_actions_on_app_id"
    t.index ["real_user_id"], name: "index_favor_mark_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "favor_mark_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_favor_mark_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "favor_mark_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_favor_mark_actions_on_user"
  end

  create_table "forms_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.string "uuid", comment: "表单的唯一标识，可以替代id给前端使用"
    t.string "name", comment: "表单的名称"
    t.jsonb "form", comment: "表单配置的内容"
    t.jsonb "form_setting", comment: "表单内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_forms_templates_on_app_id"
  end

  create_table "member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.string "name", comment: "身份名称"
    t.string "member_type", comment: "Member的类型"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.jsonb "form", comment: "Member配置的表单"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_member_identities_on_app_id"
    t.index ["org_id"], name: "index_member_identities_on_org_id"
  end

  create_table "member_identity_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "member_identity_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_member_identity_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_member_identity_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "member_identity_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_member_identity_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "member_identity_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_member_identity_permit_actions_on_user"
  end

  create_table "member_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.string "create_instance_state", comment: "状态"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "请求名称"
    t.jsonb "payload", comment: "相关信息，会存储到member的payload里"
    t.jsonb "member_attributes", comment: "相关信息，会存储到member的attributes里"
    t.jsonb "options", comment: "加入什么组织和岗位，相关配置"
    t.string "state", comment: "状态"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_member_requests_on_app_id"
    t.index ["member_identity_id"], name: "index_member_requests_on_member_identity_id"
    t.index ["user_id"], name: "index_member_requests_on_user_id"
  end

  create_table "members", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "app_id"
    t.bigint "member_request_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.string "code", comment: "用户标识"
    t.datetime "blocked_at"
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_members_on_app_id"
    t.index ["member_identity_id"], name: "index_members_on_member_identity_id"
    t.index ["member_request_id"], name: "index_members_on_member_request_id"
    t.index ["user_id"], name: "index_members_on_user_id"
  end

  create_table "memberships", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.bigint "department_id"
    t.bigint "duty_id"
    t.datetime "effective_at", comment: "生效时间，可以为空"
    t.datetime "invalid_at", comment: "失效时间，可以为空"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_memberships_on_app_id"
    t.index ["department_id"], name: "index_memberships_on_department_id"
    t.index ["duty_id"], name: "index_memberships_on_duty_id"
    t.index ["member_id"], name: "index_memberships_on_member_id"
    t.index ["org_id"], name: "index_memberships_on_org_id"
    t.index ["user_id"], name: "index_memberships_on_user_id"
  end

  create_table "model_confs", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "name", comment: "名称"
    t.string "klass", comment: "类名"
    t.jsonb "conf", comment: "具体配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["model_define_id"], name: "index_model_confs_on_model_define_id"
  end

  create_table "model_defines", force: :cascade do |t|
    t.string "klass", comment: "对应设置的Model名称"
    t.string "name", comment: "模型设置的中文名"
    t.string "association_chain"
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "model_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "setable_type"
    t.bigint "setable_id"
    t.bigint "app_id"
    t.bigint "forms_template_id"
    t.string "flag", comment: "同一个模型中的不同定义，其中model代表是这个对象的模型"
    t.string "flag_name", comment: "flag对应中文名称"
    t.jsonb "form", comment: "可以直接定义表单"
    t.jsonb "form_setting", comment: "表单结构"
    t.jsonb "api_config", comment: "API Config"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "bpm_workflow_id"
    t.bigint "ref_model_setting_id"
    t.string "ref_model_setting_flag", comment: "关联model_setting_flag"
    t.index ["app_id"], name: "index_model_settings_on_app_id"
    t.index ["bpm_workflow_id"], name: "index_model_settings_on_bpm_workflow_id"
    t.index ["forms_template_id"], name: "index_model_settings_on_forms_template_id"
    t.index ["model_define_id"], name: "index_model_settings_on_model_define_id"
    t.index ["ref_model_setting_id"], name: "index_model_settings_on_ref_model_setting_id"
    t.index ["setable_type", "setable_id"], name: "index_model_settings_on_setable"
  end

  create_table "mods", force: :cascade do |t|
    t.string "name", comment: "模块名称"
    t.string "key", comment: "模块对应查找的key值"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "notify_info_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "create_user_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "发送标识，保证唯一性"
    t.string "title", comment: "发送标题"
    t.string "content", comment: "发送内容"
    t.jsonb "meta", comment: "额外信息"
    t.string "url", comment: "链接地址"
    t.datetime "read_at"
    t.boolean "is_read"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_notify_info_messages_on_app_id"
    t.index ["create_user_id"], name: "index_notify_info_messages_on_create_user_id"
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_info_messages_on_notifyable"
    t.index ["user_type", "user_id"], name: "index_notify_info_messages_on_user"
  end

  create_table "notify_like_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "notify_like_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_notify_like_actions_on_app_id"
    t.index ["real_user_id"], name: "index_notify_like_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "notify_like_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_notify_like_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "notify_like_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_notify_like_actions_on_user"
  end

  create_table "notify_sms_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.string "mobile", comment: "发送手机号"
    t.string "title", comment: "发送标题"
    t.string "account", comment: "发送账号"
    t.string "content", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_sms_messages_on_notifyable"
  end

  create_table "notify_template_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "type", comment: "STI属性"
    t.string "state", comment: "状态"
    t.string "oauth_app_id", comment: "微信服务名称的标识"
    t.string "openid", comment: "微信发送的openid"
    t.jsonb "body", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_notify_template_messages_on_app_id"
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_notify_template_messages_on_user_id"
  end

  create_table "notify_wechat_template_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "oauth_app_id", comment: "微信服务名称的标识"
    t.string "openid", comment: "微信发送的openid"
    t.jsonb "message", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_wechat_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_notify_wechat_template_messages_on_user_id"
  end

  create_table "org_clients", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "client_type"
    t.bigint "client_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_clients_on_app_id"
    t.index ["client_type", "client_id"], name: "index_org_clients_on_client"
    t.index ["org_id"], name: "index_org_clients_on_org_id"
  end

  create_table "org_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "org_anc_desc_idx", unique: true
  end

  create_table "org_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "组织类型名称"
    t.string "org_type", comment: "Org的类型，可能会关系到Org的STI"
    t.integer "orgs_count", comment: "关联的Org数量"
    t.jsonb "form", comment: "Member配置的表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_identities_on_app_id"
  end

  create_table "org_member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "code", comment: "标识"
    t.string "org_member_type", comment: "OrgMember的类型"
    t.jsonb "settle_in_form", comment: "入驻申请表单"
    t.jsonb "postpone_form", comment: "延期申请表单"
    t.jsonb "form", comment: "表单"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_member_identities_on_app_id"
  end

  create_table "org_members", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.date "effective_at", comment: "生效时间"
    t.date "invalid_at", comment: "失效时间"
    t.string "type", comment: "STI类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_members_on_app_id"
    t.index ["org_id"], name: "index_org_members_on_org_id"
    t.index ["org_member_identity_id"], name: "index_org_members_on_org_member_identity_id"
  end

  create_table "org_ownerships", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["org_id"], name: "index_org_ownerships_on_org_id"
    t.index ["user_id"], name: "index_org_ownerships_on_user_id"
  end

  create_table "org_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "member_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.bigint "org_member_id"
    t.bigint "org_identity_id"
    t.bigint "tanent_id"
    t.string "create_instance_state", comment: "状态"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组织名称"
    t.string "code", comment: "组织标识"
    t.jsonb "org_payload", comment: "相关信息，会存储到org的payload里"
    t.jsonb "member_payload", comment: "相关信息，会存储到member的payload里"
    t.string "type", comment: "STI"
    t.string "state", comment: "状态: draft, approving"
    t.datetime "approval_at", comment: "审批通过时间"
    t.jsonb "options", comment: "其他预留信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_org_requests_on_app_id"
    t.index ["member_id"], name: "index_org_requests_on_member_id"
    t.index ["member_identity_id"], name: "index_org_requests_on_member_identity_id"
    t.index ["org_id"], name: "index_org_requests_on_org_id"
    t.index ["org_identity_id"], name: "index_org_requests_on_org_identity_id"
    t.index ["org_member_id"], name: "index_org_requests_on_org_member_id"
    t.index ["org_member_identity_id"], name: "index_org_requests_on_org_member_identity_id"
    t.index ["tanent_id"], name: "index_org_requests_on_tanent_id"
    t.index ["user_id"], name: "index_org_requests_on_user_id"
  end

  create_table "orgs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_orgs_on_app_id"
    t.index ["org_identity_id"], name: "index_orgs_on_org_identity_id"
  end

  create_table "page_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "页面配置名称"
    t.jsonb "conf", comment: "页面配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_page_settings_on_app_id"
  end

  create_table "paper_trail_versions", force: :cascade do |t|
    t.string "operator_type"
    t.bigint "operator_id"
    t.string "item_type"
    t.integer "item_id"
    t.string "event", comment: "create, update, destroy"
    t.string "whodunnit", comment: "whodunnit"
    t.jsonb "object", comment: "object attributes"
    t.jsonb "object_changes", comment: "object changes"
    t.jsonb "controller_info", comment: "controller info"
    t.jsonb "model_info", comment: "model info"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_type", "item_id"], name: "index_versions_on_item_id_item_type"
    t.index ["operator_type", "operator_id"], name: "index_paper_trail_versions_on_operator"
  end

  create_table "pay_channels", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "merchant_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "渠道名称"
    t.string "type", comment: "渠道STI类型，包括 Pay::Channels::WxChannel Pay::Channels::WxChannelAlipayChannel"
    t.string "payment_type", comment: "支付渠道内的子类型 STI，例如公众号支付小程序支付等"
    t.jsonb "config", comment: "渠道配置参数"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_pay_channels_on_app_id"
    t.index ["merchant_id"], name: "index_pay_channels_on_merchant_id"
  end

  create_table "pay_merchant_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "pay_merchant_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_pay_merchant_actions_on_app_id"
    t.index ["real_user_id"], name: "index_pay_merchant_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "pay_merchant_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_pay_merchant_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "pay_merchant_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_pay_merchant_actions_on_user"
  end

  create_table "pay_merchants", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "商户名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_pay_merchants_on_app_id"
  end

  create_table "pay_payments", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "merchant_id"
    t.bigint "channel_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "user_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "state"
    t.string "name", comment: "订单说明(subject)"
    t.string "body", comment: "订单详细说明"
    t.string "batch", comment: "订单批次概念"
    t.string "type", comment: "Payment STI"
    t.decimal "amount", comment: "金额"
    t.string "client_ip"
    t.jsonb "payload", comment: "订单提交的额外信息"
    t.jsonb "meta", comment: "订单处理需要的返回信息"
    t.decimal "refund_amount", comment: "已退款金额"
    t.datetime "pay_at", comment: "支付时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_pay_payments_on_app_id"
    t.index ["channel_id"], name: "index_pay_payments_on_channel_id"
    t.index ["merchant_id"], name: "index_pay_payments_on_merchant_id"
    t.index ["source_type", "source_id"], name: "index_pay_payments_on_source"
    t.index ["user_id"], name: "index_pay_payments_on_user_id"
  end

  create_table "pay_refunds", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "merchant_id"
    t.bigint "channel_id"
    t.bigint "payment_id"
    t.bigint "user_id"
    t.bigint "transfer_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "state"
    t.string "name", comment: "退款说明"
    t.decimal "amount", comment: "金额"
    t.string "type", comment: "STI"
    t.datetime "pay_at", comment: "支付时间"
    t.jsonb "meta", comment: "订单处理需要的返回信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_pay_refunds_on_app_id"
    t.index ["channel_id"], name: "index_pay_refunds_on_channel_id"
    t.index ["merchant_id"], name: "index_pay_refunds_on_merchant_id"
    t.index ["payment_id"], name: "index_pay_refunds_on_payment_id"
    t.index ["transfer_id"], name: "index_pay_refunds_on_transfer_id"
    t.index ["user_id"], name: "index_pay_refunds_on_user_id"
  end

  create_table "pay_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "payment_id"
    t.string "from_source_type"
    t.bigint "from_source_id"
    t.bigint "merchant_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "name", comment: "金额转移说明"
    t.decimal "max_amount", comment: "金额上限"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_pay_transfers_on_app_id"
    t.index ["from_source_type", "from_source_id"], name: "index_pay_transfers_on_from_source"
    t.index ["merchant_id"], name: "index_pay_transfers_on_merchant_id"
    t.index ["payment_id"], name: "index_pay_transfers_on_payment_id"
    t.index ["source_type", "source_id"], name: "index_pay_transfers_on_source"
  end

  create_table "permit_permissions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.bigint "route_setting_id"
    t.string "platform", comment: "平台"
    t.string "aname", comment: "action名称"
    t.string "cname", comment: "controller名称"
    t.string "klass", comment: "controller"
    t.string "action", comment: "action"
    t.jsonb "whitelist"
    t.jsonb "blacklist"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.string "key"
    t.index ["app_id"], name: "index_permit_permissions_on_app_id"
    t.index ["mod_id"], name: "index_permit_permissions_on_mod_id"
    t.index ["route_setting_id"], name: "index_permit_permissions_on_route_setting_id"
    t.index ["tanent_id"], name: "index_permit_permissions_on_tanent_id"
    t.index ["user_id"], name: "index_permit_permissions_on_user_id"
  end

  create_table "res_book_relations", force: :cascade do |t|
    t.bigint "book_id"
    t.string "source_type"
    t.bigint "source_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["book_id"], name: "index_res_book_relations_on_book_id"
    t.index ["source_type", "source_id"], name: "index_res_book_relations_on_source"
  end

  create_table "res_books", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "说明"
    t.string "relation_type", comment: "通讯录的类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_books_on_app_id"
    t.index ["user_id"], name: "index_res_books_on_user_id"
  end

  create_table "res_member_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "res_member_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_member_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_member_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_member_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_member_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_member_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_member_actions_on_user"
  end

  create_table "res_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "res_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_permit_actions_on_user"
  end

  create_table "res_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "标签名称"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_tags_on_app_id"
    t.index ["org_id"], name: "index_res_tags_on_org_id"
  end

  create_table "res_tags_relations", force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "user_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["member_id"], name: "index_res_tags_relations_on_member_id"
    t.index ["org_id"], name: "index_res_tags_relations_on_org_id"
    t.index ["tag_id"], name: "index_res_tags_relations_on_tag_id"
    t.index ["user_id"], name: "index_res_tags_relations_on_user_id"
  end

  create_table "res_user_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "res_user_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_user_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_user_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_user_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_user_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_user_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_user_actions_on_user"
  end

  create_table "role_permission_relations", force: :cascade do |t|
    t.bigint "role_id"
    t.string "permission_type"
    t.bigint "permission_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_type", "permission_id"], name: "index_role_permission_relations_on_permission"
    t.index ["role_id"], name: "index_role_permission_relations_on_role_id"
  end

  create_table "role_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "role_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_role_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_role_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "role_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_role_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "role_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_role_permit_actions_on_user"
  end

  create_table "roles", force: :cascade do |t|
    t.string "resource_type"
    t.bigint "resource_id"
    t.bigint "mod_id"
    t.string "name", comment: "权限标识"
    t.string "label", comment: "显示名称"
    t.string "pinyin", comment: "拼音,排序用"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mod_id"], name: "index_roles_on_mod_id"
    t.index ["resource_type", "resource_id"], name: "index_roles_on_resource"
  end

  create_table "route_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "module名称"
    t.jsonb "conf", comment: "module导出路由"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_route_settings_on_app_id"
    t.index ["mod_id"], name: "index_route_settings_on_mod_id"
  end

  create_table "soa_auth_auth_accounts", force: :cascade do |t|
    t.bigint "user_id"
    t.string "app_id", comment: "对应的app code"
    t.string "account", comment: "对应的app code"
    t.string "password", comment: "加密后的密码"
    t.string "account_type", comment: "用户类型"
    t.string "encrypt_mode", comment: "加密类型"
    t.string "account_mode", comment: "账号类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_soa_auth_auth_accounts_on_user_id"
  end

  create_table "soa_auth_oauths", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "auth_account_id"
    t.string "type", comment: "STI类型，例如Oauth::Wechat"
    t.string "app_id", comment: "对应的app code，解决一个第三方应用绑定多个内部应用"
    t.string "oauth_app_id", comment: "对应的应用，可以是app_id，也可以是配置的app_account"
    t.string "openid", comment: "oauth_id，在对应鉴权系统中的id"
    t.string "unionid", comment: "unionid，在跨平台中使用"
    t.jsonb "options", comment: "oauth平台额外的属性"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["auth_account_id"], name: "index_soa_auth_oauths_on_auth_account_id"
    t.index ["user_id"], name: "index_soa_auth_oauths_on_user_id"
  end

  create_table "state_activate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "state_activate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_state_activate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_state_activate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "state_activate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_state_activate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "state_activate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_state_activate_actions_on_user"
  end

  create_table "state_bpm_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "state_bpm_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_state_bpm_actions_on_app_id"
    t.index ["real_user_id"], name: "index_state_bpm_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "state_bpm_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_state_bpm_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "state_bpm_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_state_bpm_actions_on_user"
  end

  create_table "state_events", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "machine_id"
    t.bigint "transition_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.string "eventable_type"
    t.bigint "eventable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type", comment: "STI"
    t.string "state"
    t.string "state_attr_name", comment: "状态机对应的模型属性名称"
    t.index ["app_id"], name: "index_state_events_on_app_id"
    t.index ["eventable_type", "eventable_id"], name: "index_state_events_on_eventable"
    t.index ["machine_id"], name: "index_state_events_on_machine_id"
    t.index ["source_id"], name: "index_state_events_on_source_id"
    t.index ["target_id"], name: "index_state_events_on_target_id"
    t.index ["transition_id"], name: "index_state_events_on_transition_id"
    t.index ["user_type", "user_id"], name: "index_state_events_on_user"
  end

  create_table "state_machines", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "状态机名称"
    t.string "state_attr_name", comment: "状态机对应模型属性名称"
    t.string "klass", comment: "类名"
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.string "flag", comment: "程序使用标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_machines_on_app_id"
  end

  create_table "state_places", force: :cascade do |t|
    t.bigint "machine_id"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "state", comment: "节点状态"
    t.string "type", comment: "STI"
    t.integer "position", comment: "排序"
    t.jsonb "options", comment: "配置信息"
    t.jsonb "trigger_options", comment: "place的触发器处理"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_places_on_machine_id"
  end

  create_table "state_token_defines", force: :cascade do |t|
    t.bigint "machine_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "token_define的唯一序列号，保持一致"
    t.string "name", comment: "名称"
    t.string "type", comment: "STI"
    t.string "token_type", comment: "对应token的type"
    t.string "token_flag", comment: "对应token的flag"
    t.string "token_default_state", comment: "token生成的默认state"
    t.jsonb "token_form", comment: "token表单"
    t.jsonb "options", comment: "配置信息"
    t.jsonb "limit_options", comment: "限制要求配置信息，包括schedule循环、次数要求等"
    t.jsonb "user_options", comment: "限制可以操作的用户类型设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_token_defines_on_machine_id"
  end

  create_table "state_tokens", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "machine_id"
    t.bigint "event_id"
    t.bigint "transition_id"
    t.bigint "token_define_id"
    t.string "token_source_type"
    t.bigint "token_source_id"
    t.string "eventable_type"
    t.bigint "eventable_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "处理节点名称"
    t.string "flag", comment: "处理节点flag"
    t.string "user_name", comment: "user的名称"
    t.string "state"
    t.jsonb "token_source_attributes", comment: "token source的attributes缓存"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_tokens_on_app_id"
    t.index ["event_id"], name: "index_state_tokens_on_event_id"
    t.index ["eventable_type", "eventable_id"], name: "index_state_tokens_on_eventable"
    t.index ["machine_id"], name: "index_state_tokens_on_machine_id"
    t.index ["token_define_id"], name: "index_state_tokens_on_token_define_id"
    t.index ["token_source_type", "token_source_id"], name: "index_state_tokens_on_token_source"
    t.index ["transition_id"], name: "index_state_tokens_on_transition_id"
    t.index ["user_type", "user_id"], name: "index_state_tokens_on_user"
  end

  create_table "state_transitions", force: :cascade do |t|
    t.bigint "machine_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.bigint "terminate_place_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "type", comment: "STI"
    t.string "seq", comment: "transition的唯一序列号，保持一致"
    t.string "name", comment: "名称"
    t.string "event_name", comment: "操作的英文名称"
    t.string "flag", comment: "程序使用的标记位"
    t.boolean "auto_trigger", comment: "是否自动触发"
    t.jsonb "options", comment: "状态转换的具体配置信息，根据STI的类型不同而不同"
    t.jsonb "trigger_options", comment: "transition的触发器处理"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_transitions_on_machine_id"
    t.index ["source_id"], name: "index_state_transitions_on_source_id"
    t.index ["target_id"], name: "index_state_transitions_on_target_id"
    t.index ["terminate_place_id"], name: "index_state_transitions_on_terminate_place_id"
  end

  create_table "tanent_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "tanent_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_tanent_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_tanent_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "tanent_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_tanent_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "tanent_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_tanent_permit_actions_on_user"
  end

  create_table "tanent_resources", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.jsonb "payload", comment: "存额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanent_resources_on_app_id"
    t.index ["resource_type", "resource_id"], name: "index_tanent_resources_on_resource"
    t.index ["tanent_id"], name: "index_tanent_resources_on_tanent_id"
  end

  create_table "tanents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "code", comment: "租户标识"
    t.string "name", comment: "租户名称"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanents_on_app_id"
  end

  create_table "tofu_entries", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "ancestry", comment: "树形结构"
    t.string "platform"
    t.string "layout", comment: "点击以后前端使用的layout"
    t.string "type", comment: "STI"
    t.string "name", comment: "名称"
    t.string "desc", comment: "描述"
    t.text "icon", comment: "显示的图片或者图标"
    t.text "url", comment: "跳转地址，如果只是menu，可以为空"
    t.string "open_mode", comment: "打开页面的方式"
    t.integer "position", comment: "位置"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_type", "source_id"], name: "index_tofu_entries_on_source"
  end

  create_table "travel_activities", force: :cascade do |t|
    t.bigint "creator_id"
    t.bigint "activity_temp_id"
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.integer "duration", comment: "天数"
    t.string "state", comment: "状态"
    t.date "start_at", comment: "开始日期"
    t.date "end_at", comment: "结束日期"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "contact_info", comment: "合同信息"
    t.string "title", comment: "标题"
    t.string "seq", comment: "编号"
    t.jsonb "attachments", comment: "附件"
    t.text "remark", comment: "备注"
    t.decimal "additional_amount", comment: "额外成本"
    t.decimal "profit_amount", comment: "利润"
    t.string "follow_state", comment: "跟进状态"
    t.index ["activity_temp_id"], name: "index_travel_activities_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_activities_on_app_id"
    t.index ["creator_id"], name: "index_travel_activities_on_creator_id"
  end

  create_table "travel_activity_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "travel_activity_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_travel_activity_actions_on_app_id"
    t.index ["real_user_id"], name: "index_travel_activity_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "travel_activity_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_travel_activity_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "travel_activity_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_travel_activity_actions_on_user"
  end

  create_table "travel_activity_city_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "travel_activity_city_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_travel_activity_city_actions_on_app_id"
    t.index ["real_user_id"], name: "index_travel_activity_city_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "travel_activity_city_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_travel_activity_city_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "travel_activity_city_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_travel_activity_city_actions_on_user"
  end

  create_table "travel_activity_scenic_spot_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "travel_activity_scenic_spot_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_travel_activity_scenic_spot_actions_on_app_id"
    t.index ["real_user_id"], name: "index_travel_activity_scenic_spot_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "travel_activity_scenic_spot_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_travel_activity_scenic_spot_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "travel_activity_scenic_spot_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_travel_activity_scenic_spot_actions_on_user"
  end

  create_table "travel_activity_temp_dirs", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "desc", comment: "描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_activity_temp_dirs_on_app_id"
  end

# Could not dump table "travel_activity_temps" because of following StandardError
#   Unknown type 'vector' for column 'name_embedding'

  create_table "travel_channels", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "config", comment: "配置"
    t.jsonb "display_config", comment: "展示配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "form", comment: "拓展字段表单"
    t.index ["app_id"], name: "index_travel_channels_on_app_id"
  end

  create_table "travel_currencies", force: :cascade do |t|
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "unit", comment: "单位"
    t.string "symbol", comment: "符号"
    t.decimal "ratio", comment: "汇率"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "travel_customer_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "travel_customer_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_travel_customer_actions_on_app_id"
    t.index ["real_user_id"], name: "index_travel_customer_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "travel_customer_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_travel_customer_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "travel_customer_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_travel_customer_actions_on_user"
  end

  create_table "travel_day_temps", force: :cascade do |t|
    t.bigint "activity_temp_id"
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", comment: "排序"
    t.index ["activity_temp_id"], name: "index_travel_day_temps_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_day_temps_on_app_id"
  end

  create_table "travel_days", force: :cascade do |t|
    t.bigint "activity_id"
    t.bigint "day_temp_id"
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "date", comment: "日期"
    t.integer "position", comment: "排序"
    t.index ["activity_id"], name: "index_travel_days_on_activity_id"
    t.index ["app_id"], name: "index_travel_days_on_app_id"
    t.index ["day_temp_id"], name: "index_travel_days_on_day_temp_id"
  end

  create_table "travel_demand_defines", force: :cascade do |t|
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "cover_image", comment: "图标"
    t.jsonb "form", comment: "表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.index ["app_id"], name: "index_travel_demand_defines_on_app_id"
  end

  create_table "travel_demand_grp_presets", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.string "state", comment: "状态"
    t.jsonb "form", comment: "表单"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_demand_grp_presets_on_app_id"
  end

  create_table "travel_demand_grp_temps", force: :cascade do |t|
    t.bigint "activity_temp_id"
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "amount", comment: "报价金额"
    t.string "amount_unit", comment: "报价金额单位"
    t.decimal "amount_ratio", comment: "报价金额对人民币汇率"
    t.decimal "amount_rmb", comment: "报价金额兑换为人民币价格"
    t.boolean "amount_sync", comment: "将报价金额切换为同步需求之和的值"
    t.string "service_name", comment: "套餐名称"
    t.index ["activity_temp_id"], name: "index_travel_demand_grp_temps_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_demand_grp_temps_on_app_id"
  end

  create_table "travel_demand_grps", force: :cascade do |t|
    t.bigint "activity_id"
    t.bigint "demand_grp_temp_id"
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "amount_sync", comment: "将报价金额切换为同步需求之和的值"
    t.decimal "amount", comment: "报价金额"
    t.string "amount_unit", comment: "报价金额单位"
    t.decimal "amount_ratio", comment: "报价金额对人民币汇率"
    t.decimal "amount_rmb", comment: "报价金额兑换为人民币价格"
    t.index ["activity_id"], name: "index_travel_demand_grps_on_activity_id"
    t.index ["app_id"], name: "index_travel_demand_grps_on_app_id"
    t.index ["demand_grp_temp_id"], name: "index_travel_demand_grps_on_demand_grp_temp_id"
  end

  create_table "travel_demand_presets", force: :cascade do |t|
    t.bigint "demand_grp_preset_id"
    t.bigint "app_id"
    t.bigint "poi_region_id"
    t.bigint "demand_define_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.string "mode", comment: "分类"
    t.integer "count", comment: "数量"
    t.integer "duration", comment: "天数"
    t.integer "start_offset", comment: "开始日期偏移量"
    t.integer "end_offset", comment: "结束日期偏移量"
    t.integer "wait_for_move_to_start_offset", comment: "记录移动天数时的暂存字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.decimal "amount", comment: "报价金额"
    t.string "amount_unit", comment: "报价金额单位"
    t.decimal "amount_ratio", comment: "报价金额对人民币汇率"
    t.decimal "amount_rmb", comment: "报价金额兑换为人民币价格"
    t.decimal "offer_amount", comment: "成本金额"
    t.string "offer_amount_unit", comment: "成本金额单位"
    t.decimal "offer_amount_ratio", comment: "成本金额对人民币汇率"
    t.decimal "offer_amount_rmb", comment: "成本金额兑换为人民币价格"
    t.decimal "offer_total_amount", comment: "成本金额 * 数量"
    t.string "offer_total_amount_unit", comment: "成本金额 * 数量单位"
    t.decimal "offer_total_amount_ratio", comment: "成本金额 * 数量对人民币汇率"
    t.decimal "offer_total_amount_rmb", comment: "成本金额 * 数量兑换为人民币价格"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "supplier_id"
    t.integer "traveller_no", comment: "人数"
    t.index ["app_id"], name: "index_travel_demand_presets_on_app_id"
    t.index ["demand_define_id"], name: "index_travel_demand_presets_on_demand_define_id"
    t.index ["demand_grp_preset_id"], name: "index_travel_demand_presets_on_demand_grp_preset_id"
    t.index ["poi_region_id"], name: "index_travel_demand_presets_on_poi_region_id"
    t.index ["supplier_id"], name: "index_travel_demand_presets_on_supplier_id"
  end

  create_table "travel_demand_temps", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "demand_grp_temp_id"
    t.bigint "app_id"
    t.bigint "poi_region_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.integer "count", comment: "数量"
    t.integer "duration", comment: "天数"
    t.integer "start_offset", comment: "开始日期偏移量"
    t.integer "end_offset", comment: "结束日期偏移量"
    t.decimal "amount", comment: "金额"
    t.string "amount_unit", comment: "金额单位"
    t.decimal "amount_ratio", comment: "金额对人民币汇率"
    t.decimal "amount_rmb", comment: "金额兑换为人民币价格"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "mode", comment: "分类"
    t.bigint "demand_define_id"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.integer "wait_for_move_to_start_offset", comment: "记录移动天数时的暂存字段"
    t.bigint "activity_temp_id"
    t.bigint "offer_temp_id"
    t.decimal "offer_amount", comment: "成本金额"
    t.string "offer_amount_unit", comment: "成本金额单位"
    t.decimal "offer_amount_ratio", comment: "成本金额对人民币汇率"
    t.decimal "offer_amount_rmb", comment: "成本金额兑换为人民币价格"
    t.decimal "offer_total_amount", comment: "成本金额 * 数量"
    t.string "offer_total_amount_unit", comment: "成本金额 * 数量单位"
    t.decimal "offer_total_amount_ratio", comment: "成本金额 * 数量对人民币汇率"
    t.decimal "offer_total_amount_rmb", comment: "成本金额 * 数量兑换为人民币价格"
    t.bigint "demand_preset_id"
    t.integer "traveller_no", comment: "人数"
    t.index ["activity_temp_id"], name: "index_travel_demand_temps_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_demand_temps_on_app_id"
    t.index ["demand_define_id"], name: "index_travel_demand_temps_on_demand_define_id"
    t.index ["demand_grp_temp_id"], name: "index_travel_demand_temps_on_demand_grp_temp_id"
    t.index ["demand_preset_id"], name: "index_travel_demand_temps_on_demand_preset_id"
    t.index ["offer_temp_id"], name: "index_travel_demand_temps_on_offer_temp_id"
    t.index ["poi_region_id"], name: "index_travel_demand_temps_on_poi_region_id"
    t.index ["product_id"], name: "index_travel_demand_temps_on_product_id"
  end

  create_table "travel_demands", force: :cascade do |t|
    t.bigint "demand_grp_id"
    t.bigint "activity_id"
    t.bigint "demand_temp_id"
    t.bigint "offer_id"
    t.bigint "app_id"
    t.bigint "poi_region_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.integer "count", comment: "数量"
    t.integer "duration", comment: "天数"
    t.integer "start_offset", comment: "开始日期偏移量"
    t.integer "end_offset", comment: "结束日期偏移量"
    t.decimal "amount", comment: "金额"
    t.string "amount_unit", comment: "金额单位"
    t.decimal "amount_ratio", comment: "金额对人民币汇率"
    t.decimal "amount_rmb", comment: "金额兑换为人民币价格"
    t.date "start_date", comment: "开始日期"
    t.date "end_date", comment: "结束日期"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "mode", comment: "分类"
    t.bigint "demand_define_id"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.bigint "product_id"
    t.integer "wait_for_move_to_start_offset", comment: "记录移动天数时的暂存字段"
    t.decimal "offer_amount", comment: "成本金额"
    t.string "offer_amount_unit", comment: "成本金额单位"
    t.decimal "offer_amount_ratio", comment: "成本金额对人民币汇率"
    t.decimal "offer_amount_rmb", comment: "成本金额兑换为人民币价格"
    t.decimal "offer_total_amount", comment: "成本金额 * 数量"
    t.string "offer_total_amount_unit", comment: "成本金额 * 数量单位"
    t.decimal "offer_total_amount_ratio", comment: "成本金额 * 数量对人民币汇率"
    t.decimal "offer_total_amount_rmb", comment: "成本金额 * 数量兑换为人民币价格"
    t.bigint "demand_preset_id"
    t.integer "traveller_no", comment: "人数"
    t.string "state", comment: "状态"
    t.index ["activity_id"], name: "index_travel_demands_on_activity_id"
    t.index ["app_id"], name: "index_travel_demands_on_app_id"
    t.index ["demand_define_id"], name: "index_travel_demands_on_demand_define_id"
    t.index ["demand_grp_id"], name: "index_travel_demands_on_demand_grp_id"
    t.index ["demand_preset_id"], name: "index_travel_demands_on_demand_preset_id"
    t.index ["demand_temp_id"], name: "index_travel_demands_on_demand_temp_id"
    t.index ["offer_id"], name: "index_travel_demands_on_offer_id"
    t.index ["poi_region_id"], name: "index_travel_demands_on_poi_region_id"
    t.index ["product_id"], name: "index_travel_demands_on_product_id"
  end

  create_table "travel_destinations", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "type", comment: "STI属性"
    t.integer "stars_count", comment: "收藏人数"
    t.datetime "hotted_at"
    t.boolean "is_hotted"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "desc", comment: "描述"
    t.text "content", comment: "内容"
    t.string "icon", comment: "icon"
    t.integer "position", comment: "排序"
    t.jsonb "cover_image", comment: "封面图"
    t.float "score", comment: "评分"
    t.float "price", comment: "价格"
    t.integer "used_count", comment: "旅行人数"
    t.integer "views_count", comment: "浏览人数"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "region_id"
    t.bigint "country_id"
    t.index ["app_id"], name: "index_travel_destinations_on_app_id"
    t.index ["country_id"], name: "index_travel_destinations_on_country_id"
    t.index ["creator_id"], name: "index_travel_destinations_on_creator_id"
    t.index ["region_id"], name: "index_travel_destinations_on_region_id"
  end

  create_table "travel_evaluations", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "评分项名称"
    t.float "score", comment: "分值"
    t.jsonb "options", comment: "评论的内容结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_evaluations_on_app_id"
    t.index ["source_type", "source_id"], name: "index_travel_evaluations_on_source"
    t.index ["user_id"], name: "index_travel_evaluations_on_user_id"
  end

  create_table "travel_extras", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_extras_on_app_id"
    t.index ["source_type", "source_id"], name: "index_travel_extras_on_source"
  end

  create_table "travel_notice_grps", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_notice_grps_on_app_id"
  end

  create_table "travel_notice_relate_actions", force: :cascade do |t|
    t.bigint "notice_id"
    t.bigint "notice_grp_id"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notice_grp_id"], name: "index_travel_notice_relate_actions_on_notice_grp_id"
    t.index ["notice_id"], name: "index_travel_notice_relate_actions_on_notice_id"
  end

  create_table "travel_notices", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.text "content", comment: "内容"
    t.string "state", comment: "状态"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_notices_on_app_id"
    t.index ["user_id"], name: "index_travel_notices_on_user_id"
  end

  create_table "travel_offer_temps", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "activity_temp_id"
    t.bigint "app_id"
    t.bigint "supplier_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.decimal "amount", comment: "金额"
    t.string "amount_unit", comment: "金额单位"
    t.decimal "amount_ratio", comment: "金额对人民币汇率"
    t.decimal "amount_rmb", comment: "金额兑换为人民币价格"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "amount_sync", comment: "将成本金额切换为同步需求之和的值"
    t.index ["activity_temp_id"], name: "index_travel_offer_temps_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_offer_temps_on_app_id"
    t.index ["product_id"], name: "index_travel_offer_temps_on_product_id"
    t.index ["supplier_id"], name: "index_travel_offer_temps_on_supplier_id"
  end

  create_table "travel_offers", force: :cascade do |t|
    t.bigint "payment_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "state"
    t.string "pay_state"
    t.decimal "amount", comment: "金额"
    t.string "amount_unit", comment: "金额单位"
    t.decimal "amount_ratio", comment: "金额对人民币汇率"
    t.decimal "amount_rmb", comment: "金额兑换为人民币价格"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "supplier_id"
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "seq", comment: "编号"
    t.bigint "offer_temp_id"
    t.boolean "amount_sync", comment: "将成本金额切换为同步需求之和的值"
    t.index ["app_id"], name: "index_travel_offers_on_app_id"
    t.index ["creator_id"], name: "index_travel_offers_on_creator_id"
    t.index ["offer_temp_id"], name: "index_travel_offers_on_offer_temp_id"
    t.index ["payment_id"], name: "index_travel_offers_on_payment_id"
    t.index ["supplier_id"], name: "index_travel_offers_on_supplier_id"
  end

  create_table "travel_order_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "travel_order_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_travel_order_actions_on_app_id"
    t.index ["real_user_id"], name: "index_travel_order_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "travel_order_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_travel_order_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "travel_order_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_travel_order_actions_on_user"
  end

  create_table "travel_order_divides", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "user_id"
    t.float "weight", comment: "权重"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_travel_order_divides_on_order_id"
    t.index ["user_id"], name: "index_travel_order_divides_on_user_id"
  end

  create_table "travel_orders", force: :cascade do |t|
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "ota_no", comment: "OTA 订单号"
    t.string "ota_state", comment: "OTA 订单状态"
    t.string "state", comment: "状态"
    t.jsonb "meta", comment: "元数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "app_id"
    t.bigint "channel_id"
    t.bigint "product_id"
    t.decimal "order_amount", comment: "金额"
    t.decimal "plus_amount", comment: "金额"
    t.decimal "discount_amount", comment: "金额"
    t.decimal "income_amount", comment: "金额"
    t.decimal "remain_amount", comment: "金额"
    t.integer "traveler_num", comment: "出行人数"
    t.date "trip_start_date", comment: "出行日期"
    t.date "trip_end_date", comment: "返程日期"
    t.integer "trip_days", comment: "出行天数"
    t.string "item_num", comment: "购买数量"
    t.text "remark", comment: "备注"
    t.jsonb "attachments", comment: "附件"
    t.jsonb "payload", comment: "信息存储"
    t.boolean "is_invoiced", comment: "是否开票"
    t.bigint "ota_sku_id"
    t.bigint "activity_temp_id"
    t.bigint "creator_id"
    t.jsonb "channel_payload", comment: "channel 拓展字段表单存储的值"
    t.bigint "ota_product_id"
    t.string "origin", comment: "渠道来源，区别于channel（订单来源）"
    t.string "sku_no", comment: "产品编号"
    t.string "order_type"
    t.bigint "sub_order_id"
    t.bigint "sku_id"
    t.decimal "fee_amount", comment: "手续费用"
    t.decimal "refund_amount", comment: "退款费用"
    t.decimal "origin_amount", comment: "原始金额"
    t.decimal "receive_amount", comment: "金额"
    t.index ["activity_temp_id"], name: "index_travel_orders_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_orders_on_app_id"
    t.index ["channel_id"], name: "index_travel_orders_on_channel_id"
    t.index ["creator_id"], name: "index_travel_orders_on_creator_id"
    t.index ["ota_product_id"], name: "index_travel_orders_on_ota_product_id"
    t.index ["ota_sku_id"], name: "index_travel_orders_on_ota_sku_id"
    t.index ["product_id"], name: "index_travel_orders_on_product_id"
    t.index ["sku_id"], name: "index_travel_orders_on_sku_id"
    t.index ["sub_order_id"], name: "index_travel_orders_on_sub_order_id"
  end

  create_table "travel_ota_channels", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "ota_type"
    t.jsonb "config", comment: "配置"
    t.jsonb "display_config", comment: "展示配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_ota_channels_on_app_id"
  end

  create_table "travel_ota_payments", force: :cascade do |t|
    t.bigint "order_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.decimal "amount", comment: "金额"
    t.jsonb "meta", comment: "元数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_travel_ota_payments_on_order_id"
  end

  create_table "travel_ota_products", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "channel_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "seq", comment: "编号"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "ota_no", comment: "ota编号"
    t.datetime "list_time", comment: "上架时间"
    t.datetime "delist_time", comment: "下架时间"
    t.integer "num", comment: "数量"
    t.integer "sold_quantity", comment: "销售数量"
    t.decimal "price", comment: "价格"
    t.jsonb "payload", comment: "额外数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_ota_products_on_app_id"
    t.index ["channel_id"], name: "index_travel_ota_products_on_channel_id"
  end

  create_table "travel_ota_sku_origins", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "channel_id"
    t.bigint "activity_temp_id"
    t.string "name", comment: "名称"
    t.string "code", comment: "编码"
    t.string "state", comment: "状态"
    t.string "from", comment: "来源"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_temp_id"], name: "index_travel_ota_sku_origins_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_ota_sku_origins_on_app_id"
    t.index ["channel_id"], name: "index_travel_ota_sku_origins_on_channel_id"
  end

  create_table "travel_ota_skus", force: :cascade do |t|
    t.bigint "product_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "ota_product_no", comment: "OTA 商品 ID"
    t.string "ota_sku_no", comment: "OTA sku ID"
    t.jsonb "meta", comment: "元数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "app_id"
    t.bigint "channel_id"
    t.bigint "activity_temp_id"
    t.bigint "ota_product_id"
    t.string "uid", comment: "套餐"
    t.float "rate", comment: "手续费率"
    t.index ["activity_temp_id"], name: "index_travel_ota_skus_on_activity_temp_id"
    t.index ["app_id"], name: "index_travel_ota_skus_on_app_id"
    t.index ["channel_id"], name: "index_travel_ota_skus_on_channel_id"
    t.index ["ota_product_id"], name: "index_travel_ota_skus_on_ota_product_id"
    t.index ["product_id"], name: "index_travel_ota_skus_on_product_id"
  end

  create_table "travel_ota_snapshots", force: :cascade do |t|
    t.bigint "ota_sku_id"
    t.bigint "order_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.decimal "amount", comment: "金额"
    t.integer "count", comment: "数量"
    t.jsonb "meta", comment: "元数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_travel_ota_snapshots_on_order_id"
    t.index ["ota_sku_id"], name: "index_travel_ota_snapshots_on_ota_sku_id"
  end

  create_table "travel_pay_accounts", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "supplier_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "账户名称"
    t.string "state", comment: "状态"
    t.string "account_type", comment: "账户类型"
    t.jsonb "payload", comment: "字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_pay_accounts_on_app_id"
    t.index ["supplier_id"], name: "index_travel_pay_accounts_on_supplier_id"
  end

  create_table "travel_payments", force: :cascade do |t|
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "state"
    t.decimal "amount", comment: "金额"
    t.string "amount_unit", comment: "金额单位"
    t.decimal "amount_ratio", comment: "金额对人民币汇率"
    t.decimal "amount_rmb", comment: "金额兑换为人民币价格"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "app_id"
    t.bigint "supplier_id"
    t.bigint "creator_id"
    t.string "seq", comment: "编号"
    t.bigint "pay_account_id"
    t.index ["app_id"], name: "index_travel_payments_on_app_id"
    t.index ["creator_id"], name: "index_travel_payments_on_creator_id"
    t.index ["pay_account_id"], name: "index_travel_payments_on_pay_account_id"
    t.index ["supplier_id"], name: "index_travel_payments_on_supplier_id"
  end

  create_table "travel_poi_app_countries", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "country_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "color", comment: "颜色"
    t.integer "position", comment: "位置"
    t.jsonb "conf", comment: "其他配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name", comment: "国家名称"
    t.string "state", comment: "状态"
    t.jsonb "content", comment: "内容"
    t.string "icon", comment: "icon"
    t.jsonb "cover_image", comment: "封面图"
    t.float "score", comment: "评分"
    t.integer "used_count", comment: "旅行人数"
    t.integer "views_count", comment: "浏览人数"
    t.integer "stars_count", comment: "收藏人数"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "hotted_at"
    t.boolean "is_hotted"
    t.index ["app_id"], name: "index_travel_poi_app_countries_on_app_id"
    t.index ["country_id"], name: "index_travel_poi_app_countries_on_country_id"
  end

  create_table "travel_poi_countries", force: :cascade do |t|
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "continent", comment: "大洲"
    t.jsonb "detail", comment: "详情"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "en_name", comment: "英文名称"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "meta", comment: "元数据"
    t.bigint "geo_id"
    t.index ["geo_id"], name: "index_travel_poi_countries_on_geo_id"
  end

  create_table "travel_poi_extras", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "detail", comment: "详情"
    t.index ["app_id"], name: "index_travel_poi_extras_on_app_id"
    t.index ["source_type", "source_id"], name: "index_travel_poi_extras_on_source"
  end

  create_table "travel_poi_region_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "travel/poi/region_anc_desc_idxx", unique: true
  end

  create_table "travel_poi_regions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "detail", comment: "详情"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "country_id"
    t.string "en_name", comment: "英文名称"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "meta", comment: "元数据"
    t.float "longitude", comment: "经度"
    t.float "latitude", comment: "纬度"
    t.string "address", comment: "地址"
    t.datetime "recommended_at"
    t.boolean "is_recommended"
    t.index ["app_id"], name: "index_travel_poi_regions_on_app_id"
    t.index ["country_id"], name: "index_travel_poi_regions_on_country_id"
    t.index ["creator_id"], name: "index_travel_poi_regions_on_creator_id"
  end

  create_table "travel_product_days", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_product_days_on_app_id"
    t.index ["product_id"], name: "index_travel_product_days_on_product_id"
  end

  create_table "travel_product_dirs", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "desc", comment: "描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_product_dirs_on_app_id"
  end

# Could not dump table "travel_products" because of following StandardError
#   Unknown type 'vector' for column 'name_embedding'

  create_table "travel_receipts", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "payment_id"
    t.bigint "creator_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.decimal "amount", comment: "金额"
    t.string "amount_unit", comment: "金额单位"
    t.decimal "amount_ratio", comment: "金额对人民币汇率"
    t.decimal "amount_rmb", comment: "金额兑换为人民币价格"
    t.text "content", comment: "内容"
    t.jsonb "attachments", comment: "附件"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_receipts_on_app_id"
    t.index ["creator_id"], name: "index_travel_receipts_on_creator_id"
    t.index ["payment_id"], name: "index_travel_receipts_on_payment_id"
  end

  create_table "travel_records", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "type", comment: "STI属性"
    t.integer "stars_count", comment: "收藏人数"
    t.datetime "hotted_at"
    t.boolean "is_hotted"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "desc", comment: "描述"
    t.string "icon", comment: "icon"
    t.string "content", comment: "内容"
    t.jsonb "cover_image", comment: "封面图"
    t.integer "position", comment: "排序"
    t.float "price", comment: "价格"
    t.float "score", comment: "评分"
    t.integer "used_count", comment: "使用人数"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_records_on_app_id"
    t.index ["creator_id"], name: "index_travel_records_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_travel_records_on_source"
  end

  create_table "travel_region_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "travel/poi/region_anc_desc_idx", unique: true
  end

  create_table "travel_regions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "creator_id"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "detail", comment: "详情"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_regions_on_app_id"
    t.index ["creator_id"], name: "index_travel_regions_on_creator_id"
    t.index ["org_id"], name: "index_travel_regions_on_org_id"
  end

  create_table "travel_relation_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "travel_relation_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_travel_relation_actions_on_app_id"
    t.index ["real_user_id"], name: "index_travel_relation_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "travel_relation_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_travel_relation_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "travel_relation_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_travel_relation_actions_on_user"
  end

  create_table "travel_revenues", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "data_type"
    t.bigint "data_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.decimal "amount", comment: "金额"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "amount_unit", comment: "支付金额单位"
    t.decimal "amount_ratio", comment: "支付金额对人民币汇率"
    t.decimal "amount_rmb", comment: "支付金额兑换为人民币价格"
    t.string "mode", comment: "模式，例如收款/补款等"
    t.string "remark", comment: "备注信息"
    t.string "type", comment: "STI属性"
    t.jsonb "attachments", comment: "附件"
    t.bigint "creator_id"
    t.datetime "create_time", comment: "收款时间"
    t.string "platform", comment: "平台"
    t.index ["app_id"], name: "index_travel_revenues_on_app_id"
    t.index ["creator_id"], name: "index_travel_revenues_on_creator_id"
    t.index ["data_type", "data_id"], name: "index_travel_revenues_on_data"
    t.index ["source_type", "source_id"], name: "index_travel_revenues_on_source"
  end

  create_table "travel_sku_prices", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "product_id"
    t.bigint "sku_id"
    t.string "state", comment: "状态"
    t.decimal "price", comment: "价格"
    t.decimal "original_price", comment: "原价"
    t.string "mode", comment: "模式"
    t.date "sale_date", comment: "销售日期"
    t.jsonb "option", comment: "价格配置"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_travel_sku_prices_on_app_id"
    t.index ["product_id"], name: "index_travel_sku_prices_on_product_id"
    t.index ["sku_id"], name: "index_travel_sku_prices_on_sku_id"
  end

  create_table "travel_skus", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "product_id"
    t.bigint "creator_id"
    t.string "type", comment: "STI属性"
    t.string "seq", comment: "编号"
    t.integer "comments_count", comment: "评论数量"
    t.string "comment_conf"
    t.datetime "recommended_at"
    t.boolean "is_recommended"
    t.string "name", comment: "商品名称"
    t.string "state", comment: "商品状态"
    t.jsonb "content", comment: "商品内容"
    t.jsonb "cover_image", comment: "商品图片(轮播图)"
    t.float "price", comment: "商品价格"
    t.float "origin_price", comment: "原始价格"
    t.integer "position", comment: "商品排序"
    t.float "total_number", comment: "库存数量"
    t.float "freeze_number", comment: "冻结数量"
    t.integer "sale_count", comment: "销售数量"
    t.integer "stars_count", comment: "收藏数量"
    t.float "score", comment: "评分"
    t.float "origin_score", comment: "原始评分"
    t.integer "origin_sale_count", comment: "原始销售数量"
    t.jsonb "payload", comment: "扩展信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "rate", comment: "手续费率"
    t.index ["app_id"], name: "index_travel_skus_on_app_id"
    t.index ["creator_id"], name: "index_travel_skus_on_creator_id"
    t.index ["product_id"], name: "index_travel_skus_on_product_id"
  end

  create_table "users", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "ref_user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "account", comment: "账号，关联登录"
    t.string "name", comment: "用户姓名"
    t.string "nickname", comment: "用户昵称"
    t.string "pinyin", comment: "用户名拼音"
    t.string "mobile", comment: "用户手机号"
    t.string "email", comment: "用户邮箱"
    t.string "gender", comment: "性别"
    t.jsonb "avatar", comment: "用户头像"
    t.string "identity_id", comment: "证件号码，需要时候可以作为唯一标识"
    t.datetime "last_visit_at", comment: "最后访问时间"
    t.datetime "blocked_at"
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_users_on_app_id"
    t.index ["ref_user_id"], name: "index_users_on_ref_user_id"
    t.index ["tanent_id"], name: "index_users_on_tanent_id"
  end

  create_table "users_roles", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role_id"], name: "index_users_roles_on_role_id"
    t.index ["user_id"], name: "index_users_roles_on_user_id"
  end

  create_table "version_relationships", force: :cascade do |t|
    t.bigint "app_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "real_resource_type"
    t.bigint "real_resource_id"
    t.string "version_type"
    t.bigint "version_id"
    t.string "operator_type"
    t.bigint "operator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_version_relationships_on_app_id"
    t.index ["operator_type", "operator_id"], name: "index_version_relationships_on_operator"
    t.index ["real_resource_type", "real_resource_id"], name: "index_version_relationships_on_real_resource"
    t.index ["resource_type", "resource_id"], name: "index_version_relationships_on_resource"
    t.index ["version_type", "version_id"], name: "index_version_relationships_on_version"
  end

  create_table "wechat_configs", force: :cascade do |t|
    t.string "environment"
    t.string "account", comment: "account name"
    t.boolean "enabled"
    t.string "appid"
    t.string "secret"
    t.string "type"
    t.string "corpid"
    t.string "corpsecret"
    t.integer "agentid"
    t.boolean "encrypt_mode"
    t.string "encoding_aes_key"
    t.string "token"
    t.string "access_token"
    t.string "jsapi_ticket"
    t.boolean "skip_verify_ssl"
    t.integer "timeout"
    t.string "trusted_domain_fullname"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["environment", "account"], name: "index_wechat_configs_on_environment_and_account", unique: true
  end

  create_table "wechat_keywords", force: :cascade do |t|
    t.string "key", comment: "关键词"
    t.string "value", comment: "返回文案"
    t.string "pattern", comment: "匹配模式"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_wechat_keywords_on_key"
  end

  create_table "wechat_sessions", force: :cascade do |t|
    t.string "openid", comment: "openid"
    t.string "hash_store", comment: "hash_store"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["openid"], name: "index_wechat_sessions_on_openid", unique: true
  end

  create_table "weixin_chats", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "owner_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "appid", comment: "企业/微信id"
    t.string "state", comment: "状态"
    t.string "chatid", comment: "群聊id"
    t.jsonb "payload", comment: "其他数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_weixin_chats_on_app_id"
    t.index ["owner_id"], name: "index_weixin_chats_on_owner_id"
    t.index ["source_type", "source_id"], name: "index_weixin_chats_on_source"
  end

  create_table "weixin_mesages", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "chat_id"
    t.bigint "user_id"
    t.string "type", comment: "STI属性"
    t.string "state"
    t.string "msg_type", comment: "消息类型"
    t.text "content", comment: "消息内容"
    t.jsonb "body", comment: "消息内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_weixin_mesages_on_app_id"
    t.index ["chat_id"], name: "index_weixin_mesages_on_chat_id"
    t.index ["user_id"], name: "index_weixin_mesages_on_user_id"
  end

  create_table "weixin_subscribes", force: :cascade do |t|
    t.string "oauth_app_id", comment: "对应的wechat client account名称"
    t.string "unionid", comment: "unionid"
    t.string "openid", comment: "openid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "xhs_channels", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "code", comment: "标识"
    t.string "agentid", comment: "客服ID"
    t.jsonb "config", comment: "配置"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_xhs_channels_on_app_id"
  end

  create_table "xhs_chats", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "channel_id"
    t.bigint "user_id"
    t.datetime "replied_at"
    t.boolean "is_replied"
    t.string "name", comment: "名称"
    t.integer "position", comment: "位置"
    t.string "state"
    t.datetime "operate_at", comment: "操作时间"
    t.string "agentid", comment: "客户ID"
    t.string "nickname", comment: "用户昵称"
    t.string "avatar", comment: "用户头像"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_xhs_chats_on_app_id"
    t.index ["channel_id"], name: "index_xhs_chats_on_channel_id"
    t.index ["user_id"], name: "index_xhs_chats_on_user_id"
  end

  create_table "xhs_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "chat_id"
    t.bigint "channel_id"
    t.string "type", comment: "STI属性"
    t.string "seq", comment: "编号"
    t.string "name", comment: "名称"
    t.string "sender"
    t.string "state"
    t.string "message_type", comment: "消息类型"
    t.text "content", comment: "内容"
    t.string "token", comment: "token"
    t.string "senderid", comment: "消息发送人ID"
    t.string "receiverid", comment: "接受者ID"
    t.datetime "operate_at", comment: "操作时间"
    t.integer "position", comment: "位置"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_xhs_messages_on_app_id"
    t.index ["channel_id"], name: "index_xhs_messages_on_channel_id"
    t.index ["chat_id"], name: "index_xhs_messages_on_chat_id"
    t.index ["user_id"], name: "index_xhs_messages_on_user_id"
  end

end
