require 'swagger_helper'

RSpec.describe 'travel/api/products', type: :request, capture_examples: true, tags: ["travel api"] do
  product_ref = {
    type: :object, properties: {
      product: {
        type: :object, properties: {
          product_dir_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          seq: { type: :string, description: '编号' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          cover_image: { type: :jsonb, description: '图片' },
          content: { type: :jsonb, description: '内容' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          duration: { type: :integer, description: '天数' },
          creator_id: { type: :integer, description: '' },
          amount_sync: { type: :boolean, description: '将报价金额切换为同步需求之和的值' },
          recommended_at: { type: :datetime, description: '' },
          is_recommended: { type: :boolean, description: '' },
          origin_sale_count: { type: :integer, description: '原始销售数量' },
          flag: { type: :string, description: '标识' },
        }
      }
    }
  }
  product_value = FactoryBot.attributes_for(:travel_product)

  before :each do
  end

  path '/travel/api/products' do

    get(summary: 'list products') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_product_count
        }
      end
    end
  end

  path '/travel/api/products/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show product') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_products.first.id }
        it {
          body = JSON.parse(response.body)
          product = @travel_products.first
          expect(body['product_dir_id']).to eq product.product_dir_id
          expect(body['model_flag']).to eq product.model_flag
          expect(body['model_payload']).to eq product.model_payload
          expect(body['model_payload_summary']).to eq product.model_payload_summary
          expect(body['model_detail']).to eq product.model_detail
          expect(body['seq']).to eq product.seq
          expect(body['name']).to eq product.name
          expect(body['state']).to eq product.state
          expect(body['app_id']).to eq product.app_id
          expect(body['type']).to eq product.type
          expect(body['cover_image']).to eq product.cover_image
          expect(body['content']).to eq product.content
          # expect(body['amount']).to eq product.amount
          # expect(body['amount_unit']).to eq product.amount_unit
          # expect(body['amount_ratio']).to eq product.amount_ratio
          # expect(body['amount_rmb']).to eq product.amount_rmb
          # expect(body['duration']).to eq product.duration
          expect(body['creator_id']).to eq product.creator_id
          expect(body['amount_sync']).to eq product.amount_sync
          expect(body['name_embedding']).to eq product.name_embedding
          expect(body['recommended_at']).to eq product.recommended_at
          expect(body['is_recommended']).to eq product.is_recommended
          expect(body['origin_sale_count']).to eq product.origin_sale_count
          expect(body['flag']).to eq product.flag
        }
      end
    end
  end
end
