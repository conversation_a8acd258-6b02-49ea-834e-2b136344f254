require 'swagger_helper'

RSpec.describe 'travel/api/poi_regions', type: :request, capture_examples: true, tags: ["travel api"] do
  region_ref = {
    type: :object, properties: {
      region: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          parent_id: { type: :integer, description: 'closure tree parent_id' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          detail: { type: :jsonb, description: '详情' },
          position: { type: :integer, description: '排序' },
          country_id: { type: :integer, description: '' },
          en_name: { type: :string, description: '英文名称' },
          cover_image: { type: :jsonb, description: '封面图' },
          meta: { type: :jsonb, description: '元数据' },
          longitude: { type: :float, description: '经度' },
          latitude: { type: :float, description: '纬度' },
          address: { type: :string, description: '地址' },
          recommended_at: { type: :datetime, description: '' },
          is_recommended: { type: :boolean, description: '' },
        }
      }
    }
  }
  region_value = FactoryBot.attributes_for(:travel_poi_region)

  before :each do
    @travel_poi_region_count = 5
    @travel_poi_regions = FactoryBot.create_list(:travel_poi_region, @travel_poi_region_count)
  end

  path '/travel/api/poi_regions' do

    get(summary: 'list poi_regions') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_region_count
        }
      end
    end
  end

  path '/travel/api/poi_regions/list_index' do

    post(summary: 'list_index poi_region') do
      produces 'application/json'
      consumes 'application/json'
      parameter :region, in: :body, schema: region_ref
      response(201, description: 'successful') do
        let(:region) do
          { region: region_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_region_count
        }
      end
    end
  end

  path '/travel/api/poi_regions/group_index' do

    post(summary: 'group_index poi_region') do
      produces 'application/json'
      consumes 'application/json'
      parameter :region, in: :body, schema: region_ref
      response(201, description: 'successful') do
        let(:region) do
          { region: region_value }
        end
      end
    end
  end

  path '/travel/api/poi_regions/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show poi_region') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_poi_regions.first.id }
        it {
          body = JSON.parse(response.body)
          region = @travel_poi_regions.first
          expect(body['app_id']).to eq region.app_id
          expect(body['creator_id']).to eq region.creator_id
          expect(body['parent_id']).to eq region.parent_id
          expect(body['type']).to eq region.type
          expect(body['model_flag']).to eq region.model_flag
          expect(body['model_payload']).to eq region.model_payload
          expect(body['model_payload_summary']).to eq region.model_payload_summary
          expect(body['model_detail']).to eq region.model_detail
          expect(body['name']).to eq region.name
          expect(body['detail']).to eq region.detail
          expect(body['position']).to eq region.position
          expect(body['country_id']).to eq region.country_id
          expect(body['en_name']).to eq region.en_name
          expect(body['cover_image']).to eq region.cover_image
          expect(body['meta']).to eq region.meta
          expect(body['longitude']).to eq region.longitude
          expect(body['latitude']).to eq region.latitude
          expect(body['address']).to eq region.address
          expect(body['recommended_at']).to eq region.recommended_at
          expect(body['is_recommended']).to eq region.is_recommended
        }
      end
    end
  end
end
