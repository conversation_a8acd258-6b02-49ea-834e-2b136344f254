require 'swagger_helper'

RSpec.describe 'travel/api/demand_grps', type: :request, capture_examples: true, tags: ["travel api"] do
  demand_grp_ref = {
    type: :object, properties: {
      demand_grp: {
        type: :object, properties: {
          activity_id: { type: :integer, description: '' },
          demand_grp_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
        }
      }
    }
  }
  demand_grp_value = FactoryBot.attributes_for(:travel_demand_grp)

  before :each do
  end

  path '/travel/api/demand_grps' do

    get(summary: 'list demand_grps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_grp_count
        }
      end
    end
  end

  path '/travel/api/demand_grps/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_grp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_demand_grps.first.id }
        it {
          body = JSON.parse(response.body)
          demand_grp = @travel_demand_grps.first
          expect(body['activity_id']).to eq demand_grp.activity_id
          expect(body['demand_grp_temp_id']).to eq demand_grp.demand_grp_temp_id
          expect(body['app_id']).to eq demand_grp.app_id
          expect(body['model_flag']).to eq demand_grp.model_flag
          expect(body['model_payload']).to eq demand_grp.model_payload
          expect(body['model_payload_summary']).to eq demand_grp.model_payload_summary
          expect(body['model_detail']).to eq demand_grp.model_detail
          expect(body['name']).to eq demand_grp.name
        }
      end
    end
  end
end
