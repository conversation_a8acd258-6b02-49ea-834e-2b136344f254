require 'swagger_helper'

RSpec.describe 'travel/api/days', type: :request, capture_examples: true, tags: ["travel api"] do
  day_ref = {
    type: :object, properties: {
      day: {
        type: :object, properties: {
          activity_id: { type: :integer, description: '' },
          day_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          date: { type: :date, description: '日期' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  day_value = FactoryBot.attributes_for(:travel_day)

  before :each do
  end

  path '/travel/api/days' do

    get(summary: 'list days') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_day_count
        }
      end
    end
  end

  path '/travel/api/days/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show day') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_days.first.id }
        it {
          body = JSON.parse(response.body)
          day = @travel_days.first
          expect(body['activity_id']).to eq day.activity_id
          expect(body['day_temp_id']).to eq day.day_temp_id
          expect(body['app_id']).to eq day.app_id
          expect(body['model_flag']).to eq day.model_flag
          expect(body['model_payload']).to eq day.model_payload
          expect(body['model_payload_summary']).to eq day.model_payload_summary
          expect(body['model_detail']).to eq day.model_detail
          expect(body['name']).to eq day.name
          expect(body['desc']).to eq day.desc
          expect(body['date']).not_to be_nil
          expect(body['position']).to eq day.position
        }
      end
    end
  end
end
