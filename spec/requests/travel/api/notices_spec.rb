require 'swagger_helper'

RSpec.describe 'travel/api/notices', type: :request, capture_examples: true, tags: ["travel api"] do
  notice_ref = {
    type: :object, properties: {
      notice: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          content: { type: :text, description: '内容' },
          state: { type: :string, description: '状态' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  notice_value = FactoryBot.attributes_for(:travel_notice)

  before :each do
  end

  path '/travel/api/notices' do

    get(summary: 'list notices') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_notice_count
        }
      end
    end
  end

  path '/travel/api/notices/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show notice') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_notices.first.id }
        it {
          body = JSON.parse(response.body)
          notice = @travel_notices.first
          expect(body['app_id']).to eq notice.app_id
          expect(body['user_id']).to eq notice.user_id
          expect(body['type']).to eq notice.type
          expect(body['name']).to eq notice.name
          expect(body['content']).to eq notice.content
          expect(body['state']).to eq notice.state
          expect(body['payload']).to eq notice.payload
        }
      end
    end
  end
end
