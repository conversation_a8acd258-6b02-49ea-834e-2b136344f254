require 'swagger_helper'

RSpec.describe 'travel/api/notice_grps', type: :request, capture_examples: true, tags: ["travel api"] do
  notice_grp_ref = {
    type: :object, properties: {
      notice_grp: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  notice_grp_value = FactoryBot.attributes_for(:travel_notice_grp)

  before :each do
    @travel_notice_grp_count = 5
    @travel_notice_grps = FactoryBot.create_list(:travel_notice_grp, @travel_notice_grp_count)
  end

  path '/travel/api/notice_grps' do

    get(summary: 'list notice_grps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_notice_grp_count
        }
      end
    end
  end

  path '/travel/api/notice_grps/list_index' do

    post(summary: 'list_index notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :notice_grp, in: :body, schema: notice_grp_ref
      response(201, description: 'successful') do
        let(:notice_grp) do
          { notice_grp: notice_grp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_notice_grp_count
        }
      end
    end
  end

  path '/travel/api/notice_grps/group_index' do

    post(summary: 'group_index notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :notice_grp, in: :body, schema: notice_grp_ref
      response(201, description: 'successful') do
        let(:notice_grp) do
          { notice_grp: notice_grp_value }
        end
      end
    end
  end

  path '/travel/api/notice_grps/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_notice_grps.first.id }
        it {
          body = JSON.parse(response.body)
          notice_grp = @travel_notice_grps.first
          expect(body['app_id']).to eq notice_grp.app_id
          expect(body['type']).to eq notice_grp.type
          expect(body['name']).to eq notice_grp.name
          expect(body['state']).to eq notice_grp.state
          expect(body['payload']).to eq notice_grp.payload
        }
      end
    end
  end
end
