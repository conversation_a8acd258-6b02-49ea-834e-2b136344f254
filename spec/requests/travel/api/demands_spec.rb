require 'swagger_helper'

RSpec.describe 'travel/api/demands', type: :request, capture_examples: true, tags: ["travel api"] do
  demand_ref = {
    type: :object, properties: {
      demand: {
        type: :object, properties: {
          demand_grp_id: { type: :integer, description: '' },
          activity_id: { type: :integer, description: '' },
          demand_temp_id: { type: :integer, description: '' },
          offer_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          poi_region_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          count: { type: :integer, description: '数量' },
          duration: { type: :integer, description: '天数' },
          start_offset: { type: :integer, description: '开始日期偏移量' },
          end_offset: { type: :integer, description: '结束日期偏移量' },
          # around_amount: { type: :decimal, description: '预估金额' },
          # around_amount_unit: { type: :string, description: '预估金额单位' },
          # around_amount_ratio: { type: :decimal, description: '预估金额对人民币汇率' },
          # around_amount_rmb: { type: :decimal, description: '预估金额兑换为人民币价格' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          start_date: { type: :date, description: '开始日期' },
          end_date: { type: :date, description: '结束日期' },
          mode: { type: :string, description: '分类' },
          demand_define_id: { type: :integer, description: '' },
          payload: { type: :jsonb, description: 'payload payload存储的字段' },
          payload_summary: { type: :jsonb, description: 'payload summary存储的字段' },
          product_id: { type: :integer, description: '' },
          wait_for_move_to_start_offset: { type: :integer, description: '记录移动天数时的暂存字段' },
        }
      }
    }
  }
  demand_value = FactoryBot.attributes_for(:travel_demand)

  before :each do
  end

  path '/travel/api/demands' do

    get(summary: 'list demands') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_count
        }
      end
    end
  end

  path '/travel/api/demands/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_demands.first.id }
        it {
          body = JSON.parse(response.body)
          demand = @travel_demands.first
          expect(body['demand_grp_id']).to eq demand.demand_grp_id
          expect(body['activity_id']).to eq demand.activity_id
          expect(body['demand_temp_id']).to eq demand.demand_temp_id
          expect(body['offer_id']).to eq demand.offer_id
          expect(body['app_id']).to eq demand.app_id
          expect(body['poi_region_id']).to eq demand.poi_region_id
          expect(body['model_flag']).to eq demand.model_flag
          expect(body['model_payload']).to eq demand.model_payload
          expect(body['model_payload_summary']).to eq demand.model_payload_summary
          expect(body['model_detail']).to eq demand.model_detail
          expect(body['name']).to eq demand.name
          expect(body['desc']).to eq demand.desc
          expect(body['count']).to eq demand.count
          expect(body['duration']).to eq demand.duration
          expect(body['start_offset']).to eq demand.start_offset
          expect(body['end_offset']).to eq demand.end_offset
          # # expect(body['around_amount']).to eq demand.around_amount
          # # expect(body['around_amount_unit']).to eq demand.around_amount_unit
          # # expect(body['around_amount_ratio']).to eq demand.around_amount_ratio
          # # expect(body['around_amount_rmb']).to eq demand.around_amount_rmb
          expect(body['amount']).to eq demand.amount
          expect(body['amount_unit']).to eq demand.amount_unit
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq demand.amount_rmb
          expect(body['start_date']).not_to be_nil
          expect(body['end_date']).not_to be_nil
          expect(body['mode']).to eq demand.mode
          expect(body['demand_define_id']).to eq demand.demand_define_id
          expect(body['payload']).to eq demand.payload
          expect(body['payload_summary']).to eq demand.payload_summary
          expect(body['product_id']).to eq demand.product_id
          expect(body['wait_for_move_to_start_offset']).to eq demand.wait_for_move_to_start_offset
        }
      end
    end
  end
end
