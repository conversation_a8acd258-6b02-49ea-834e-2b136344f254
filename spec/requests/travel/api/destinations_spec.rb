require 'swagger_helper'

RSpec.describe 'travel/api/destinations', type: :request, capture_examples: true, tags: ["travel api"] do
  destination_ref = {
    type: :object, properties: {
      destination: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          stars_count: { type: :integer, description: '收藏人数' },
          hotted_at: { type: :datetime, description: '' },
          is_hotted: { type: :boolean, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          desc: { type: :string, description: '描述' },
          content: { type: :text, description: '内容' },
          icon: { type: :string, description: 'icon' },
          position: { type: :integer, description: '排序' },
          cover_image: { type: :jsonb, description: '封面图' },
          score: { type: :float, description: '评分' },
          price: { type: :float, description: '价格' },
          used_count: { type: :integer, description: '旅行人数' },
          views_count: { type: :integer, description: '浏览人数' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  destination_value = FactoryBot.attributes_for(:travel_destination)

  before :each do
  end

  path '/travel/api/destinations' do

    get(summary: 'list destinations') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_destination_count
        }
      end
    end
  end

  path '/travel/api/destinations/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show destination') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_destinations.first.id }
        it {
          body = JSON.parse(response.body)
          destination = @travel_destinations.first
          expect(body['app_id']).to eq destination.app_id
          expect(body['creator_id']).to eq destination.creator_id
          expect(body['type']).to eq destination.type
          expect(body['stars_count']).to eq destination.stars_count
          expect(body['hotted_at']).to eq destination.hotted_at
          expect(body['is_hotted']).to eq destination.is_hotted
          expect(body['name']).to eq destination.name
          expect(body['state']).to eq destination.state
          expect(body['desc']).to eq destination.desc
          expect(body['content']).to eq destination.content
          expect(body['icon']).to eq destination.icon
          expect(body['position']).to eq destination.position
          expect(body['cover_image']).to eq destination.cover_image
          expect(body['score']).to eq destination.score
          expect(body['price']).to eq destination.price
          expect(body['used_count']).to eq destination.used_count
          expect(body['views_count']).to eq destination.views_count
          expect(body['payload']).to eq destination.payload
        }
      end
    end
  end
end
