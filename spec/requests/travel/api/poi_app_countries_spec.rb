require 'swagger_helper'

RSpec.describe 'travel/api/poi_app_countries', type: :request, capture_examples: true, tags: ["travel api"] do
  app_country_ref = {
    type: :object, properties: {
      app_country: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          country_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          color: { type: :string, description: '颜色' },
          position: { type: :integer, description: '位置' },
          conf: { type: :jsonb, description: '其他配置' },
          name: { type: :string, description: '国家名称' },
          state: { type: :string, description: '状态' },
          content: { type: :jsonb, description: '内容' },
          icon: { type: :string, description: 'icon' },
          cover_image: { type: :jsonb, description: '封面图' },
          score: { type: :float, description: '评分' },
          used_count: { type: :integer, description: '旅行人数' },
          views_count: { type: :integer, description: '浏览人数' },
          stars_count: { type: :integer, description: '收藏人数' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  app_country_value = FactoryBot.attributes_for(:travel_poi_app_country)

  before :each do
  end

  path '/travel/api/poi_app_countries' do

    get(summary: 'list poi_app_countries') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_app_country_count
        }
      end
    end
  end

  path '/travel/api/poi_app_countries/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show poi_app_country') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_poi_app_countries.first.id }
        it {
          body = JSON.parse(response.body)
          app_country = @travel_poi_app_countries.first
          expect(body['app_id']).to eq app_country.app_id
          expect(body['country_id']).to eq app_country.country_id
          expect(body['model_flag']).to eq app_country.model_flag
          expect(body['model_payload']).to eq app_country.model_payload
          expect(body['model_payload_summary']).to eq app_country.model_payload_summary
          expect(body['model_detail']).to eq app_country.model_detail
          expect(body['color']).to eq app_country.color
          expect(body['position']).to eq app_country.position
          expect(body['conf']).to eq app_country.conf
          expect(body['name']).to eq app_country.name
          expect(body['state']).to eq app_country.state
          expect(body['content']).to eq app_country.content
          expect(body['icon']).to eq app_country.icon
          expect(body['cover_image']).to eq app_country.cover_image
          expect(body['score']).to eq app_country.score
          expect(body['used_count']).to eq app_country.used_count
          expect(body['views_count']).to eq app_country.views_count
          expect(body['stars_count']).to eq app_country.stars_count
          expect(body['payload']).to eq app_country.payload
        }
      end
    end
  end
end
