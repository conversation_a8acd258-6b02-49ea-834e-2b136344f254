require 'swagger_helper'

RSpec.describe 'travel/user/activities', type: :request, capture_examples: true, tags: ["travel user"] do
  activity_ref = {
    type: :object, properties: {
      activity: {
        type: :object, properties: {
          creator_id: { type: :integer, description: '' },
          activity_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          duration: { type: :integer, description: '天数' },
          state: { type: :string, description: '状态' },
          start_at: { type: :date, description: '开始日期' },
          end_at: { type: :date, description: '结束日期' },
          contact_info: { type: :jsonb, description: '合同信息' },
          title: { type: :string, description: '标题' },
          seq: { type: :string, description: '编号' },
        }
      }
    }
  }
  activity_value = FactoryBot.attributes_for(:travel_activity)

  before :each do
  end

  path '/travel/user/activities' do

    get(summary: 'list activities') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_activity_count
        }
      end
    end
  end

  path '/travel/user/activities/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_activities.first.id }
        it {
          body = JSON.parse(response.body)
          activity = @travel_activities.first
          expect(body['creator_id']).to eq activity.creator_id
          expect(body['activity_temp_id']).to eq activity.activity_temp_id
          expect(body['app_id']).to eq activity.app_id
          expect(body['model_flag']).to eq activity.model_flag
          expect(body['model_payload']).to eq activity.model_payload
          expect(body['model_payload_summary']).to eq activity.model_payload_summary
          expect(body['model_detail']).to eq activity.model_detail
          expect(body['name']).to eq activity.name
          expect(body['desc']).to eq activity.desc
          expect(body['duration']).to eq activity.duration
          expect(body['state']).to eq activity.state
          expect(body['start_at']).not_to be_nil
          expect(body['end_at']).to eq activity.end_at.to_s
          expect(body['contact_info']).to eq activity.contact_info
          expect(body['title']).to eq activity.title
          expect(body['seq']).to eq activity.seq
        }
      end
    end
  end
end
