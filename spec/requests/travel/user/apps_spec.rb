require 'swagger_helper'

RSpec.describe 'travel/user/apps', type: :request, capture_examples: true, tags: ["travel user"] do
  app_ref = {
    type: :object, properties: {
      app: {
        type: :object, properties: {
          code: { type: :string, description: '应用标识' },
          name: { type: :string, description: '应用的名称' },
          settings: { type: :jsonb, description: '配置信息' },
        }
      }
    }
  }
  app_value = FactoryBot.attributes_for(:app)

  before :each do
  end

  # path '/travel/user/apps/{id}/ta_resource_statistic' do
  #   parameter 'id', in: :path, type: :string

  #   post(summary: 'ta_resource_statistic app') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :app, in: :body, schema: app_ref
  #     response(201, description: 'successful') do
  #       let(:id) { @apps.first.id }
  #       let(:app) do
  #         { app: app_value }
  #       end
  #     end
  #   end
  # end

  # path '/travel/user/apps/ta_collection_statistic' do

  #   post(summary: 'ta_collection_statistic app') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :app, in: :body, schema: app_ref
  #     response(201, description: 'successful') do
  #       let(:app) do
  #         { app: app_value }
  #       end
  #     end
  #   end
  # end
end
