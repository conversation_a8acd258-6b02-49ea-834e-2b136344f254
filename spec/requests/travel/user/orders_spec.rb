require 'swagger_helper'

RSpec.describe 'travel/user/orders', type: :request, capture_examples: true, tags: ["travel user"] do
  order_ref = {
    type: :object, properties: {
      order: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          seq: { type: :string, description: '编号' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          ota_no: { type: :string, description: 'OTA 订单号' },
          ota_state: { type: :string, description: 'OTA 订单状态' },
          state: { type: :string, description: '状态' },
          meta: { type: :jsonb, description: '元数据' },
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          channel_id: { type: :integer, description: '' },
          product_id: { type: :integer, description: '' },
        }
      }
    }
  }
  order_value = FactoryBot.attributes_for(:travel_order)

  before :each do
  end

  path '/travel/user/orders' do

    get(summary: 'list orders') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_order_count
        }
      end
    end
  end

  path '/travel/user/orders/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show order') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_orders.first.id }
        it {
          body = JSON.parse(response.body)
          order = @travel_orders.first
          expect(body['model_flag']).to eq order.model_flag
          expect(body['model_payload']).to eq order.model_payload
          expect(body['model_payload_summary']).to eq order.model_payload_summary
          expect(body['model_detail']).to eq order.model_detail
          expect(body['seq']).to eq order.seq
          expect(body['type']).to eq order.type
          expect(body['name']).to eq order.name
          expect(body['ota_no']).to eq order.ota_no
          expect(body['ota_state']).to eq order.ota_state
          expect(body['state']).to eq order.state
          expect(body['meta']).not_to be_nil
          expect(body['app_id']).to eq order.app_id
          expect(body['channel_id']).to eq order.channel_id
          expect(body['product_id']).to eq order.product_id
          expect(body['traveler_num']).to eq order.traveler_num
          expect(body['trip_start_date']).to eq order.trip_start_date
          expect(body['trip_end_date']).to eq order.trip_end_date
          expect(body['trip_days']).to eq order.trip_days
        }
      end
    end
  end
end
