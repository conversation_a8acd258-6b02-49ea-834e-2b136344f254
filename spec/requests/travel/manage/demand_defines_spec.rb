require 'swagger_helper'

RSpec.describe '/travel/manage/demand_defines', type: :request, capture_examples: true, tags: [" travel manage"] do
  demand_define_ref = {
    type: :object, properties: {
      demand_define: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          cover_image: { type: :jsonb, description: '图标' },
          form: { type: :jsonb, description: '表单' },
        }
      }
    }
  }
  demand_define_value = FactoryBot.attributes_for(:travel_demand_define)

  before :each do
    @user.add_role(:travel_admin)
    @travel_demand_define_count = 5
    @travel_demand_defines = FactoryBot.create_list(:travel_demand_define, @travel_demand_define_count, app: @app)
  end

  path '/travel/manage/demand_defines' do

    get(summary: 'list demand_defines') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_define_count
        }
      end
    end

    post(summary: 'create demand_define') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_define, in: :body, schema: demand_define_ref
      response(201, description: 'successful') do
        let(:demand_define) do
          { demand_define: demand_define_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_flag']).to eq demand_define_value[:model_flag]
          expect(body['model_payload']).to eq demand_define_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_define_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_define_value[:model_detail]
          expect(body['name']).to eq demand_define_value[:name]
          expect(body['cover_image']).to eq demand_define_value[:cover_image]
          expect(body['form']).to eq demand_define_value[:form]
        }
      end
    end
  end

  path '/travel/manage/demand_defines/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_define') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_demand_defines.first.id }
        it {
          body = JSON.parse(response.body)
          demand_define = @travel_demand_defines.first
          expect(body['model_flag']).to eq demand_define.model_flag
          expect(body['model_payload']).to eq demand_define.model_payload
          expect(body['model_payload_summary']).to eq demand_define.model_payload_summary
          expect(body['model_detail']).to eq demand_define.model_detail
          expect(body['name']).to eq demand_define.name
          expect(body['cover_image']).to eq demand_define.cover_image
          expect(body['form']).to eq demand_define.form
        }
      end
    end

    patch(summary: 'update demand_define') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_define, in: :body, schema: demand_define_ref
      response(201, description: 'successful') do
        let(:id) { @travel_demand_defines.first.id }
        let(:demand_define) do
          { demand_define: demand_define_value }
        end
      end
    end

    delete(summary: 'delete demand_define') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_demand_defines.first.id }
        it {
          expect(Travel::DemandDefine.count).to eq(@travel_demand_define_count-1)
        }
      end
    end
  end
end
