require 'swagger_helper'

RSpec.describe '/travel/manage/order_divides', type: :request, capture_examples: true, tags: [" travel manage"] do
  order_divide_ref = {
    type: :object, properties: {
      order_divide: {
        type: :object, properties: {
          order_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          weight: { type: :float, description: '权重' },
        }
      }
    }
  }
  order_divide_value = FactoryBot.attributes_for(:travel_order_divide)

  before :each do
    @user.add_role(:travel_admin)
    @order = create(:travel_order, app: @app)
    @travel_order_divide_count = 5
    @travel_order_divides = FactoryBot.create_list(:travel_order_divide, @travel_order_divide_count, order: @order, user: @user)
  end

  path '/travel/manage/orders/{order_id}/order_divides' do
    parameter 'order_id', in: :path, type: :string

    get(summary: 'list order_divides') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_order_divide_count
        }
      end
    end

    post(summary: 'create order_divide') do
      produces 'application/json'
      consumes 'application/json'
      parameter :order_divide, in: :body, schema: order_divide_ref
      response(201, description: 'successful') do
        let(:order_id) { @order.id }
        let(:order_divide) do
          {
            order_divide: order_divide_value.merge(
              order_id: @order.id,
              user_id: @user.id,
            )
          }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['order_id']).to eq @order.id
          expect(body['user_id']).to eq @user.id
          expect(body['weight']).to eq order_divide_value[:weight]
        }
      end
    end
  end

  path '/travel/manage/orders/{order_id}/order_divides/{id}' do
    parameter 'order_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show order_divide') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_order_divides.first.id }
        it {
          body = JSON.parse(response.body)
          order_divide = @travel_order_divides.first
          expect(body['order_id']).to eq order_divide.order_id
          expect(body['user_id']).to eq order_divide.user_id
          expect(body['weight']).to eq order_divide.weight
        }
      end
    end

    patch(summary: 'update order_divide') do
      produces 'application/json'
      consumes 'application/json'
      parameter :order_divide, in: :body, schema: order_divide_ref
      response(201, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_order_divides.first.id }
        let(:order_divide) do
          { order_divide: order_divide_value }
        end
      end
    end

    delete(summary: 'delete order_divide') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_order_divides.first.id }
        it {
          expect(Travel::OrderDivide.count).to eq(@travel_order_divide_count-1)
        }
      end
    end
  end
end
