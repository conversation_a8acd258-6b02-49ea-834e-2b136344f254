require 'swagger_helper'

RSpec.describe '/travel/manage/activity_temp_dirs', type: :request, capture_examples: true, tags: [" travel manage"] do
  activity_temp_dir_ref = {
    type: :object, properties: {
      activity_temp_dir: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :string, description: '描述' },
        }
      }
    }
  }
  activity_temp_dir_value = FactoryBot.attributes_for(:travel_activity_temp_dir).with_indifferent_access

  before :each do
    @user.add_role(:travel_admin)
    @travel_activity_temp_dir_count = 5
    @travel_activity_temp_dirs = FactoryBot.create_list(:travel_activity_temp_dir, @travel_activity_temp_dir_count, app: @app)
  end

  path '/travel/manage/activity_temp_dirs' do

    get(summary: 'list activity_temp_dirs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_activity_temp_dir_count
        }
      end
    end

    post(summary: 'create activity_temp_dir') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity_temp_dir, in: :body, schema: activity_temp_dir_ref
      response(201, description: 'successful') do
        let(:activity_temp_dir) do
          { activity_temp_dir: activity_temp_dir_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq activity_temp_dir_value[:model_flag]
          expect(body['model_payload']).to eq activity_temp_dir_value[:model_payload]
          expect(body['model_payload_summary']).to eq activity_temp_dir_value[:model_payload_summary]
          expect(body['model_detail']).to eq activity_temp_dir_value[:model_detail]
          expect(body['name']).to eq activity_temp_dir_value[:name]
          expect(body['desc']).to eq activity_temp_dir_value[:desc]
        }
      end
    end
  end

  path '/travel/manage/activity_temp_dirs/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity_temp_dir') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_activity_temp_dirs.first.id }
        it {
          body = JSON.parse(response.body)
          activity_temp_dir = @travel_activity_temp_dirs.first
          expect(body['app_id']).to eq activity_temp_dir.app_id
          expect(body['model_flag']).to eq activity_temp_dir.model_flag
          expect(body['model_payload']).to eq activity_temp_dir.model_payload
          expect(body['model_payload_summary']).to eq activity_temp_dir.model_payload_summary
          expect(body['model_detail']).to eq activity_temp_dir.model_detail
          expect(body['name']).to eq activity_temp_dir.name
          expect(body['desc']).to eq activity_temp_dir.desc
        }
      end
    end

    patch(summary: 'update activity_temp_dir') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity_temp_dir, in: :body, schema: activity_temp_dir_ref
      response(201, description: 'successful') do
        let(:id) { @travel_activity_temp_dirs.first.id }
        let(:activity_temp_dir) do
          { activity_temp_dir: activity_temp_dir_value.except('app_id') }
        end
      end
    end

    delete(summary: 'delete activity_temp_dir') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_activity_temp_dirs.first.id }
        it {
          expect(Travel::ActivityTempDir.count).to eq(@travel_activity_temp_dir_count-1)
        }
      end
    end
  end
end
