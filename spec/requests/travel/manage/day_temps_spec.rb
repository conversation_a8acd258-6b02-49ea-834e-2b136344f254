require 'swagger_helper'

RSpec.describe '/travel/manage/day_temps', type: :request, capture_examples: true, tags: [" travel manage"] do
  day_temp_ref = {
    type: :object, properties: {
      day_temp: {
        type: :object, properties: {
          activity_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  day_temp_value = FactoryBot.attributes_for(:travel_day_temp)

  before :each do
    @user.add_role(:travel_admin)
    @activity_temp = FactoryBot.create(:travel_activity_temp, app: @app, duration: 10)

    @travel_day_temp_count = @activity_temp.day_temps.count
    @travel_day_temps = @activity_temp.day_temps
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/day_temps' do
    parameter 'activity_temp_id', in: :path, type: :string

    get(summary: 'list day_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_day_temp_count
        }
      end
    end

    post(summary: 'create day_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :day_temp, in: :body, schema: day_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:day_temp) do
          { day_temp: day_temp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['activity_temp_id']).to eq @activity_temp.id
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq day_temp_value[:model_flag]
          expect(body['model_payload']).to eq day_temp_value[:model_payload]
          expect(body['model_payload_summary']).to eq day_temp_value[:model_payload_summary]
          expect(body['model_detail']).to eq day_temp_value[:model_detail]
          expect(body['name']).to eq day_temp_value[:name]
          expect(body['desc']).to eq day_temp_value[:desc]
          expect(body['position']).to eq day_temp_value[:position]
        }
      end
    end
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/day_temps/{id}' do
    parameter 'activity_temp_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show day_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_day_temps.first.id }
        it {
          body = JSON.parse(response.body)
          day_temp = @travel_day_temps.first.reload
          expect(body['activity_temp_id']).to eq day_temp.activity_temp_id
          expect(body['app_id']).to eq day_temp.app_id
          expect(body['model_flag']).to eq day_temp.model_flag
          expect(body['model_payload']).to eq day_temp.model_payload
          expect(body['model_payload_summary']).to eq day_temp.model_payload_summary
          expect(body['model_detail']).to eq day_temp.model_detail
          expect(body['name']).to eq day_temp.name
          expect(body['desc']).to eq day_temp.desc
          expect(body['position']).to eq day_temp.position
        }
      end
    end

    patch(summary: 'update day_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :day_temp, in: :body, schema: day_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_day_temps.first.id }
        let(:day_temp) do
          { day_temp: day_temp_value }
        end
      end
    end

    delete(summary: 'delete day_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_day_temps.first.id }
        it {
          expect(@activity_temp.day_temps.count).to eq(@travel_day_temp_count-1)
        }
      end
    end
  end
end
