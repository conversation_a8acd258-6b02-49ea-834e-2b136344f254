require 'swagger_helper'

RSpec.describe '/travel/manage/activity_temps', type: :request, capture_examples: true, tags: [" travel manage"] do
  activity_temp_ref = {
    type: :object, properties: {
      activity_temp: {
        type: :object, properties: {
          creator_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          duration: { type: :integer, description: '天数' },
          state: { type: :string, description: '状态' },
        }
      }
    }
  }
  activity_temp_value = FactoryBot.attributes_for(:travel_activity_temp)

  before :each do
    @user.add_role(:travel_admin)
    @travel_activity_temps = FactoryBot.create_list(:travel_activity_temp, 5, app: @app)
    @travel_activity_temp_count = @app.travel_activity_temps.count
  end

  path '/travel/manage/activity_temps' do

    get(summary: 'list activity_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_activity_temp_count
        }
      end
    end

    post(summary: 'create activity_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity_temp, in: :body, schema: activity_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp) do
          { activity_temp: activity_temp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['creator_id']).to eq @user.id
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq activity_temp_value[:model_flag]
          expect(body['model_payload']).to eq activity_temp_value[:model_payload]
          expect(body['model_payload_summary']).to eq activity_temp_value[:model_payload_summary]
          expect(body['model_detail']).to eq activity_temp_value[:model_detail]
          expect(body['name']).to eq activity_temp_value[:name]
          expect(body['desc']).to eq activity_temp_value[:desc]
          expect(body['duration']).to eq activity_temp_value[:duration]
          expect(body['state']).to eq activity_temp_value[:state]
        }
      end
    end
  end

  path '/travel/manage/activity_temps/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_activity_temps.first.id }
        it {
          body = JSON.parse(response.body)
          activity_temp = @travel_activity_temps.first
          expect(body['creator_id']).to eq activity_temp.creator_id
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq activity_temp.model_flag
          expect(body['model_payload']).to eq activity_temp.model_payload
          expect(body['model_payload_summary']).to eq activity_temp.model_payload_summary
          expect(body['model_detail']).to eq activity_temp.model_detail
          expect(body['name']).to eq activity_temp.name
          expect(body['desc']).to eq activity_temp.desc
          expect(body['duration']).to eq activity_temp.duration
          expect(body['state']).to eq activity_temp.state
        }
      end
    end

    patch(summary: 'update activity_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity_temp, in: :body, schema: activity_temp_ref
      response(201, description: 'successful') do
        let(:id) { @travel_activity_temps.first.id }
        let(:activity_temp) do
          { activity_temp: activity_temp_value }
        end
      end
    end

    delete(summary: 'delete activity_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_activity_temps.first.id }
        it {
          expect(Travel::ActivityTemp.count).to eq(@travel_activity_temp_count-1)
        }
      end
    end
  end
end
