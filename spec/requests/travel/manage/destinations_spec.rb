require 'swagger_helper'

RSpec.describe 'travel/manage/destinations', type: :request, capture_examples: true, tags: ["travel manage"] do
  destination_ref = {
    type: :object, properties: {
      destination: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          hotted_at: { type: :datetime, description: '' },
          is_hotted: { type: :boolean, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          desc: { type: :string, description: '描述' },
          icon: { type: :string, description: 'icon' },
          content: { type: :text, description: '内容' },
          position: { type: :integer, description: '排序' },
          cover_image: { type: :jsonb, description: '封面图' },
          score: { type: :float, description: '评分' },
          price: { type: :float, description: '价格' },
          used_count: { type: :integer, description: '旅行人数' },
          views_count: { type: :integer, description: '浏览人数' },
          stars_count: { type: :integer, description: '收藏人数' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  destination_value = FactoryBot.attributes_for(:travel_destination)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/destinations' do

    get(summary: 'list destinations') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_destination_count
        }
      end
    end

    post(summary: 'create destination') do
      produces 'application/json'
      consumes 'application/json'
      parameter :destination, in: :body, schema: destination_ref
      response(201, description: 'successful') do
        let(:destination) do
          { destination: destination_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq @user.id
          expect(body['type']).to eq destination_value[:type]
          expect(body['hotted_at']).to eq destination_value[:hotted_at]
          expect(body['is_hotted']).to eq destination_value[:is_hotted]
          expect(body['name']).to eq destination_value[:name]
          expect(body['state']).to eq destination_value[:state]
          expect(body['desc']).to eq destination_value[:desc]
          expect(body['icon']).to eq destination_value[:icon]
          expect(body['content']).to eq destination_value[:content]
          expect(body['position']).not_to be_nil
          expect(body['cover_image']).to eq destination_value[:cover_image]
          expect(body['score']).to eq destination_value[:score]
          expect(body['price']).to eq destination_value[:price]
          expect(body['used_count']).to eq destination_value[:used_count]
          expect(body['views_count']).to eq destination_value[:views_count]
          expect(body['stars_count']).to eq destination_value[:stars_count]
          expect(body['payload']).to eq destination_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/destinations/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show destination') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_destinations.first.id }
        it {
          body = JSON.parse(response.body)
          destination = @travel_destinations.first
          expect(body['app_id']).to eq destination.app_id
          expect(body['creator_id']).to eq destination.creator_id
          expect(body['type']).to eq destination.type
          expect(body['hotted_at']).to eq destination.hotted_at
          expect(body['is_hotted']).to eq destination.is_hotted
          expect(body['name']).to eq destination.name
          expect(body['state']).to eq destination.state
          expect(body['desc']).to eq destination.desc
          expect(body['icon']).to eq destination.icon
          expect(body['content']).to eq destination.content
          expect(body['position']).to eq destination.position
          expect(body['cover_image']).to eq destination.cover_image
          expect(body['score']).to eq destination.score
          expect(body['price']).to eq destination.price
          expect(body['used_count']).to eq destination.used_count
          expect(body['views_count']).to eq destination.views_count
          expect(body['stars_count']).to eq destination.stars_count
          expect(body['payload']).to eq destination.payload
        }
      end
    end

    patch(summary: 'update destination') do
      produces 'application/json'
      consumes 'application/json'
      parameter :destination, in: :body, schema: destination_ref
      response(201, description: 'successful') do
        let(:id) { @travel_destinations.first.id }
        let(:destination) do
          { destination: destination_value }
        end
      end
    end

    delete(summary: 'delete destination') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_destinations.first.id }
        it {
          expect(Travel::Destination.count).to eq(@travel_destination_count-1)
        }
      end
    end
  end
end
