require 'swagger_helper'

RSpec.describe '/travel/manage/currencies', type: :request, capture_examples: true, tags: [" travel manage"] do
  currency_ref = {
    type: :object, properties: {
      currency: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          unit: { type: :string, description: '单位' },
          symbol: { type: :string, description: '符号' },
          ratio: { type: :decimal, description: '汇率' },
        }
      }
    }
  }

  currency_value = FactoryBot.attributes_for(:travel_currency)

  before :each do
    @user.add_role(:travel_admin)
  end

  path '/travel/manage/currencies' do

    get(summary: 'list currencies') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_currency_count
        }
      end
    end

    post(summary: 'create currency') do
      produces 'application/json'
      consumes 'application/json'
      parameter :currency, in: :body, schema: currency_ref
      response(201, description: 'successful') do
        let(:currency) do
          { currency: currency_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_flag']).to eq currency_value[:model_flag]
          expect(body['model_payload']).to eq currency_value[:model_payload]
          expect(body['model_payload_summary']).to eq currency_value[:model_payload_summary]
          expect(body['model_detail']).to eq currency_value[:model_detail]
          expect(body['name']).to eq currency_value[:name]
          expect(body['unit']).to eq currency_value[:unit]
          expect(body['symbol']).to eq currency_value[:symbol]
          expect(body['ratio']).to eq currency_value[:ratio]
        }
      end
    end
  end

  path '/travel/manage/currencies/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show currency') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_currencies.first.id }
        it {
          body = JSON.parse(response.body)
          currency = @travel_currencies.first
          expect(body['model_flag']).to eq currency.model_flag
          expect(body['model_payload']).to eq currency.model_payload
          expect(body['model_payload_summary']).to eq currency.model_payload_summary
          expect(body['model_detail']).to eq currency.model_detail
          expect(body['name']).to eq currency.name
          expect(body['unit']).to eq currency.unit
          expect(body['symbol']).to eq currency.symbol
          expect(body['ratio']).not_to be_nil
        }
      end
    end

    patch(summary: 'update currency') do
      produces 'application/json'
      consumes 'application/json'
      parameter :currency, in: :body, schema: currency_ref
      response(201, description: 'successful') do
        let(:id) { @travel_currencies.first.id }
        let(:currency) do
          { currency: currency_value }
        end
      end
    end

    delete(summary: 'delete currency') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_currencies.first.id }
        it {
          expect(Travel::Currency.count).to eq(@travel_currency_count-1)
        }
      end
    end
  end
end
