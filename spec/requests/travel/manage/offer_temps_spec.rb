require 'swagger_helper'

RSpec.describe '/travel/manage/offer_temps', type: :request, capture_examples: true, tags: [" travel manage"] do
  offer_temp_ref = {
    type: :object, properties: {
      offer_temp: {
        type: :object, properties: {
          product_id: { type: :integer, description: '' },
          activity_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          supplier_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
        }
      }
    }
  }
  offer_temp_value = FactoryBot.attributes_for(:travel_offer_temp)

  before :each do
    @user.add_role :travel_admin

    @travel_offer_temp_count = 5
    @travel_offer_temps = FactoryBot.create_list(:travel_offer_temp, @travel_offer_temp_count, activity_temp: @travel_activity_temp, product: @travel_product)
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/offer_temps' do
    parameter 'activity_temp_id', in: :path, type: :string

    get(summary: 'list offer_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @travel_activity_temp.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_offer_temp_count
        }
      end
    end

    post(summary: 'create offer_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :offer_temp, in: :body, schema: offer_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @travel_activity_temp.id }
        let(:offer_temp) do
          { offer_temp: offer_temp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['product_id']).to eq offer_temp_value[:product_id]
          expect(body['activity_temp_id']).to eq @travel_activity_temp.id
          expect(body['app_id']).to eq @app.id
          expect(body['supplier_id']).to eq offer_temp_value[:supplier_id]
          expect(body['model_flag']).to eq offer_temp_value[:model_flag]
          expect(body['model_payload']).to eq offer_temp_value[:model_payload]
          expect(body['model_payload_summary']).to eq offer_temp_value[:model_payload_summary]
          expect(body['model_detail']).to eq offer_temp_value[:model_detail]
          expect(body['name']).to eq offer_temp_value[:name]
          expect(body['amount']).not_to be_nil
          expect(body['amount_unit']).to eq offer_temp_value[:amount_unit]
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq offer_temp_value[:amount_rmb]
        }
      end
    end
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/offer_temps/{id}' do
    parameter 'activity_temp_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show offer_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @travel_activity_temp.id }
        let(:id) { @travel_offer_temps.first.id }
        it {
          body = JSON.parse(response.body)
          offer_temp = @travel_offer_temps.first
          expect(body['product_id']).to eq offer_temp.product_id
          expect(body['activity_temp_id']).to eq offer_temp.activity_temp_id
          expect(body['app_id']).to eq offer_temp.app_id
          expect(body['supplier_id']).to eq offer_temp.supplier_id
          expect(body['model_flag']).to eq offer_temp.model_flag
          expect(body['model_payload']).to eq offer_temp.model_payload
          expect(body['model_payload_summary']).to eq offer_temp.model_payload_summary
          expect(body['model_detail']).to eq offer_temp.model_detail
          # expect(body['name']).to eq offer_temp.name
          expect(body['amount']).to eq offer_temp.amount.to_d.to_s
          expect(body['amount_unit']).to eq offer_temp.amount_unit
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq offer_temp.amount_rmb
        }
      end
    end

    patch(summary: 'update offer_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :offer_temp, in: :body, schema: offer_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @travel_activity_temp.id }
        let(:id) { @travel_offer_temps.first.id }
        let(:offer_temp) do
          { offer_temp: offer_temp_value.merge('app_id' => @app) }
        end
      end
    end

    delete(summary: 'delete offer_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_temp_id) { @travel_activity_temp.id }
        let(:id) { @travel_offer_temps.first.id }
        it {
          expect(Travel::OfferTemp.count).to eq(@travel_offer_temp_count-1)
        }
      end
    end
  end

  path '/travel/manage/products/{product_id}/offer_temps' do
    parameter 'product_id', in: :path, type: :string

    get(summary: 'list offer_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:product_id) { @travel_product.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_offer_temp_count
        }
      end
    end
  end
end
