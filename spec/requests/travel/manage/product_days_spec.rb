require 'swagger_helper'

RSpec.describe '/travel/manage/product_days', type: :request, capture_examples: true, tags: [" travel manage"] do
  product_day_ref = {
    type: :object, properties: {
      product_day: {
        type: :object, properties: {
          product_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  product_day_value = FactoryBot.attributes_for(:travel_product_day)

  before :each do
    @user.add_role(:travel_admin)
    @product = create(:travel_product, duration: 5, app: @app)
    @travel_product_day_count = 5
    @travel_product_days = @product.product_days
  end

  path '/travel/manage/products/{product_id}/product_days' do
    parameter 'product_id', in: :path, type: :string

    get(summary: 'list product_days') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:product_id) { @product.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_product_day_count
        }
      end
    end

    post(summary: 'create product_day') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product_day, in: :body, schema: product_day_ref
      response(201, description: 'successful') do
        let(:product_id) { @product.id }
        let(:product_day) do
          { product_day: product_day_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['product_id']).to eq @product.id
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq product_day_value[:model_flag]
          expect(body['model_payload']).to eq product_day_value[:model_payload]
          expect(body['model_payload_summary']).to eq product_day_value[:model_payload_summary]
          expect(body['model_detail']).to eq product_day_value[:model_detail]
          expect(body['name']).not_to be_nil
          # expect(body['position']).to eq product_day_value[:position]
        }
      end
    end
  end

  path '/travel/manage/products/{product_id}/product_days/{id}' do
    parameter 'product_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show product_day') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:product_id) { @product.id }
        let(:id) { @travel_product_days.first.id }
        it {
          body = JSON.parse(response.body)
          product_day = @travel_product_days.first
          expect(body['product_id']).to eq product_day.product_id
          expect(body['app_id']).to eq product_day.app_id
          expect(body['model_flag']).to eq product_day.model_flag
          expect(body['model_payload']).to eq product_day.model_payload
          expect(body['model_payload_summary']).to eq product_day.model_payload_summary
          expect(body['model_detail']).to eq product_day.model_detail
          expect(body['name']).to eq product_day.name
          expect(body['position']).to eq product_day.position
        }
      end
    end

    patch(summary: 'update product_day') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product_day, in: :body, schema: product_day_ref
      response(201, description: 'successful') do
        let(:product_id) { @product.id }
        let(:id) { @travel_product_days.first.id }
        let(:product_day) do
          { product_day: product_day_value }
        end
      end
    end

    delete(summary: 'delete product_day') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:product_id) { @product.id }
        let(:id) { @travel_product_days.first.id }
        it {
          # expect(Travel::ProductDay.count).to eq(@travel_product_day_count-1)
        }
      end
    end
  end
end
