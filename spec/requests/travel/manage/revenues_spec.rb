require 'swagger_helper'

RSpec.describe 'travel/manage/revenues', type: :request, capture_examples: true, tags: ["travel manage"] do
  before :each do
    @user.add_role :travel_manage
    @order = @travel_order
    create_list :travel_revenue, 2, app: @app, source: @order
    @travel_revenues = Travel::Revenue.all
    @travel_revenue = @travel_revenues.first
    @travel_revenue_count = @travel_revenues.count
  end

  path '/travel/manage/orders/{order_id}/revenues' do
    parameter 'order_id', in: :path, type: :string

    get(summary: 'list revenues') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_revenue_count
        }
      end
    end
  end

  path '/travel/manage/orders/{order_id}/revenues/{id}' do
    parameter 'order_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show revenue') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_revenues.first.id }
        it {
          body = JSON.parse(response.body)
          revenue = @travel_revenues.first
          expect(body['app_id']).to eq revenue.app_id
          expect(body['source_type']).to eq revenue.source_type
          expect(body['source_id']).to eq revenue.source_id
          expect(body['data_type']).to eq revenue.data_type
          expect(body['data_id']).to eq revenue.data_id
          expect(body['model_flag']).to eq revenue.model_flag
          expect(body['model_payload']).to eq revenue.model_payload
          expect(body['model_payload_summary']).to eq revenue.model_payload_summary
          expect(body['model_detail']).to eq revenue.model_detail
          expect(body['seq']).to eq revenue.seq
          expect(body['amount']).to eq revenue.amount
          expect(body['amount_unit']).to eq revenue.amount_unit
          expect(body['amount_ratio']).to eq revenue.amount_ratio.to_s
          expect(body['amount_rmb']).to eq revenue.amount_rmb
          expect(body['mode']).to eq revenue.mode
          expect(body['remark']).to eq revenue.remark
          expect(body['type']).to eq revenue.type
          expect(body['attachments']).to eq revenue.attachments
        }
      end
    end
  end
end
