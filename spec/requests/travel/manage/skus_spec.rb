require 'swagger_helper'

RSpec.describe 'travel/manage/skus', type: :request, capture_examples: true, tags: ["travel manage"] do
  sku_ref = {
    type: :object, properties: {
      sku: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          product_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          seq: { type: :string, description: '编号' },
          comments_count: { type: :integer, description: '评论数量' },
          comment_conf: { type: :string, description: '' },
          recommended_at: { type: :datetime, description: '' },
          is_recommended: { type: :boolean, description: '' },
          name: { type: :string, description: '商品名称' },
          state: { type: :string, description: '商品状态' },
          content: { type: :jsonb, description: '商品内容' },
          cover_image: { type: :jsonb, description: '商品图片(轮播图)' },
          price: { type: :float, description: '商品价格' },
          origin_price: { type: :float, description: '原始价格' },
          position: { type: :integer, description: '商品排序' },
          total_number: { type: :float, description: '库存数量' },
          freeze_number: { type: :float, description: '冻结数量' },
          sale_count: { type: :integer, description: '销售数量' },
          stars_count: { type: :integer, description: '收藏数量' },
          score: { type: :float, description: '评分' },
          origin_score: { type: :float, description: '原始评分' },
          origin_sale_count: { type: :integer, description: '原始销售数量' },
          payload: { type: :jsonb, description: '扩展信息' },
          rate: { type: :float, description: '手续费率' },
        }
      }
    }
  }
  sku_value = FactoryBot.attributes_for(:travel_sku)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/skus' do

    get(summary: 'list skus') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_sku_count
        }
      end
    end

    post(summary: 'create sku') do
      produces 'application/json'
      consumes 'application/json'
      parameter :sku, in: :body, schema: sku_ref
      response(201, description: 'successful') do
        let(:sku) do
          { sku: sku_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq sku_value[:app_id]
          expect(body['product_id']).to eq sku_value[:product_id]
          expect(body['creator_id']).to eq sku_value[:creator_id]
          expect(body['type']).to eq sku_value[:type]
          expect(body['seq']).to eq sku_value[:seq]
          expect(body['comments_count']).to eq sku_value[:comments_count]
          expect(body['comment_conf']).to eq sku_value[:comment_conf]
          expect(body['recommended_at']).to eq sku_value[:recommended_at]
          expect(body['is_recommended']).to eq sku_value[:is_recommended]
          expect(body['name']).to eq sku_value[:name]
          expect(body['state']).to eq sku_value[:state]
          expect(body['content']).to eq sku_value[:content]
          expect(body['cover_image']).to eq sku_value[:cover_image]
          expect(body['price']).to eq sku_value[:price]
          expect(body['origin_price']).to eq sku_value[:origin_price]
          expect(body['position']).to eq sku_value[:position]
          expect(body['total_number']).to eq sku_value[:total_number]
          expect(body['freeze_number']).to eq sku_value[:freeze_number]
          expect(body['sale_count']).to eq sku_value[:sale_count]
          expect(body['stars_count']).to eq sku_value[:stars_count]
          expect(body['score']).to eq sku_value[:score]
          expect(body['origin_score']).to eq sku_value[:origin_score]
          expect(body['origin_sale_count']).to eq sku_value[:origin_sale_count]
          expect(body['payload']).to eq sku_value[:payload]
          expect(body['rate']).to eq sku_value[:rate]
        }
      end
    end
  end

  path '/travel/manage/skus/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show sku') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_skus.first.id }
        it {
          body = JSON.parse(response.body)
          sku = @travel_skus.first
          expect(body['app_id']).to eq sku.app_id
          expect(body['product_id']).to eq sku.product_id
          expect(body['creator_id']).to eq sku.creator_id
          expect(body['type']).to eq sku.type
          expect(body['seq']).to eq sku.seq
          expect(body['comments_count']).to eq sku.comments_count
          expect(body['comment_conf']).to eq sku.comment_conf
          expect(body['recommended_at']).to eq sku.recommended_at
          expect(body['is_recommended']).to eq sku.is_recommended
          expect(body['name']).to eq sku.name
          expect(body['state']).to eq sku.state
          expect(body['content']).to eq sku.content
          expect(body['cover_image']).to eq sku.cover_image
          expect(body['price']).to eq sku.price
          expect(body['origin_price']).to eq sku.origin_price
          expect(body['position']).to eq sku.position
          expect(body['total_number']).to eq sku.total_number
          expect(body['freeze_number']).to eq sku.freeze_number
          expect(body['sale_count']).to eq sku.sale_count
          expect(body['stars_count']).to eq sku.stars_count
          expect(body['score']).to eq sku.score
          expect(body['origin_score']).to eq sku.origin_score
          expect(body['origin_sale_count']).to eq sku.origin_sale_count
          expect(body['payload']).to eq sku.payload
          expect(body['rate']).to eq sku.rate
        }
      end
    end

    patch(summary: 'update sku') do
      produces 'application/json'
      consumes 'application/json'
      parameter :sku, in: :body, schema: sku_ref
      response(201, description: 'successful') do
        let(:id) { @travel_skus.first.id }
        let(:sku) do
          { sku: sku_value }
        end
      end
    end

    delete(summary: 'delete sku') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_skus.first.id }
        it {
          expect(Travel::Sku.count).to eq(@travel_sku_count-1)
        }
      end
    end
  end
end
