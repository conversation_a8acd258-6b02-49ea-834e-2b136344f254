require 'swagger_helper'

RSpec.describe '/travel/manage/demand_grps', type: :request, capture_examples: true, tags: [" travel manage"] do
  demand_grp_ref = {
    type: :object, properties: {
      demand_grp: {
        type: :object, properties: {
          activity_id: { type: :integer, description: '' },
          demand_grp_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          mode: { type: :string, description: '' },
        }
      }
    }
  }
  demand_grp_value = FactoryBot.attributes_for(:travel_demand_grp)

  before :each do
    @user.add_role(:travel_admin)
    @travel_demand_grp_count = 5
    @activity = FactoryBot.create(:travel_activity, app: @app)
    @travel_demand_grps = FactoryBot.create_list(:travel_demand_grp, @travel_demand_grp_count, activity: @activity, app: @app)
  end

  # path '/travel/manage/activities/{activity_id}/demand_grps/{id}/import_product' do
  #   parameter 'activity_id', in: :path, type: :string
  #   parameter 'id', in: :path, type: :string

  #   post(summary: 'import_product demand_grp') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :demand_grp, in: :body, schema: demand_grp_ref
  #     response(201, description: 'successful') do
  #       let(:activity_id) { @activity.id }
  #       let(:id) { @travel_demand_grps.first.id }
  #       let(:demand_grp) do
  #         { demand_grp: demand_grp_value }
  #       end
  #     end
  #   end
  # end

  path '/travel/manage/activities/{activity_id}/demand_grps' do
    parameter 'activity_id', in: :path, type: :string

    get(summary: 'list demand_grps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_grp_count
        }
      end
    end

    post(summary: 'create demand_grp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_grp, in: :body, schema: demand_grp_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:demand_grp) do
          { demand_grp: demand_grp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['activity_id']).to eq @activity.id
          expect(body['demand_grp_temp_id']).to eq demand_grp_value[:demand_grp_temp_id]
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq demand_grp_value[:model_flag]
          expect(body['model_payload']).to eq demand_grp_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_grp_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_grp_value[:model_detail]
          expect(body['name']).to eq demand_grp_value[:name]
          expect(body['mode']).to eq demand_grp_value[:mode]
        }
      end
    end
  end

  path '/travel/manage/activities/{activity_id}/demand_grps/{id}' do
    parameter 'activity_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_grp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_demand_grps.first.id }
        it {
          body = JSON.parse(response.body)
          demand_grp = @travel_demand_grps.first
          expect(body['activity_id']).to eq demand_grp.activity_id
          expect(body['demand_grp_temp_id']).to eq demand_grp.demand_grp_temp_id
          expect(body['app_id']).to eq demand_grp.app_id
          expect(body['model_flag']).to eq demand_grp.model_flag
          expect(body['model_payload']).to eq demand_grp.model_payload
          expect(body['model_payload_summary']).to eq demand_grp.model_payload_summary
          expect(body['model_detail']).to eq demand_grp.model_detail
          expect(body['name']).to eq demand_grp.name
        }
      end
    end

    patch(summary: 'update demand_grp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_grp, in: :body, schema: demand_grp_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_demand_grps.first.id }
        let(:demand_grp) do
          { demand_grp: demand_grp_value }
        end
      end
    end

    delete(summary: 'delete demand_grp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_demand_grps.first.id }
        it {
          expect(@activity.demand_grps.count).to eq(@travel_demand_grp_count-1)
        }
      end
    end
  end
end
