require 'swagger_helper'

RSpec.describe 'travel/manage/payments', type: :request, capture_examples: true, tags: ["travel manage"] do
  payment_ref = {
    type: :object, properties: {
      payment: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          state: { type: :string, description: '' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          supplier_id: { type: :integer, description: '' },
        }
      }
    }
  }
  payment_value = FactoryBot.attributes_for(:travel_payment)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/payments' do

    get(summary: 'list payments') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_payment_count
        }
      end
    end

    post(summary: 'create payment') do
      produces 'application/json'
      consumes 'application/json'
      parameter :payment, in: :body, schema: payment_ref
      response(201, description: 'successful') do
        let(:payment) do
          { payment: payment_value.merge(
            supplier_id: @travel_supplier.id,
            offer_ids: @travel_offers.pluck(:id),
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_flag']).to eq payment_value[:model_flag]
          expect(body['model_payload']).to eq payment_value[:model_payload]
          expect(body['model_payload_summary']).to eq payment_value[:model_payload_summary]
          expect(body['model_detail']).to eq payment_value[:model_detail]
          expect(body['state']).to eq 'pending'
          expect(body['amount']).to eq payment_value[:amount]
          expect(body['amount_unit']).to eq 'CNY'
          expect(body['amount_ratio']).to eq payment_value[:amount_ratio].to_s
          expect(body['amount_rmb']).to eq payment_value[:amount_rmb]
          expect(body['app_id']).to eq @app.id
          expect(body['supplier_id']).to eq @travel_supplier.id
          expect(body['creator_id']).to eq @user.id
          expect(body['offer_ids']).to eq @travel_offers.pluck(:id)
        }
      end
    end
  end

  path '/travel/manage/payments/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show payment') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_payments.first.id }
        it {
          body = JSON.parse(response.body)
          payment = @travel_payments.first
          expect(body['model_flag']).to eq payment.model_flag
          expect(body['model_payload']).to eq payment.model_payload
          expect(body['model_payload_summary']).to eq payment.model_payload_summary
          expect(body['model_detail']).to eq payment.model_detail
          expect(body['state']).to eq payment.state
          expect(body['amount']).to eq payment.amount
          expect(body['amount_unit']).to eq payment.amount_unit
          expect(body['amount_ratio']).to eq payment.amount_ratio.to_s
          expect(body['amount_rmb']).to eq payment.amount_rmb
          expect(body['app_id']).to eq payment.app_id
          expect(body['supplier_id']).to eq payment.supplier_id
          expect(body['creator_id']).to eq payment.creator_id
        }
      end
    end

    patch(summary: 'update payment') do
      produces 'application/json'
      consumes 'application/json'
      parameter :payment, in: :body, schema: payment_ref
      response(201, description: 'successful') do
        let(:id) { @travel_payments.first.id }
        let(:payment) do
          { payment: payment_value }
        end
      end
    end

    delete(summary: 'delete payment') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_payments.first.id }
        it {
          expect(Travel::Payment.count).to eq(@travel_payment_count-1)
        }
      end
    end
  end
end
