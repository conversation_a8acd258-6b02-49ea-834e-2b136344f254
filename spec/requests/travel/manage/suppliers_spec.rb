require 'swagger_helper'

RSpec.describe 'travel/manage/suppliers', type: :request, capture_examples: true, tags: ["travel manage"] do
  supplier_ref = {
    type: :object, properties: {
      supplier: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          org_identity_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          payload: { type: :jsonb, description: 'payload payload存储的字段' },
          payload_summary: { type: :jsonb, description: 'payload summary存储的字段' },
          parent_id: { type: :integer, description: 'closure tree parent_id' },
          code: { type: :string, description: '组织标识' },
          name: { type: :string, description: '组织名称' },
          short_name: { type: :string, description: '组织简称' },
          type: { type: :string, description: 'STI类型，可以是集团，或者在某些时候可能是学校这样的类型' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  supplier_value = FactoryBot.attributes_for(:travel_supplier)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/suppliers' do

    get(summary: 'list suppliers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_supplier_count
        }
      end
    end

    post(summary: 'create supplier') do
      produces 'application/json'
      consumes 'application/json'
      parameter :supplier, in: :body, schema: supplier_ref
      response(201, description: 'successful') do
        let(:supplier) do
          { supplier: supplier_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['org_identity_id']).to eq supplier_value[:org_identity_id]
          expect(body['model_flag']).to eq supplier_value[:model_flag]
          expect(body['model_payload']).to eq supplier_value[:model_payload]
          expect(body['model_payload_summary']).to eq supplier_value[:model_payload_summary]
          expect(body['model_detail']).to eq supplier_value[:model_detail]
          expect(body['payload']).to eq supplier_value[:payload]
          expect(body['payload_summary']).to eq supplier_value[:payload_summary]
          expect(body['parent_id']).to eq supplier_value[:parent_id]
          expect(body['code']).to eq supplier_value[:code]
          expect(body['name']).to eq supplier_value[:name]
          expect(body['short_name']).to eq supplier_value[:short_name]
          expect(body['type']).to eq 'Travel::Supplier'
          expect(body['position']).not_to be_nil
        }
      end
    end
  end

  path '/travel/manage/suppliers/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show supplier') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_suppliers.first.id }
        it {
          body = JSON.parse(response.body)
          supplier = @travel_suppliers.first
          expect(body['app_id']).to eq supplier.app_id
          expect(body['org_identity_id']).to eq supplier.org_identity_id
          expect(body['model_flag']).to eq supplier.model_flag
          expect(body['model_payload']).to eq supplier.model_payload
          expect(body['model_payload_summary']).to eq supplier.model_payload_summary
          expect(body['model_detail']).to eq supplier.model_detail
          expect(body['payload']).to eq supplier.payload
          expect(body['payload_summary']).to eq supplier.payload_summary
          expect(body['parent_id']).to eq supplier.parent_id
          expect(body['code']).to eq supplier.code
          expect(body['name']).to eq supplier.name
          expect(body['short_name']).to eq supplier.short_name
          expect(body['type']).to eq supplier.type
          expect(body['position']).to eq supplier.position
        }
      end
    end

    patch(summary: 'update supplier') do
      produces 'application/json'
      consumes 'application/json'
      parameter :supplier, in: :body, schema: supplier_ref
      response(201, description: 'successful') do
        let(:id) { @travel_suppliers.first.id }
        let(:supplier) do
          { supplier: supplier_value }
        end
      end
    end

    delete(summary: 'delete supplier') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_suppliers.first.id }
        it {
          expect(Travel::Supplier.count).to eq(@travel_supplier_count-1)
        }
      end
    end
  end
end
