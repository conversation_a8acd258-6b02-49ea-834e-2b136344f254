require 'swagger_helper'

RSpec.describe 'travel/manage/demand_presets', type: :request, capture_examples: true, tags: ["travel manage"] do
  demand_preset_ref = {
    type: :object, properties: {
      demand_preset: {
        type: :object, properties: {
          demand_grp_preset_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          poi_region_id: { type: :integer, description: '' },
          demand_define_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          mode: { type: :string, description: '分类' },
          count: { type: :integer, description: '数量' },
          duration: { type: :integer, description: '天数' },
          start_offset: { type: :integer, description: '开始日期偏移量' },
          end_offset: { type: :integer, description: '结束日期偏移量' },
          wait_for_move_to_start_offset: { type: :integer, description: '记录移动天数时的暂存字段' },
          payload: { type: :jsonb, description: 'payload payload存储的字段' },
          payload_summary: { type: :jsonb, description: 'payload summary存储的字段' },
          amount: { type: :decimal, description: '报价金额' },
          amount_unit: { type: :string, description: '报价金额单位' },
          amount_ratio: { type: :decimal, description: '报价金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '报价金额兑换为人民币价格' },
          offer_amount: { type: :decimal, description: '成本金额' },
          offer_amount_unit: { type: :string, description: '成本金额单位' },
          offer_amount_ratio: { type: :decimal, description: '成本金额对人民币汇率' },
          offer_amount_rmb: { type: :decimal, description: '成本金额兑换为人民币价格' },
          offer_total_amount: { type: :decimal, description: '成本金额 * 数量' },
          offer_total_amount_unit: { type: :string, description: '成本金额 * 数量单位' },
          offer_total_amount_ratio: { type: :decimal, description: '成本金额 * 数量对人民币汇率' },
          offer_total_amount_rmb: { type: :decimal, description: '成本金额 * 数量兑换为人民币价格' },
        }
      }
    }
  }
  demand_preset_value = FactoryBot.attributes_for(:travel_demand_preset)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/demand_presets' do

    get(summary: 'list demand_presets') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_preset_count
        }
      end
    end

    post(summary: 'create demand_preset') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_preset, in: :body, schema: demand_preset_ref
      response(201, description: 'successful') do
        let(:demand_preset) do
          { demand_preset: demand_preset_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['demand_grp_preset_id']).to eq demand_preset_value[:demand_grp_preset_id]
          expect(body['app_id']).to eq @app.id
          expect(body['poi_region_id']).to eq demand_preset_value[:poi_region_id]
          expect(body['demand_define_id']).to eq demand_preset_value[:demand_define_id]
          expect(body['model_flag']).to eq demand_preset_value[:model_flag]
          expect(body['model_payload']).to eq demand_preset_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_preset_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_preset_value[:model_detail]
          expect(body['name']).to eq demand_preset_value[:name]
          expect(body['desc']).to eq demand_preset_value[:desc]
          expect(body['mode']).to eq demand_preset_value[:mode]
          expect(body['count']).to eq demand_preset_value[:count]
          expect(body['duration']).to eq demand_preset_value[:duration]
          expect(body['start_offset']).to eq demand_preset_value[:start_offset]
          expect(body['end_offset']).not_to be_nil
          expect(body['wait_for_move_to_start_offset']).to eq demand_preset_value[:wait_for_move_to_start_offset]
          expect(body['payload']).to eq demand_preset_value[:payload]
          expect(body['payload_summary']).to eq demand_preset_value[:payload_summary]
          # expect(body['amount']).to eq demand_preset_value[:amount]
          # expect(body['amount_unit']).to eq demand_preset_value[:amount_unit]
          # expect(body['amount_ratio']).not_to be_nil
          # expect(body['amount_rmb']).to eq demand_preset_value[:amount_rmb]
          # expect(body['offer_amount']).to eq demand_preset_value[:offer_amount]
          # expect(body['offer_amount_unit']).to eq demand_preset_value[:offer_amount_unit]
          # expect(body['offer_amount_ratio']).to eq demand_preset_value[:offer_amount_ratio]
          # expect(body['offer_amount_rmb']).to eq demand_preset_value[:offer_amount_rmb]
          # expect(body['offer_total_amount']).to eq demand_preset_value[:offer_total_amount]
          # expect(body['offer_total_amount_unit']).to eq demand_preset_value[:offer_total_amount_unit]
          # expect(body['offer_total_amount_ratio']).to eq demand_preset_value[:offer_total_amount_ratio]
          # expect(body['offer_total_amount_rmb']).to eq demand_preset_value[:offer_total_amount_rmb]
        }
      end
    end
  end

  path '/travel/manage/demand_presets/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_preset') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_demand_presets.first.id }
        it {
          body = JSON.parse(response.body)
          demand_preset = @travel_demand_presets.first
          expect(body['demand_grp_preset_id']).to eq demand_preset.demand_grp_preset_id
          expect(body['app_id']).to eq demand_preset.app_id
          expect(body['poi_region_id']).to eq demand_preset.poi_region_id
          expect(body['demand_define_id']).to eq demand_preset.demand_define_id
          expect(body['model_flag']).to eq demand_preset.model_flag
          expect(body['model_payload']).to eq demand_preset.model_payload
          expect(body['model_payload_summary']).to eq demand_preset.model_payload_summary
          expect(body['model_detail']).to eq demand_preset.model_detail
          expect(body['name']).to eq demand_preset.name
          expect(body['desc']).to eq demand_preset.desc
          expect(body['mode']).to eq demand_preset.mode
          expect(body['count']).to eq demand_preset.count
          expect(body['duration']).to eq demand_preset.duration
          expect(body['start_offset']).to eq demand_preset.start_offset
          expect(body['end_offset']).to eq demand_preset.end_offset
          expect(body['wait_for_move_to_start_offset']).to eq demand_preset.wait_for_move_to_start_offset
          expect(body['payload']).to eq demand_preset.payload
          expect(body['payload_summary']).to eq demand_preset.payload_summary
          # expect(body['amount']).to eq demand_preset.amount
          # expect(body['amount_unit']).to eq demand_preset.amount_unit
          # expect(body['amount_ratio']).not_to be_nil
          # expect(body['amount_rmb']).to eq demand_preset.amount_rmb
          # expect(body['offer_amount']).to eq demand_preset.offer_amount
          # expect(body['offer_amount_unit']).to eq demand_preset.offer_amount_unit
          # expect(body['offer_amount_ratio']).not_to be_nil
          # expect(body['offer_amount_rmb']).to eq demand_preset.offer_amount_rmb
          # expect(body['offer_total_amount']).to eq demand_preset.offer_total_amount
          # expect(body['offer_total_amount_unit']).to eq demand_preset.offer_total_amount_unit
          # expect(body['offer_total_amount_ratio']).to eq demand_preset.offer_total_amount_ratio
          # expect(body['offer_total_amount_rmb']).to eq demand_preset.offer_total_amount_rmb
        }
      end
    end

    patch(summary: 'update demand_preset') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_preset, in: :body, schema: demand_preset_ref
      response(201, description: 'successful') do
        let(:id) { @travel_demand_presets.first.id }
        let(:demand_preset) do
          { demand_preset: demand_preset_value }
        end
      end
    end

    delete(summary: 'delete demand_preset') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_demand_presets.first.id }
        it {
          expect(Travel::DemandPreset.count).to eq(@travel_demand_preset_count-1)
        }
      end
    end
  end
end
