require 'swagger_helper'

RSpec.describe 'travel/manage/poi_countries', type: :request, capture_examples: true, tags: ["travel manage"] do
  country_ref = {
    type: :object, properties: {
      country: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          continent: { type: :string, description: '大洲' },
          detail: { type: :jsonb, description: '详情' },
        }
      }
    }
  }
  country_value = FactoryBot.attributes_for(:travel_poi_country)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/poi_countries' do

    get(summary: 'list poi_countries') do
      produces 'application/json'
      consumes 'application/json'
      parameter :mode, in: :query, type: :string
      response(200, description: 'successful') do
        let(:mode) { 'public' }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_country_count
        }
      end
    end

    post(summary: 'create poi_country') do
      produces 'application/json'
      consumes 'application/json'
      parameter :country, in: :body, schema: country_ref
      response(201, description: 'successful') do
        let(:country) do
          { country: country_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_flag']).to eq country_value[:model_flag]
          expect(body['model_payload']).to eq country_value[:model_payload]
          expect(body['model_payload_summary']).to eq country_value[:model_payload_summary]
          expect(body['model_detail']).to eq country_value[:model_detail]
          expect(body['name']).not_to be_nil
          expect(body['continent']).to eq country_value[:continent]
          expect(body['detail']).to eq country_value[:detail]
        }
      end
    end
  end

  path '/travel/manage/poi_countries/{id}' do
    parameter 'id', in: :path, type: :string
    parameter :mode, in: :query, type: :string

    get(summary: 'show poi_country') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_poi_countries.first.id }
        let(:mode) { 'public' }
        it {
          body = JSON.parse(response.body)
          country = @travel_poi_countries.first
          expect(body['model_flag']).to eq country.model_flag
          expect(body['model_payload']).to eq country.model_payload
          expect(body['model_payload_summary']).to eq country.model_payload_summary
          expect(body['model_detail']).to eq country.model_detail
          expect(body['name']).to eq country.name
          expect(body['continent']).to eq country.continent
          expect(body['detail']).to eq country.detail
        }
      end
    end

    patch(summary: 'update poi_country') do
      produces 'application/json'
      consumes 'application/json'
      parameter :country, in: :body, schema: country_ref
      response(201, description: 'successful') do
        let(:id) { @travel_poi_countries.first.id }
        let(:mode) { 'public' }
        let(:country) do
          { country: country_value }
        end
      end
    end

    delete(summary: 'delete poi_country') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:mode) { 'public' }
        let(:id) { @travel_poi_countries.first.id }
        it {
          expect(Travel::Poi::Country.count).to eq(@travel_poi_country_count-1)
        }
      end
    end
  end
end
