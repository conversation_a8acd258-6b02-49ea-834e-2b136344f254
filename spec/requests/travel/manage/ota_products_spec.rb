require 'swagger_helper'

RSpec.describe 'travel/manage/ota_products', type: :request, capture_examples: true, tags: ["travel manage"] do
  ota_product_ref = {
    type: :object, properties: {
      ota_product: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          channel_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          seq: { type: :string, description: '编号' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          ota_no: { type: :string, description: 'ota编号' },
          list_time: { type: :datetime, description: '上架时间' },
          delist_time: { type: :datetime, description: '下架时间' },
          num: { type: :integer, description: '数量' },
          sold_quantity: { type: :integer, description: '销售数量' },
          price: { type: :decimal, description: '价格' },
          payload: { type: :jsonb, description: '额外数据' },
        }
      }
    }
  }

  ota_product_value = FactoryBot.attributes_for(:travel_ota_product)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/ota_products' do

    get(summary: 'list ota_products') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_ota_product_count
        }
      end
    end

    post(summary: 'create ota_product') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ota_product, in: :body, schema: ota_product_ref
      response(201, description: 'successful') do
        let(:ota_product) do
          { ota_product: ota_product_value.merge(channel_id: @travel_channel.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['channel_id']).to eq @travel_channel.id
          expect(body['model_flag']).to eq ota_product_value[:model_flag]
          expect(body['model_payload']).to eq ota_product_value[:model_payload]
          expect(body['model_payload_summary']).to eq ota_product_value[:model_payload_summary]
          expect(body['model_detail']).to eq ota_product_value[:model_detail]
          expect(body['type']).to eq ota_product_value[:type]
          expect(body['seq']).not_to be_nil
          expect(body['name']).to eq ota_product_value[:name]
          expect(body['state']).to eq ota_product_value[:state]
          expect(body['ota_no']).to eq ota_product_value[:ota_no]
          expect(body['list_time']).to eq ota_product_value[:list_time]
          expect(body['delist_time']).to eq ota_product_value[:delist_time]
          expect(body['num']).to eq ota_product_value[:num]
          expect(body['sold_quantity']).to eq ota_product_value[:sold_quantity]
          expect(body['price']).to eq ota_product_value[:price]
          expect(body['payload']).to eq ota_product_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/ota_products/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show ota_product') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_ota_products.first.id }
        it {
          body = JSON.parse(response.body)
          ota_product = @travel_ota_products.first
          expect(body['app_id']).to eq ota_product.app_id
          expect(body['channel_id']).to eq ota_product.channel_id
          expect(body['model_flag']).to eq ota_product.model_flag
          expect(body['model_payload']).to eq ota_product.model_payload
          expect(body['model_payload_summary']).to eq ota_product.model_payload_summary
          expect(body['model_detail']).to eq ota_product.model_detail
          expect(body['type']).to eq ota_product.type
          expect(body['seq']).to eq ota_product.seq
          expect(body['name']).to eq ota_product.name
          expect(body['state']).to eq ota_product.state
          expect(body['ota_no']).to eq ota_product.ota_no
          expect(body['list_time']).to eq ota_product.list_time
          expect(body['delist_time']).to eq ota_product.delist_time
          expect(body['num']).to eq ota_product.num
          expect(body['sold_quantity']).to eq ota_product.sold_quantity
          expect(body['price']).to eq ota_product.price
          expect(body['payload']).to eq ota_product.payload
        }
      end
    end

    patch(summary: 'update ota_product') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ota_product, in: :body, schema: ota_product_ref
      response(201, description: 'successful') do
        let(:id) { @travel_ota_products.first.id }
        let(:ota_product) do
          { ota_product: ota_product_value }
        end
      end
    end

    delete(summary: 'delete ota_product') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_ota_products.first.id }
        it {
          expect(Travel::OtaProduct.count).to eq(@travel_ota_product_count-1)
        }
      end
    end
  end
end
