require 'swagger_helper'

RSpec.describe '/travel/manage/demands', type: :request, capture_examples: true, tags: [" travel manage"] do
  demand_ref = {
    type: :object, properties: {
      demand: {
        type: :object, properties: {
          demand_grp_id: { type: :integer, description: '' },
          activity_id: { type: :integer, description: '' },
          demand_temp_id: { type: :integer, description: '' },
          offer_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          poi_region_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          count: { type: :integer, description: '数量' },
          duration: { type: :integer, description: '天数' },
          start_offset: { type: :integer, description: '开始日期偏移量' },
          end_offset: { type: :integer, description: '结束日期偏移量' },
          # around_amount: { type: :decimal, description: '预估金额' },
          # around_amount_unit: { type: :string, description: '预估金额单位' },
          # around_amount_ratio: { type: :decimal, description: '预估金额对人民币汇率' },
          # around_amount_rmb: { type: :decimal, description: '预估金额兑换为人民币价格' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          start_date: { type: :date, description: '开始日期' },
          end_date: { type: :date, description: '结束日期' },
        }
      }
    }
  }
  demand_value = FactoryBot.attributes_for(:travel_demand)

  before :each do
    @user.add_role :travel_admin
    @activity = create(:travel_activity, app: @app, duration: 10)
    @demand_grp = create(:travel_demand_grp, app: @app, activity: @activity)
    @travel_demand_count = 5
    @travel_demands = FactoryBot.create_list(
      :travel_demand,
      @travel_demand_count,
      demand_grp: @demand_grp,
      activity: @activity,
      app: @app,
    )
  end

  path '/travel/manage/activities/{activity_id}/demands' do
    parameter 'activity_id', in: :path, type: :string

    get(summary: 'list demands') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_count
        }
      end
    end

    post(summary: 'create demand') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand, in: :body, schema: demand_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:demand) do
          { demand: demand_value.merge(demand_grp_id: @demand_grp.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['demand_grp_id']).to eq @demand_grp.id
          expect(body['activity_id']).to eq @demand_grp.activity_id
          expect(body['demand_temp_id']).to eq demand_value[:demand_temp_id]
          expect(body['offer_id']).to eq demand_value[:offer_id]
          expect(body['app_id']).to eq @app.id
          expect(body['poi_region_id']).to eq demand_value[:poi_region_id]
          expect(body['model_flag']).to eq demand_value[:model_flag]
          expect(body['model_payload']).to eq demand_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_value[:model_detail]
          expect(body['name']).to eq demand_value[:name]
          expect(body['desc']).to eq demand_value[:desc]
          expect(body['count']).to eq demand_value[:count]
          expect(body['duration']).to eq demand_value[:duration]
          expect(body['start_offset']).to eq 0
          # expect(body['end_offset']).to eq 0
          # record = Travel::Demand.find(body['id'])
          # expect(record.end_offset).to eq(0)
          # # expect(body['around_amount']).to eq demand_value[:around_amount]
          # # expect(body['around_amount_unit']).to eq demand_value[:around_amount_unit]
          # # expect(body['around_amount_ratio']).to eq demand_value[:around_amount_ratio]
          # # expect(body['around_amount_rmb']).to eq demand_value[:around_amount_rmb]
          expect(body['amount']).to eq demand_value[:amount]
          expect(body['amount_unit']).to eq demand_value[:amount_unit]
          expect(body['amount_ratio'].to_f).to eq demand_value[:amount_ratio].to_f
          expect(body['amount_rmb']).to eq demand_value[:amount_rmb]
          expect(body['start_date']).not_to be_nil
          expect(body['end_date']).not_to be_nil
        }
      end
    end
  end

  path '/travel/manage/activities/{activity_id}/demands/{id}' do
    parameter 'activity_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_demands.first.id }
        it {
          body = JSON.parse(response.body)
          demand = @travel_demands.first
          expect(body['demand_grp_id']).to eq demand.demand_grp_id
          expect(body['activity_id']).to eq demand.activity_id
          expect(body['demand_temp_id']).to eq demand.demand_temp_id
          expect(body['offer_id']).to eq demand.offer_id
          expect(body['app_id']).to eq demand.app_id
          expect(body['poi_region_id']).to eq demand.poi_region_id
          expect(body['model_flag']).to eq demand.model_flag
          expect(body['model_payload']).to eq demand.model_payload
          expect(body['model_payload_summary']).to eq demand.model_payload_summary
          expect(body['model_detail']).to eq demand.model_detail
          expect(body['name']).to eq demand.name
          expect(body['desc']).to eq demand.desc
          expect(body['count']).to eq demand.count
          expect(body['duration']).to eq demand.duration
          expect(body['start_offset']).to eq demand.start_offset
          expect(body['end_offset']).to eq demand.end_offset
          # # expect(body['around_amount']).to eq demand.around_amount
          # # expect(body['around_amount_unit']).to eq demand.around_amount_unit
          # # expect(body['around_amount_ratio']).to eq demand.around_amount_ratio
          # # expect(body['around_amount_rmb']).to eq demand.around_amount_rmb
          expect(body['amount']).to eq demand.amount
          expect(body['amount_unit']).to eq demand.amount_unit
          expect(body['amount_ratio'].to_f).to eq demand.amount_ratio.to_f
          expect(body['amount_rmb']).to eq demand.amount_rmb
          expect(body['start_date']).to eq demand.start_date
          expect(body['end_date']).to eq demand.end_date
        }
      end
    end

    patch(summary: 'update demand') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand, in: :body, schema: demand_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_demands.first.id }
        let(:demand) do
          { demand: demand_value }
        end
      end
    end

    delete(summary: 'delete demand') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_demands.first.id }
        it {
          expect(@activity.demands.count).to eq(@travel_demand_count-1)
        }
      end
    end
  end
end
