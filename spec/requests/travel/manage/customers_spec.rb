require 'swagger_helper'

RSpec.describe 'travel/manage/customers', type: :request, capture_examples: true, tags: ["travel manage"] do
  user_ref = {
    type: :object, properties: {
      user: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          tanent_id: { type: :integer, description: '' },
          ref_user_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          account: { type: :string, description: '账号，关联登录' },
          name: { type: :string, description: '用户姓名' },
          nickname: { type: :string, description: '用户昵称' },
          pinyin: { type: :string, description: '用户名拼音' },
          mobile: { type: :string, description: '用户手机号' },
          email: { type: :string, description: '用户邮箱' },
          gender: { type: :string, description: '性别' },
          avatar: { type: :jsonb, description: '用户头像' },
          identity_id: { type: :string, description: '证件号码，需要时候可以作为唯一标识' },
          last_visit_at: { type: :datetime, description: '最后访问时间' },
          blocked_at: { type: :datetime, description: '' },
          is_blocked: { type: :boolean, description: '' },
        }
      }
    }
  }
  user_value = FactoryBot.attributes_for(:user)

  before :each do
    @user.add_role :travel_admin
    @customer = @travel_customer
  end

  path '/travel/manage/customers' do

    get(summary: 'list customers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_customer_count
        }
      end
    end

    post(summary: 'create customer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :customer, in: :body, schema: user_ref
      response(201, description: 'successful') do
        let(:customer) do
          { customer: user_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['tanent_id']).to eq user_value[:tanent_id]
          expect(body['ref_user_id']).to eq user_value[:ref_user_id]
          expect(body['model_flag']).not_to be_nil
          expect(body['model_payload']).to eq user_value[:model_payload]
          expect(body['model_payload_summary']).to eq user_value[:model_payload_summary]
          expect(body['model_detail']).to eq user_value[:model_detail]
          expect(body['effective_at']).to eq user_value[:effective_at]
          expect(body['invalid_at']).to eq user_value[:invalid_at]
          expect(body['account']).to eq user_value[:account]
          expect(body['name']).to eq user_value[:name]
          expect(body['nickname']).to eq user_value[:nickname]
          expect(body['pinyin']).not_to be_nil
          expect(body['mobile']).to eq user_value[:mobile]
          expect(body['email']).to eq user_value[:email]
          expect(body['gender']).to eq user_value[:gender]
          expect(body['avatar']).to eq user_value[:avatar]
          expect(body['identity_id']).to eq user_value[:identity_id]
          expect(body['last_visit_at']).to eq user_value[:last_visit_at]
          expect(body['blocked_at']).to eq user_value[:blocked_at]
          expect(body['is_blocked']).not_to be_nil
        }
      end
    end
  end

  path '/travel/manage/customers/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show customer') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @customer.id }
        it {
          body = JSON.parse(response.body)
          user = @customer
          expect(body['app_id']).to eq user.app_id
          expect(body['tanent_id']).to eq user.tanent_id
          expect(body['ref_user_id']).to eq user.ref_user_id
          expect(body['model_flag']).to eq user.model_flag
          expect(body['model_payload']).to eq user.model_payload
          expect(body['model_payload_summary']).to eq user.model_payload_summary
          expect(body['model_detail']).to eq user.model_detail
          expect(body['effective_at']).to eq user.effective_at
          expect(body['invalid_at']).to eq user.invalid_at
          expect(body['account']).to eq user.account
          expect(body['name']).to eq user.name
          expect(body['nickname']).to eq user.nickname
          expect(body['pinyin']).to eq user.pinyin
          expect(body['mobile']).to eq user.mobile
          expect(body['email']).to eq user.email
          expect(body['gender']).to eq user.gender
          expect(body['avatar']).to eq user.avatar
          expect(body['identity_id']).to eq user.identity_id
          expect(body['last_visit_at']).to eq user.last_visit_at
          expect(body['blocked_at']).to eq user.blocked_at
          expect(body['is_blocked']).to eq user.is_blocked
        }
      end
    end

    patch(summary: 'update customer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :customer, in: :body, schema: user_ref
      response(201, description: 'successful') do
        let(:id) { @customer.id }
        let(:customer) do
          { customer: user_value }
        end
      end
    end

    delete(summary: 'delete customer') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @customer.id }
        it {
          expect(User.count).to eq(@travel_customer_count-1)
        }
      end
    end
  end
end
