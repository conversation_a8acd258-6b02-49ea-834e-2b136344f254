require 'swagger_helper'

RSpec.describe '/travel/manage/activities', type: :request, capture_examples: true, tags: [" travel manage"] do
  activity_ref = {
    type: :object, properties: {
      activity: {
        type: :object, properties: {
          activity_temp_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          title: { type: :string, description: '标题' },
          desc: { type: :text, description: '描述' },
          duration: { type: :integer, description: '天数' },
          state: { type: :string, description: '状态' },
          start_at: { type: :date, description: '开始日期' },
          end_at: { type: :date, description: '结束日期' },
        }
      }
    }
  }
  activity_value = FactoryBot.attributes_for(:travel_activity)

  before :each do
    @user.add_role(:travel_admin)
    @travel_activities = FactoryBot.create_list(:travel_activity, 5, app: @app)
    @travel_activity_count = @app.travel_activities.count
    @country = create(:travel_poi_country)
    @app_country = create(:travel_poi_app_country, app: @app, country: @country)
    @city = create(:travel_poi_region, type: 'Travel::Poi::City', country: @country)
  end

  path '/travel/manage/activities' do

    get(summary: 'list activities') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_activity_count
        }
      end
    end

    post(summary: 'create activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(201, description: 'successful') do
        let(:activity) do
          { activity: activity_value.merge(city_ids: [@city.id]) }
        end
        it {
          activity_value = activity_value.with_indifferent_access
          body = JSON.parse(response.body)
          expect(body['app_countries'].count).to eq 1
          expect(body['app_countries'].first['id']).to eq(@app_country.id)
          expect(body['creator_id']).to eq @user.id
          expect(body['activity_temp_id']).to eq activity_value[:activity_temp_id]
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq activity_value[:model_flag]
          expect(body['model_payload']).to eq activity_value[:model_payload]
          expect(body['model_payload_summary']).to eq activity_value[:model_payload_summary]
          expect(body['model_detail']).to eq activity_value[:model_detail]
          expect(body['title']).to eq activity_value[:title]
          expect(body['desc']).to eq activity_value[:desc]
          expect(body['duration']).to eq activity_value[:duration]
          expect(body['state']).to eq 'pending'
          expect(body['start_at']).to eq activity_value[:start_at].strftime('%Y-%m-%d')
          expect(body['end_at']).to eq (activity_value[:start_at] + activity_value[:duration].days - 1.day).strftime('%Y-%m-%d')
        }
      end
    end
  end

  path '/travel/manage/activities/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_activities.first.id }
        it {
          body = JSON.parse(response.body)
          activity = @travel_activities.first
          expect(body['creator_id']).to eq activity.creator_id
          expect(body['activity_temp_id']).to eq activity.activity_temp_id
          expect(body['app_id']).to eq activity.app_id
          expect(body['model_flag']).to eq activity.model_flag
          expect(body['model_payload']).to eq activity.model_payload
          expect(body['model_payload_summary']).to eq activity.model_payload_summary
          expect(body['model_detail']).to eq activity.model_detail
          expect(body['name']).to eq activity.name
          expect(body['desc']).to eq activity.desc
          expect(body['duration']).to eq activity.duration
          expect(body['state']).to eq activity.state
          expect(body['start_at']).to eq activity.start_at.strftime('%Y-%m-%d')
          expect(body['end_at']).to eq activity.end_at.strftime('%Y-%m-%d')
        }
      end
    end

    patch(summary: 'update activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(201, description: 'successful') do
        let(:id) { @travel_activities.first.id }
        let(:activity) do
          { activity: activity_value }
        end
      end
    end

    delete(summary: 'delete activity') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_activities.first.id }
        it {
          expect(Travel::Activity.count).to eq(@travel_activity_count-1)
        }
      end
    end
  end
end
