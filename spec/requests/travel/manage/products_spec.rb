require 'swagger_helper'

RSpec.describe 'travel/manage/products', type: :request, capture_examples: true, tags: ["travel manage"] do
  product_ref = {
    type: :object, properties: {
      product: {
        type: :object, properties: {
          product_dir_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          seq: { type: :string, description: '编号' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          num: { type: :float, description: '数量' },
          cover_image: { type: :jsonb, description: '图片' },
          content: { type: :jsonb, description: '内容' },
          price: { type: :float, description: '价格' },
        }
      }
    }
  }
  product_value = FactoryBot.attributes_for(:travel_product)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/products' do

    get(summary: 'list products') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_product_count
        }
      end
    end

    post(summary: 'create product') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product, in: :body, schema: product_ref
      response(201, description: 'successful') do
        let(:product) do
          { product: product_value.merge(product_dir_id: @travel_product_dir.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['product_dir_id']).to eq @travel_product_dir.id
          expect(body['model_flag']).to eq product_value[:model_flag]
          expect(body['model_payload']).to eq product_value[:model_payload]
          expect(body['model_payload_summary']).to eq product_value[:model_payload_summary]
          expect(body['model_detail']).to eq product_value[:model_detail]
          expect(body['seq']).not_to be_nil
          expect(body['name']).to eq product_value[:name]
          expect(body['state']).to eq product_value[:state]
          expect(body['app_id']).to eq @app.id
          expect(body['type']).to eq product_value[:type]
          expect(body['cover_image']).to eq product_value[:cover_image]
          expect(body['content']).to eq product_value[:content]
        }
      end
    end
  end

  path '/travel/manage/products/{id}/import_entity' do
    parameter 'id', in: :path, type: :string

    post(summary: 'product import_entity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product, in: :body, schema: product_ref

      before do
        @product = @travel_products.first
        expect(@product.demand_temps.count).to eq(0)

        @demand_temp = create(:travel_demand_temp, product: @product, start_offset: 1, duration: 1, app: @app)
        expect(@product.demand_temps.count).not_to eq(0)

        @activity = create(:travel_activity, app: @app)
        @demand_grp = create(:travel_demand_grp, app: @app, activity: @activity)

        @demand_grps_count = @activity.demand_grps.count
        @demand_count = @demand_grp.demands.count
      end

      response(201, description: 'into exists demand_grp successful') do
        let(:id) { @product.id }
        let(:product) do
          {
            product: {
              demand_grp_id: @demand_grp.id,
              import_extra: { start_position: 1 }
            }
          }
        end

        it {
          expect(@demand_grp.demands.count).to eq(@demand_count + @product.demand_temps.count)
          @new_demand = @demand_grp.demands.where(demand_temp_id: @demand_temp.id).first
          expect(@new_demand).not_to be_nil
          expect(@new_demand.start_offset).to eq(1)
          expect(@new_demand.start_position).to eq(2)
        }
      end

      response(201, description: 'into exists activity and create demand_grp successful') do
        let(:id) { @product.id }
        let(:product) do
          {
            product: {
              activity_id: @activity.id,
              import_extra: { start_position: 1 }
            }
          }
        end

        it {
          expect(@activity.demand_grps.count).to eq(@demand_grps_count + 1)
          @new_demand_grp = @activity.demand_grps.order(id: :desc).first
          expect(@new_demand_grp.demands.count).to eq(@product.demand_temps.count)
          @new_demand = @new_demand_grp.demands.where(demand_temp_id: @demand_temp.id).first
          expect(@new_demand).not_to be_nil
          expect(@new_demand.start_offset).to eq(1)
          expect(@new_demand.start_position).to eq(2)
        }
      end

      response(201, description: 'create new activity and create demand_grp successful') do
        before do
          @title = 'test activity 1'
        end
        let(:id) { @product.id }
        let(:product) do
          {
            product: {
              import_extra: { start_position: 1 },
              activity_extra: {
                title: @title,
                start_at: Date.today,
              }
            }
          }
        end

        it {
          # p response.body
          @new_activity = Travel::Activity.unscope(:order).order(id: :desc).first
          expect(@new_activity.title).to eq(@title)
          expect(@new_activity.demand_grps.count).to eq(1)
          @new_demand_grp = @new_activity.demand_grps.order(id: :desc).first
          expect(@new_demand_grp.demands.count).to eq(@product.demand_temps.count)
          @new_demand = @new_demand_grp.demands.where(demand_temp_id: @demand_temp.id).first
          expect(@new_demand).not_to be_nil
          expect(@new_demand.start_offset).to eq(1)
          expect(@new_demand.start_position).to eq(2)
        }
      end
    end
  end

  path '/travel/manage/products/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show product') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_products.first.id }
        it {
          body = JSON.parse(response.body)
          product = @travel_products.first
          expect(body['product_dir_id']).to eq product.product_dir_id
          expect(body['model_flag']).to eq product.model_flag
          expect(body['model_payload']).to eq product.model_payload
          expect(body['model_payload_summary']).to eq product.model_payload_summary
          expect(body['model_detail']).to eq product.model_detail
          expect(body['seq']).to eq product.seq
          expect(body['name']).to eq product.name
          expect(body['state']).to eq product.state
          expect(body['app_id']).to eq product.app_id
          expect(body['type']).to eq product.type
          expect(body['cover_image']).to eq product.cover_image
          expect(body['content']).to eq product.content
        }
      end
    end

    patch(summary: 'update product') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product, in: :body, schema: product_ref
      response(201, description: 'successful') do
        let(:id) { @travel_products.first.id }
        let(:product) do
          { product: product_value }
        end
      end
    end

    delete(summary: 'delete product') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_products.first.id }
        it {
          expect(Travel::Product.count).to eq(@travel_product_count-1)
        }
      end
    end
  end
end
