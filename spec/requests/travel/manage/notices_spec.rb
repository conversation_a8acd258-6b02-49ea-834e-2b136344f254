require 'swagger_helper'

RSpec.describe 'travel/manage/notices', type: :request, capture_examples: true, tags: ["travel manage"] do
  notice_ref = {
    type: :object, properties: {
      notice: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          content: { type: :text, description: '内容' },
          state: { type: :string, description: '状态' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  notice_value = FactoryBot.attributes_for(:travel_notice)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/notices' do

    get(summary: 'list notices') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_notice_count
        }
      end
    end

    post(summary: 'create notice') do
      produces 'application/json'
      consumes 'application/json'
      parameter :notice, in: :body, schema: notice_ref
      response(201, description: 'successful') do
        let(:notice) do
          { notice: notice_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['type']).to eq notice_value[:type]
          expect(body['name']).to eq notice_value[:name]
          expect(body['content']).to eq notice_value[:content]
          expect(body['state']).to eq notice_value[:state]
          expect(body['payload']).to eq notice_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/notices/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show notice') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_notices.first.id }
        it {
          body = JSON.parse(response.body)
          notice = @travel_notices.first
          expect(body['app_id']).to eq notice.app_id
          expect(body['user_id']).to eq notice.user_id
          expect(body['type']).to eq notice.type
          expect(body['name']).to eq notice.name
          expect(body['content']).to eq notice.content
          expect(body['state']).to eq notice.state
          expect(body['payload']).to eq notice.payload
        }
      end
    end

    patch(summary: 'update notice') do
      produces 'application/json'
      consumes 'application/json'
      parameter :notice, in: :body, schema: notice_ref
      response(201, description: 'successful') do
        let(:id) { @travel_notices.first.id }
        let(:notice) do
          { notice: notice_value }
        end
      end
    end

    delete(summary: 'delete notice') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_notices.first.id }
        it {
          expect(Travel::Notice.count).to eq(@travel_notice_count-1)
        }
      end
    end
  end
end
