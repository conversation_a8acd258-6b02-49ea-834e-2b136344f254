require 'swagger_helper'

RSpec.describe 'travel/manage/channels', type: :request, capture_examples: true, tags: ["travel manage"] do
  channel_ref = {
    type: :object, properties: {
      channel: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          config: { type: :jsonb, description: '配置' },
          display_config: { type: :jsonb, description: '展示配置' },
        }
      }
    }
  }
  channel_value = FactoryBot.attributes_for(:travel_channel)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/channels' do

    get(summary: 'list channels') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_channel_count
        }
      end
    end

    post(summary: 'create channel') do
      produces 'application/json'
      consumes 'application/json'
      parameter :channel, in: :body, schema: channel_ref
      response(201, description: 'successful') do
        let(:channel) do
          { channel: channel_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq channel_value[:model_flag]
          expect(body['model_payload']).to eq channel_value[:model_payload]
          expect(body['model_payload_summary']).to eq channel_value[:model_payload_summary]
          expect(body['model_detail']).to eq channel_value[:model_detail]
          expect(body['type']).to eq channel_value[:type]
          expect(body['name']).to eq channel_value[:name]
          expect(body['state']).to eq channel_value[:state]
          expect(body['config']).to eq channel_value[:config]
          expect(body['display_config']).to eq channel_value[:display_config]
        }
      end
    end
  end

  path '/travel/manage/channels/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show channel') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_channels.first.id }
        it {
          body = JSON.parse(response.body)
          channel = @travel_channels.first
          expect(body['app_id']).to eq channel.app_id
          expect(body['model_flag']).to eq channel.model_flag
          expect(body['model_payload']).to eq channel.model_payload
          expect(body['model_payload_summary']).to eq channel.model_payload_summary
          expect(body['model_detail']).to eq channel.model_detail
          expect(body['type']).to eq channel.type
          expect(body['name']).to eq channel.name
          expect(body['state']).to eq channel.state
          expect(body['config']).to eq channel.config
          expect(body['display_config']).to eq channel.display_config
        }
      end
    end

    patch(summary: 'update channel') do
      produces 'application/json'
      consumes 'application/json'
      parameter :channel, in: :body, schema: channel_ref
      response(201, description: 'successful') do
        let(:id) { @travel_channels.first.id }
        let(:channel) do
          { channel: channel_value }
        end
      end
    end

    delete(summary: 'delete channel') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_channels.first.id }
        it {
          expect(Travel::Channel.count).to eq(@travel_channel_count-1)
        }
      end
    end
  end
end
