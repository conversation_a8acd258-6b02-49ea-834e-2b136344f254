require 'swagger_helper'

RSpec.describe '/travel/manage/demand_grp_temps', type: :request, capture_examples: true, tags: [" travel manage"] do
  demand_grp_temp_ref = {
    type: :object, properties: {
      demand_grp_temp: {
        type: :object, properties: {
          activity_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
        }
      }
    }
  }
  demand_grp_temp_value = FactoryBot.attributes_for(:travel_demand_grp_temp)

  before :each do
    @user.add_role(:travel_admin)
    @activity_temp = create(:travel_activity_temp, app: @app)
    @travel_demand_grp_temp_count = 5
    @travel_demand_grp_temps = FactoryBot.create_list(:travel_demand_grp_temp, @travel_demand_grp_temp_count, activity_temp: @activity_temp, app: @app)
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/demand_grp_temps' do
    parameter 'activity_temp_id', in: :path, type: :string

    get(summary: 'list demand_grp_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_grp_temp_count
        }
      end
    end

    post(summary: 'create demand_grp_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_grp_temp, in: :body, schema: demand_grp_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:demand_grp_temp) do
          { demand_grp_temp: demand_grp_temp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['activity_temp_id']).to eq @activity_temp.id
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq demand_grp_temp_value[:model_flag]
          expect(body['model_payload']).to eq demand_grp_temp_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_grp_temp_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_grp_temp_value[:model_detail]
          expect(body['name']).to eq demand_grp_temp_value[:name]
          expect(body['mode']).to eq demand_grp_temp_value[:mode]
        }
      end
    end
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/demand_grp_temps/{id}' do
    parameter 'activity_temp_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_grp_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_demand_grp_temps.first.id }
        it {
          body = JSON.parse(response.body)
          demand_grp_temp = @travel_demand_grp_temps.first
          expect(body['activity_temp_id']).to eq @activity_temp.id
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq demand_grp_temp.model_flag
          expect(body['model_payload']).to eq demand_grp_temp.model_payload
          expect(body['model_payload_summary']).to eq demand_grp_temp.model_payload_summary
          expect(body['model_detail']).to eq demand_grp_temp.model_detail
          expect(body['name']).to eq demand_grp_temp.name
        }
      end
    end

    patch(summary: 'update demand_grp_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_grp_temp, in: :body, schema: demand_grp_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_demand_grp_temps.first.id }
        let(:demand_grp_temp) do
          { demand_grp_temp: demand_grp_temp_value }
        end
      end
    end

    delete(summary: 'delete demand_grp_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_demand_grp_temps.first.id }
        it {
          expect(@activity_temp.demand_grp_temps.count).to eq(@travel_demand_grp_temp_count-1)
        }
      end
    end
  end
end
