require 'swagger_helper'

RSpec.describe '/travel/manage/days', type: :request, capture_examples: true, tags: [" travel manage"] do
  day_ref = {
    type: :object, properties: {
      day: {
        type: :object, properties: {
          activity_id: { type: :integer, description: '' },
          day_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          date: { type: :date, description: '日期' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  day_value = FactoryBot.attributes_for(:travel_day)

  before :each do
    @user.add_role(:travel_admin)
    @activity = @travel_activities.first
    # @travel_days = @activity.days
    # expect(@activity.reload.days.count).to eq(10)
    # @travel_day_count = Travel::Day.count
  end

  path '/travel/manage/days' do

    get(summary: 'list days') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_day_count
        }
      end
    end
  end

  path '/travel/manage/days/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show day') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_days.first.id }
        it {
          body = JSON.parse(response.body)
          day = @travel_days.first.reload
          expect(body['activity_id']).to eq day.activity_id
          expect(body['day_temp_id']).to eq day.day_temp_id
          expect(body['app_id']).to eq day.app_id
          expect(body['model_flag']).to eq day.model_flag
          expect(body['model_payload']).to eq day.model_payload
          expect(body['model_payload_summary']).to eq day.model_payload_summary
          expect(body['model_detail']).to eq day.model_detail
          expect(body['name']).to eq day.name
          expect(body['desc']).to eq day.desc
          expect(body['date']).to eq day.date.strftime('%Y-%m-%d')
          expect(body['position']).to eq day.position
        }
      end
    end
  end

  path '/travel/manage/activities/{activity_id}/days' do
    parameter 'activity_id', in: :path, type: :string

    get(summary: 'list days') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_activity.days.count
        }
      end
    end

    post(summary: 'create day') do
      produces 'application/json'
      consumes 'application/json'
      parameter :day, in: :body, schema: day_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:day) do
          { day: day_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['activity_id']).to eq @activity.id
          expect(body['day_temp_id']).to eq day_value[:day_temp_id]
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq day_value[:model_flag]
          expect(body['model_payload']).to eq day_value[:model_payload]
          expect(body['model_payload_summary']).to eq day_value[:model_payload_summary]
          expect(body['model_detail']).to eq day_value[:model_detail]
          expect(body['name']).to eq day_value[:name]
          expect(body['desc']).to eq day_value[:desc]
          expect(body['date']).to eq @activity.start_at.strftime('%Y-%m-%d')
          expect(body['position']).to eq day_value[:position]
        }
      end
    end
  end

  path '/travel/manage/activities/{activity_id}/days/{id}' do
    parameter 'activity_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show day') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_days.first.id }
        it {
          body = JSON.parse(response.body)
          day = @travel_days.first.reload
          expect(body['activity_id']).to eq day.activity_id
          expect(body['day_temp_id']).to eq day.day_temp_id
          expect(body['app_id']).to eq day.app_id
          expect(body['model_flag']).to eq day.model_flag
          expect(body['model_payload']).to eq day.model_payload
          expect(body['model_payload_summary']).to eq day.model_payload_summary
          expect(body['model_detail']).to eq day.model_detail
          expect(body['name']).to eq day.name
          expect(body['desc']).to eq day.desc
          expect(body['date']).to eq day.date.strftime('%Y-%m-%d')
          expect(body['position']).to eq day.position
        }
      end
    end

    patch(summary: 'update day') do
      produces 'application/json'
      consumes 'application/json'
      parameter :day, in: :body, schema: day_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_days.first.id }
        let(:day) do
          { day: day_value }
        end
      end
    end

    delete(summary: 'delete day') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @travel_days.first.id }
        it {
          expect(Travel::Day.count).to eq(@travel_day_count-1)
          expect(@activity.days.count).to eq(1)
          expect(@activity.reload.duration).to eq(1)
        }
      end
    end
  end
end
