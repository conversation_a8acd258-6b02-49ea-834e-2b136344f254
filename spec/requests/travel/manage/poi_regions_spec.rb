require 'swagger_helper'

RSpec.describe 'travel/manage/poi_regions', type: :request, capture_examples: true, tags: ["travel manage"] do
  region_ref = {
    type: :object, properties: {
      region: {
        type: :object, properties: {
          parent_id: { type: :integer, description: 'closure tree parent_id' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          detail: { type: :jsonb, description: '详情' },
          position: { type: :integer, description: '排序' },
          country_id: { type: :integer, description: '' },
        }
      }
    }
  }
  region_value = FactoryBot.attributes_for(:travel_poi_region)

  before :each do
    @user.add_role :travel_admin
    @poi_country = @travel_poi_country
    @travel_poi_region.update app: @app
  end

  path '/travel/manage/poi_regions' do
    get(summary: 'list poi_regions') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_region_count
        }
      end
    end
  end

  path '/travel/manage/poi_countries/{poi_country_id}/poi_regions' do
    parameter 'poi_country_id', in: :path, type: :string

    get(summary: 'list poi_regions') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:poi_country_id) { @poi_country.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_region_count
        }
      end
    end

    post(summary: 'create poi_region') do
      produces 'application/json'
      consumes 'application/json'
      parameter :region, in: :body, schema: region_ref
      response(201, description: 'successful') do
        let(:poi_country_id) { @poi_country.id }
        let(:region) do
          { region: region_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq @user.id
          expect(body['parent_id']).to eq region_value[:parent_id]
          expect(body['type']).to eq region_value[:type]
          expect(body['model_flag']).to eq region_value[:model_flag]
          expect(body['model_payload']).to eq region_value[:model_payload]
          expect(body['model_payload_summary']).to eq region_value[:model_payload_summary]
          expect(body['model_detail']).to eq region_value[:model_detail]
          expect(body['name']).not_to be_nil
          expect(body['detail']).to eq region_value[:detail]
          expect(body['country_id']).to eq @travel_poi_country.id
        }
      end
    end
  end

  path '/travel/manage/poi_countries/{poi_country_id}/poi_regions/{id}' do
    parameter 'poi_country_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show poi_region') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:poi_country_id) { @poi_country.id }
        let(:id) { @travel_poi_regions.first.id }
        it {
          body = JSON.parse(response.body)
          region = @travel_poi_regions.first
          expect(body['app_id']).to eq region.app_id
          expect(body['creator_id']).to eq region.creator_id
          expect(body['parent_id']).to eq region.parent_id
          expect(body['type']).to eq region.type
          expect(body['model_flag']).to eq region.model_flag
          expect(body['model_payload']).to eq region.model_payload
          expect(body['model_payload_summary']).to eq region.model_payload_summary
          expect(body['model_detail']).to eq region.model_detail
          expect(body['name']).to eq region.name
          expect(body['detail']).to eq region.detail
          expect(body['position']).to eq region.position
          expect(body['country_id']).to eq region.country_id
        }
      end
    end

    patch(summary: 'update poi_region') do
      produces 'application/json'
      consumes 'application/json'
      parameter :region, in: :body, schema: region_ref
      response(201, description: 'successful') do
        let(:poi_country_id) { @poi_country.id }
        let(:id) { @travel_poi_regions.first.id }
        let(:region) do
          { region: region_value }
        end
      end
    end

    delete(summary: 'delete poi_region') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:poi_country_id) { @poi_country.id }
        let(:id) { @travel_poi_regions.first.id }
        it {
          expect(Travel::Poi::Region.count).to eq(@travel_poi_region_count-1)
        }
      end
    end
  end
end
