require 'swagger_helper'

RSpec.describe 'travel/manage/demand_grp_presets', type: :request, capture_examples: true, tags: ["travel manage"] do
  demand_grp_preset_ref = {
    type: :object, properties: {
      demand_grp_preset: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          state: { type: :string, description: '状态' },
          form: { type: :jsonb, description: '表单' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  demand_grp_preset_value = FactoryBot.attributes_for(:travel_demand_grp_preset)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/demand_grp_presets' do

    get(summary: 'list demand_grp_presets') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_grp_preset_count
        }
      end
    end

    post(summary: 'create demand_grp_preset') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_grp_preset, in: :body, schema: demand_grp_preset_ref
      response(201, description: 'successful') do
        let(:demand_grp_preset) do
          { demand_grp_preset: demand_grp_preset_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq demand_grp_preset_value[:name]
          expect(body['desc']).to eq demand_grp_preset_value[:desc]
          expect(body['state']).to eq demand_grp_preset_value[:state]
          expect(body['form']).to eq demand_grp_preset_value[:form]
          expect(body['payload']).to eq demand_grp_preset_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/demand_grp_presets/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_grp_preset') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_demand_grp_presets.first.id }
        it {
          body = JSON.parse(response.body)
          demand_grp_preset = @travel_demand_grp_presets.first
          expect(body['app_id']).to eq demand_grp_preset.app_id
          expect(body['name']).to eq demand_grp_preset.name
          expect(body['desc']).to eq demand_grp_preset.desc
          expect(body['state']).to eq demand_grp_preset.state
          expect(body['form']).to eq demand_grp_preset.form
          expect(body['payload']).to eq demand_grp_preset.payload
        }
      end
    end

    patch(summary: 'update demand_grp_preset') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_grp_preset, in: :body, schema: demand_grp_preset_ref
      response(201, description: 'successful') do
        let(:id) { @travel_demand_grp_presets.first.id }
        let(:demand_grp_preset) do
          { demand_grp_preset: demand_grp_preset_value }
        end
      end
    end

    delete(summary: 'delete demand_grp_preset') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_demand_grp_presets.first.id }
        it {
          expect(Travel::DemandGrpPreset.count).to eq(@travel_demand_grp_preset_count-1)
        }
      end
    end
  end
end
