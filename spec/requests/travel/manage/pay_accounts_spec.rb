require 'swagger_helper'

RSpec.describe 'travel/manage/pay_accounts', type: :request, capture_examples: true, tags: ["travel manage"] do
  pay_account_ref = {
    type: :object, properties: {
      pay_account: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          supplier_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '账户名称' },
          state: { type: :string, description: '状态' },
          account_type: { type: :string, description: '账户类型' },
          payload: { type: :jsonb, description: '字段' },
        }
      }
    }
  }
  pay_account_value = FactoryBot.attributes_for(:travel_pay_account)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/pay_accounts' do

    get(summary: 'list pay_accounts') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_pay_account_count
        }
      end
    end

    post(summary: 'create pay_account') do
      produces 'application/json'
      consumes 'application/json'
      parameter :pay_account, in: :body, schema: pay_account_ref
      response(201, description: 'successful') do
        let(:pay_account) do
          { pay_account: pay_account_value.merge(supplier_id: @travel_supplier.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['supplier_id']).to eq @travel_supplier.id
          expect(body['model_flag']).to eq pay_account_value[:model_flag]
          expect(body['model_payload']).to eq pay_account_value[:model_payload]
          expect(body['model_payload_summary']).to eq pay_account_value[:model_payload_summary]
          expect(body['model_detail']).to eq pay_account_value[:model_detail]
          expect(body['type']).to eq pay_account_value[:type]
          expect(body['name']).not_to be_nil
          expect(body['state']).to eq pay_account_value[:state]
          expect(body['account_type']).to eq pay_account_value[:account_type]
          expect(body['payload']).to eq pay_account_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/pay_accounts/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show pay_account') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_pay_accounts.first.id }
        it {
          body = JSON.parse(response.body)
          pay_account = @travel_pay_accounts.first
          expect(body['app_id']).to eq pay_account.app_id
          expect(body['supplier_id']).to eq pay_account.supplier_id
          expect(body['model_flag']).to eq pay_account.model_flag
          expect(body['model_payload']).to eq pay_account.model_payload
          expect(body['model_payload_summary']).to eq pay_account.model_payload_summary
          expect(body['model_detail']).to eq pay_account.model_detail
          expect(body['type']).to eq pay_account.type
          expect(body['name']).to eq pay_account.name
          expect(body['state']).to eq pay_account.state
          expect(body['account_type']).to eq pay_account.account_type
          expect(body['payload']).to eq pay_account.payload
        }
      end
    end

    patch(summary: 'update pay_account') do
      produces 'application/json'
      consumes 'application/json'
      parameter :pay_account, in: :body, schema: pay_account_ref
      response(201, description: 'successful') do
        let(:id) { @travel_pay_accounts.first.id }
        let(:pay_account) do
          { pay_account: pay_account_value }
        end
      end
    end

    delete(summary: 'delete pay_account') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_pay_accounts.first.id }
        it {
          expect(Travel::PayAccount.count).to eq(@travel_pay_account_count-1)
        }
      end
    end
  end
end
