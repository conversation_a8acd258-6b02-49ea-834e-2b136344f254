require 'swagger_helper'

RSpec.describe 'travel/manage/notice_grps', type: :request, capture_examples: true, tags: ["travel manage"] do
  notice_grp_ref = {
    type: :object, properties: {
      notice_grp: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  notice_grp_value = FactoryBot.attributes_for(:travel_notice_grp)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/notice_grps' do

    get(summary: 'list notice_grps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_notice_grp_count
        }
      end
    end

    post(summary: 'create notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :notice_grp, in: :body, schema: notice_grp_ref
      response(201, description: 'successful') do
        let(:notice_grp) do
          { notice_grp: notice_grp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq notice_grp_value[:app_id]
          expect(body['type']).to eq notice_grp_value[:type]
          expect(body['name']).to eq notice_grp_value[:name]
          expect(body['state']).to eq notice_grp_value[:state]
          expect(body['payload']).to eq notice_grp_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/notice_grps/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_notice_grps.first.id }
        it {
          body = JSON.parse(response.body)
          notice_grp = @travel_notice_grps.first
          expect(body['app_id']).to eq notice_grp.app_id
          expect(body['type']).to eq notice_grp.type
          expect(body['name']).to eq notice_grp.name
          expect(body['state']).to eq notice_grp.state
          expect(body['payload']).to eq notice_grp.payload
        }
      end
    end

    patch(summary: 'update notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :notice_grp, in: :body, schema: notice_grp_ref
      response(201, description: 'successful') do
        let(:id) { @travel_notice_grps.first.id }
        let(:notice_grp) do
          { notice_grp: notice_grp_value }
        end
      end
    end

    delete(summary: 'delete notice_grp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_notice_grps.first.id }
        it {
          expect(Travel::NoticeGrp.count).to eq(@travel_notice_grp_count-1)
        }
      end
    end
  end
end
