require 'swagger_helper'

RSpec.describe 'travel/manage/offers', type: :request, capture_examples: true, tags: ["travel manage"] do
  offer_ref = {
    type: :object, properties: {
      offer: {
        type: :object, properties: {
          payment_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          pay_state: { type: :string, description: '' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          supplier_id: { type: :integer, description: '' },
        }
      }
    }
  }
  offer_value = FactoryBot.attributes_for(:travel_offer)

  before :each do
    @user.add_role :travel_admin
    @activity = @travel_activity
  end

  path '/travel/manage/activities/{activity_id}/offers' do
    parameter 'activity_id', in: :path, type: :string

    get(summary: 'list offers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @activity.offers.count
        }
      end
    end

    post(summary: 'create offer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :offer, in: :body, schema: offer_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:offer) do
          { offer: offer_value.merge(
            supplier_id: @travel_supplier.id,
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['payment_id']).to eq offer_value[:payment_id]
          expect(body['model_flag']).to eq offer_value[:model_flag]
          expect(body['model_payload']).to eq offer_value[:model_payload]
          expect(body['model_payload_summary']).to eq offer_value[:model_payload_summary]
          expect(body['model_detail']).to eq offer_value[:model_detail]
          expect(body['name']).to eq offer_value[:name]
          expect(body['state']).to eq 'pending'
          expect(body['pay_state']).to eq 'unpaid'
          expect(body['amount']).to eq '88.0'
          expect(body['amount_unit']).to eq offer_value[:amount_unit]
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq offer_value[:amount_rmb]
          expect(body['supplier_id']).to eq @travel_supplier.id
        }
      end
    end
  end

  path '/travel/manage/activities/{activity_id}/offers/{id}' do
    parameter 'activity_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show offer') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @activity.offers.first.id }
        it {
          body = JSON.parse(response.body)
          offer = @activity.offers.first
          expect(body['payment_id']).to eq offer.payment_id
          expect(body['model_flag']).to eq offer.model_flag
          expect(body['model_payload']).to eq offer.model_payload
          expect(body['model_payload_summary']).to eq offer.model_payload_summary
          expect(body['model_detail']).to eq offer.model_detail
          expect(body['name']).to eq offer.name
          expect(body['state']).to eq offer.state
          expect(body['pay_state']).to eq offer.pay_state
          expect(body['amount']).to eq offer.amount.to_s
          expect(body['amount_unit']).to eq offer.amount_unit
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq offer.amount_rmb
          expect(body['supplier_id']).to eq offer.supplier_id
        }
      end
    end

    patch(summary: 'update offer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :offer, in: :body, schema: offer_ref
      response(201, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @activity.offers.first.id }
        let(:offer) do
          { offer: offer_value }
        end
      end
    end

    delete(summary: 'delete offer') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_id) { @activity.id }
        let(:id) { @activity.offers.first.id }
        it {
          expect(Travel::Offer.count).to eq(@travel_offer_count-1)
        }
      end
    end
  end

  path '/travel/manage/offers' do

    get(summary: 'list offers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_offer_count
        }
      end
    end

    post(summary: 'create offer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :offer, in: :body, schema: offer_ref
      response(201, description: 'successful') do
        let(:offer) do
          { offer: offer_value.merge(
            supplier_id: @travel_supplier.id
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['payment_id']).to eq offer_value[:payment_id]
          expect(body['model_flag']).to eq offer_value[:model_flag]
          expect(body['model_payload']).to eq offer_value[:model_payload]
          expect(body['model_payload_summary']).to eq offer_value[:model_payload_summary]
          expect(body['model_detail']).to eq offer_value[:model_detail]
          expect(body['name']).to eq offer_value[:name]
          expect(body['state']).to eq 'pending'
          expect(body['pay_state']).to eq 'unpaid'
          expect(body['amount']).to eq '88.0'
          expect(body['amount_unit']).to eq offer_value[:amount_unit]
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq offer_value[:amount_rmb]
          expect(body['supplier_id']).to eq @travel_supplier.id
        }
      end
    end
  end

  path '/travel/manage/offers/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show offer') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_offers.first.id }
        it {
          body = JSON.parse(response.body)
          offer = @travel_offers.first
          expect(body['payment_id']).to eq offer.payment_id
          expect(body['model_flag']).to eq offer.model_flag
          expect(body['model_payload']).to eq offer.model_payload
          expect(body['model_payload_summary']).to eq offer.model_payload_summary
          expect(body['model_detail']).to eq offer.model_detail
          expect(body['name']).to eq offer.name
          expect(body['state']).to eq offer.state
          expect(body['pay_state']).to eq offer.pay_state
          expect(body['amount']).to eq offer.amount.to_s
          expect(body['amount_unit']).to eq offer.amount_unit
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq offer.amount_rmb
          expect(body['supplier_id']).to eq offer.supplier_id
        }
      end
    end

    patch(summary: 'update offer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :offer, in: :body, schema: offer_ref
      response(201, description: 'successful') do
        let(:id) { @travel_offers.first.id }
        let(:offer) do
          { offer: offer_value }
        end
      end
    end

    delete(summary: 'delete offer') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_offers.first.id }
        it {
          expect(Travel::Offer.count).to eq(@travel_offer_count-1)
        }
      end
    end
  end
end
