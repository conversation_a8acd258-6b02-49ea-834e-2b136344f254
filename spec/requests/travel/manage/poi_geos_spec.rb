require 'swagger_helper'

RSpec.describe 'travel/manage/poi_geos', type: :request, capture_examples: true, tags: ["travel manage"] do
  geo_ref = {
    type: :object, properties: {
      geo: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          parent_id: { type: :integer, description: 'closure tree parent_id' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          detail: { type: :jsonb, description: '详情' },
          position: { type: :integer, description: '排序' },
          country_id: { type: :integer, description: '' },
          en_name: { type: :string, description: '英文名称' },
          cover_image: { type: :jsonb, description: '封面图' },
          meta: { type: :jsonb, description: '元数据' },
          longitude: { type: :float, description: '经度' },
          latitude: { type: :float, description: '纬度' },
          address: { type: :string, description: '地址' },
          recommended_at: { type: :datetime, description: '' },
          is_recommended: { type: :boolean, description: '' },
        }
      }
    }
  }
  geo_value = FactoryBot.attributes_for(:travel_poi_geo)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/poi_geos' do

    get(summary: 'list poi_geos') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_geo_count
        }
      end
    end

    post(summary: 'create poi_geo') do
      produces 'application/json'
      consumes 'application/json'
      parameter :geo, in: :body, schema: geo_ref
      response(201, description: 'successful') do
        let(:geo) do
          { geo: geo_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq @user.id
          expect(body['parent_id']).to eq geo_value[:parent_id]
          expect(body['model_flag']).to eq geo_value[:model_flag]
          expect(body['model_payload']).to eq geo_value[:model_payload]
          expect(body['model_payload_summary']).to eq geo_value[:model_payload_summary]
          expect(body['model_detail']).to eq geo_value[:model_detail]
          expect(body['name']).to eq geo_value[:name]
          expect(body['detail']).to eq geo_value[:detail]
          expect(body['position']).to eq geo_value[:position]
          expect(body['country_id']).to eq geo_value[:country_id]
          expect(body['en_name']).to eq geo_value[:en_name]
          expect(body['cover_image']).to eq geo_value[:cover_image]
          expect(body['meta']).to eq geo_value[:meta]
          expect(body['longitude']).to eq geo_value[:longitude]
          expect(body['latitude']).to eq geo_value[:latitude]
          expect(body['address']).to eq geo_value[:address]
          expect(body['recommended_at']).to eq geo_value[:recommended_at]
          expect(body['is_recommended']).to eq geo_value[:is_recommended]
        }
      end
    end
  end

  path '/travel/manage/poi_geos/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show poi_geo') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_poi_geos.first.id }
        it {
          body = JSON.parse(response.body)
          geo = @travel_poi_geos.first
          expect(body['app_id']).to eq geo.app_id
          expect(body['creator_id']).to eq geo.creator_id
          expect(body['parent_id']).to eq geo.parent_id
          expect(body['type']).to eq geo.type
          expect(body['model_flag']).to eq geo.model_flag
          expect(body['model_payload']).to eq geo.model_payload
          expect(body['model_payload_summary']).to eq geo.model_payload_summary
          expect(body['model_detail']).to eq geo.model_detail
          expect(body['name']).to eq geo.name
          expect(body['detail']).to eq geo.detail
          expect(body['position']).to eq geo.position
          expect(body['country_id']).to eq geo.country_id
          expect(body['en_name']).to eq geo.en_name
          expect(body['cover_image']).to eq geo.cover_image
          expect(body['meta']).to eq geo.meta
          expect(body['longitude']).to eq geo.longitude
          expect(body['latitude']).to eq geo.latitude
          expect(body['address']).to eq geo.address
          expect(body['recommended_at']).to eq geo.recommended_at
          expect(body['is_recommended']).to eq geo.is_recommended
        }
      end
    end

    patch(summary: 'update poi_geo') do
      produces 'application/json'
      consumes 'application/json'
      parameter :geo, in: :body, schema: geo_ref
      response(201, description: 'successful') do
        let(:id) { @travel_poi_geos.first.id }
        let(:geo) do
          { geo: geo_value }
        end
      end
    end

    delete(summary: 'delete poi_geo') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_poi_geos.first.id }
        it {
          expect(Travel::Poi::Geo.count).to eq(@travel_poi_geo_count-1)
        }
      end
    end
  end
end
