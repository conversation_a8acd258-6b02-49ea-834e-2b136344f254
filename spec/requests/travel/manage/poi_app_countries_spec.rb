require 'swagger_helper'

RSpec.describe 'travel/manage/poi_app_countries', type: :request, capture_examples: true, tags: ["travel manage"] do
  app_country_ref = {
    type: :object, properties: {
      app_country: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          country_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          color: { type: :string, description: '颜色' },
          position: { type: :integer, description: '位置' },
          conf: { type: :jsonb, description: '其他配置' },
        }
      }
    }
  }
  app_country_value = FactoryBot.attributes_for(:travel_poi_app_country)

  before :each do
    @user.add_role :travel_admin
  end

  path '/travel/manage/poi_app_countries' do

    get(summary: 'list poi_app_countries') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_poi_app_country_count
        }
      end
    end

    post(summary: 'create poi_app_country') do
      produces 'application/json'
      consumes 'application/json'
      parameter :app_country, in: :body, schema: app_country_ref
      response(201, description: 'successful') do
        let(:app_country) do
          { app_country: app_country_value.merge(
            country_id: @travel_poi_countries.last.id
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['country_id']).to eq @travel_poi_countries.last.id
          expect(body['model_flag']).to eq app_country_value[:model_flag]
          expect(body['model_payload']).to eq app_country_value[:model_payload]
          expect(body['model_payload_summary']).to eq app_country_value[:model_payload_summary]
          expect(body['model_detail']).to eq app_country_value[:model_detail]
          expect(body['color']).not_to be_nil
          expect(body['position']).not_to be_nil
          expect(body['conf']).to eq app_country_value[:conf]
        }
      end
    end
  end

  path '/travel/manage/poi_app_countries/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show poi_app_country') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_poi_app_countries.first.id }
        it {
          body = JSON.parse(response.body)
          app_country = @travel_poi_app_countries.first
          expect(body['app_id']).to eq app_country.app_id
          expect(body['country_id']).to eq app_country.country_id
          expect(body['model_flag']).to eq app_country.model_flag
          expect(body['model_payload']).to eq app_country.model_payload
          expect(body['model_payload_summary']).to eq app_country.model_payload_summary
          expect(body['model_detail']).to eq app_country.model_detail
          expect(body['color']).to eq app_country.color
          expect(body['position']).to eq app_country.position
          expect(body['conf']).to eq app_country.conf
        }
      end
    end

    patch(summary: 'update poi_app_country') do
      produces 'application/json'
      consumes 'application/json'
      parameter :app_country, in: :body, schema: app_country_ref
      response(201, description: 'successful') do
        let(:id) { @travel_poi_app_countries.first.id }
        let(:app_country) do
          { app_country: app_country_value }
        end
      end
    end

    delete(summary: 'delete poi_app_country') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_poi_app_countries.first.id }
        it {
          expect(Travel::Poi::AppCountry.count).to eq(@travel_poi_app_country_count-1)
        }
      end
    end
  end
end
