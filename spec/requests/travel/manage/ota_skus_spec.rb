require 'swagger_helper'

RSpec.describe '/travel/manage/ota_skus', type: :request, capture_examples: true, tags: [" travel manage"] do
  ota_sku_ref = {
    type: :object, properties: {
      ota_sku: {
        type: :object, properties: {
          channel_id: { type: :integer, description: '' },
          product_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          ota_product_no: { type: :string, description: 'OTA 商品 ID' },
          ota_sku_no: { type: :string, description: 'OTA sku ID' },
          meta: { type: :jsonb, description: '元数据' },
          app_id: { type: :integer, description: '' },
        }
      }
    }
  }
  ota_sku_value = FactoryBot.attributes_for(:travel_ota_sku)

  before :each do
    @user.add_role(:travel_admin)
    @travel_ota_sku_count = 5
    @travel_ota_skus = FactoryBot.create_list(:travel_ota_sku, @travel_ota_sku_count, app: @app, channel: @travel_channel)
  end

  path '/travel/manage/ota_skus' do

    get(summary: 'list ota_skus') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_ota_sku_count
        }
      end
    end

    post(summary: 'create ota_sku') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ota_sku, in: :body, schema: ota_sku_ref
      response(201, description: 'successful') do
        let(:ota_sku) do
          { ota_sku: ota_sku_value.merge(channel_id: @travel_channel.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['channel_id']).to eq @travel_channel.id
          expect(body['product_id']).to eq ota_sku_value[:product_id]
          expect(body['model_flag']).to eq ota_sku_value[:model_flag]
          expect(body['model_payload']).to eq ota_sku_value[:model_payload]
          expect(body['model_payload_summary']).to eq ota_sku_value[:model_payload_summary]
          expect(body['model_detail']).to eq ota_sku_value[:model_detail]
          expect(body['type']).to eq ota_sku_value[:type]
          expect(body['name']).not_to be_nil
          expect(body['ota_product_no']).to eq ota_sku_value[:ota_product_no]
          expect(body['ota_sku_no']).to eq ota_sku_value[:ota_sku_no]
          expect(body['meta']).not_to be_nil
          expect(body['app_id']).to eq @app.id
        }
      end
    end
  end

  path '/travel/manage/ota_skus/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show ota_sku') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_ota_skus.first.id }
        it {
          body = JSON.parse(response.body)
          ota_sku = @travel_ota_skus.first
          expect(body['channel_id']).to eq @travel_channel.id
          expect(body['product_id']).to eq ota_sku.product_id
          expect(body['model_flag']).to eq ota_sku.model_flag
          expect(body['model_payload']).to eq ota_sku.model_payload
          expect(body['model_payload_summary']).to eq ota_sku.model_payload_summary
          expect(body['model_detail']).to eq ota_sku.model_detail
          expect(body['type']).to eq ota_sku.type
          expect(body['name']).to eq ota_sku.name
          expect(body['ota_product_no']).to eq ota_sku.ota_product_no
          expect(body['ota_sku_no']).to eq ota_sku.ota_sku_no
          expect(body['meta']).not_to be_nil
          expect(body['app_id']).to eq @app.id
        }
      end
    end

    patch(summary: 'update ota_sku') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ota_sku, in: :body, schema: ota_sku_ref
      response(201, description: 'successful') do
        let(:id) { @travel_ota_skus.first.id }
        let(:ota_sku) do
          { ota_sku: ota_sku_value }
        end
      end
    end

    delete(summary: 'delete ota_sku') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_ota_skus.first.id }
        it {
          expect(Travel::OtaSku.count).to eq(@travel_ota_sku_count-1)
        }
      end
    end
  end
end
