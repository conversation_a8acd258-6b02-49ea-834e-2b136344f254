require 'swagger_helper'

RSpec.describe '/travel/manage/demand_temps', type: :request, capture_examples: true, tags: [" travel manage"] do
  demand_temp_ref = {
    type: :object, properties: {
      demand_temp: {
        type: :object, properties: {
          product_id: { type: :integer, description: '' },
          demand_grp_temp_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          poi_region_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :text, description: '描述' },
          count: { type: :integer, description: '数量' },
          duration: { type: :integer, description: '天数' },
          start_offset: { type: :integer, description: '开始日期偏移量' },
          end_offset: { type: :integer, description: '结束日期偏移量' },
          # around_amount: { type: :decimal, description: '预估金额' },
          # around_amount_unit: { type: :string, description: '预估金额单位' },
          # around_amount_ratio: { type: :decimal, description: '预估金额对人民币汇率' },
          # around_amount_rmb: { type: :decimal, description: '预估金额兑换为人民币价格' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          mode: { type: :string, description: '分类' },
          demand_define_id: { type: :integer, description: '' },
          payload: { type: :jsonb, description: 'payload payload存储的字段' },
          payload_summary: { type: :jsonb, description: 'payload summary存储的字段' },
          wait_for_move_to_start_offset: { type: :integer, description: '记录移动天数时的暂存字段' },
        }
      }
    }
  }
  demand_temp_value = FactoryBot.attributes_for(:travel_demand_temp)

  before :each do
    @user.add_role(:travel_admin)
    @product = create(:travel_product, duration: 10, app: @app)
    @activity_temp = create(:travel_activity_temp, app: @app)
    @travel_demand_grp_temp = create(:travel_demand_grp_temp, activity_temp: @activity_temp, app: @app)
    @travel_demand_temp_count = 5
    @travel_demand_temps = FactoryBot.create_list(
      :travel_demand_temp,
      @travel_demand_temp_count,
      product: @product,
      demand_grp_temp: @travel_demand_grp_temp,
      activity_temp: @activity_temp,
      app: @app
    )
  end

  path '/travel/manage/activity_temps/{activity_temp_id}/demand_temps' do
    parameter 'activity_temp_id', in: :path, type: :string

    get(summary: 'list demand_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_temp_count
        }
      end
    end

    post(summary: 'create demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_temp, in: :body, schema: demand_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:demand_temp) do
          { demand_temp: demand_temp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['product_id']).to eq nil
          expect(body['demand_grp_temp_id']).to eq demand_temp_value[:demand_grp_temp_id]
          expect(body['app_id']).to eq @app.id
          expect(body['poi_region_id']).to eq demand_temp_value[:poi_region_id]
          expect(body['model_flag']).to eq demand_temp_value[:model_flag]
          expect(body['model_payload']).to eq demand_temp_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_temp_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_temp_value[:model_detail]
          expect(body['name']).to eq demand_temp_value[:name]
          expect(body['desc']).to eq demand_temp_value[:desc]
          expect(body['count']).to eq demand_temp_value[:count]
          expect(body['duration']).to eq demand_temp_value[:duration]
          expect(body['start_offset']).to eq 0
          # expect(body['end_offset']).to eq 0
          # # expect(body['around_amount']).to eq demand_temp_value[:around_amount]
          # # expect(body['around_amount_unit']).to eq demand_temp_value[:around_amount_unit]
          # # expect(body['around_amount_ratio']).to eq demand_temp_value[:around_amount_ratio]
          # # expect(body['around_amount_rmb']).to eq demand_temp_value[:around_amount_rmb]
          expect(body['amount']).to eq demand_temp_value[:amount]
          expect(body['amount_unit']).to eq demand_temp_value[:amount_unit]
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq demand_temp_value[:amount_rmb]
          expect(body['mode']).to eq demand_temp_value[:mode]
          expect(body['demand_define_id']).to eq demand_temp_value[:demand_define_id]
          expect(body['payload']).to eq demand_temp_value[:payload]
          expect(body['payload_summary']).to eq demand_temp_value[:payload_summary]
          expect(body['wait_for_move_to_start_offset']).to eq demand_temp_value[:wait_for_move_to_start_offset]
        }
      end
    end
  end


  path '/travel/manage/activity_temps/{activity_temp_id}/demand_temps/{id}' do
    parameter 'activity_temp_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_demand_temps.first.id }
        it {
          body = JSON.parse(response.body)
          demand_temp = @travel_demand_temps.first
          expect(body['product_id']).to eq demand_temp.product_id
          expect(body['demand_grp_temp_id']).to eq demand_temp.demand_grp_temp_id
          expect(body['app_id']).to eq demand_temp.app_id
          expect(body['poi_region_id']).to eq demand_temp.poi_region_id
          expect(body['model_flag']).to eq demand_temp.model_flag
          expect(body['model_payload']).to eq demand_temp.model_payload
          expect(body['model_payload_summary']).to eq demand_temp.model_payload_summary
          expect(body['model_detail']).to eq demand_temp.model_detail
          expect(body['name']).to eq demand_temp.name
          expect(body['desc']).to eq demand_temp.desc
          expect(body['count']).to eq demand_temp.count
          expect(body['duration']).to eq demand_temp.duration
          expect(body['start_offset']).to eq demand_temp.start_offset
          expect(body['end_offset']).to eq demand_temp.end_offset
          # # expect(body['around_amount']).to eq demand_temp.around_amount
          # # expect(body['around_amount_unit']).to eq demand_temp.around_amount_unit
          # # expect(body['around_amount_ratio']).to eq demand_temp.around_amount_ratio
          # # expect(body['around_amount_rmb']).to eq demand_temp.around_amount_rmb
          expect(body['amount']).to eq demand_temp.amount
          expect(body['amount_unit']).to eq demand_temp.amount_unit
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq demand_temp.amount_rmb
          expect(body['mode']).to eq demand_temp.mode
          expect(body['demand_define_id']).to eq demand_temp.demand_define_id
          expect(body['payload']).to eq demand_temp.payload
          expect(body['payload_summary']).to eq demand_temp.payload_summary
          expect(body['wait_for_move_to_start_offset']).to eq demand_temp.wait_for_move_to_start_offset
        }
      end
    end

    patch(summary: 'update demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_temp, in: :body, schema: demand_temp_ref
      response(201, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_demand_temps.first.id }
        let(:demand_temp) do
          { demand_temp: demand_temp_value }
        end
      end
    end

    delete(summary: 'delete demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:activity_temp_id) { @activity_temp.id }
        let(:id) { @travel_demand_temps.first.id }
        it {
          expect(@product.demand_temps.count).to eq(@travel_demand_temp_count-1)
        }
      end
    end
  end

  path '/travel/manage/products/{product_id}/demand_temps' do
    parameter 'product_id', in: :path, type: :string

    get(summary: 'list demand_temps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:product_id) { @product.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_demand_temp_count
        }
      end
    end

    post(summary: 'create demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_temp, in: :body, schema: demand_temp_ref
      response(201, description: 'successful') do
        let(:product_id) { @product.id }
        let(:demand_temp) do
          { demand_temp: demand_temp_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['product_id']).to eq @product.id
          expect(body['demand_grp_temp_id']).to eq demand_temp_value[:demand_grp_temp_id]
          expect(body['app_id']).to eq @app.id
          expect(body['poi_region_id']).to eq demand_temp_value[:poi_region_id]
          expect(body['model_flag']).to eq demand_temp_value[:model_flag]
          expect(body['model_payload']).to eq demand_temp_value[:model_payload]
          expect(body['model_payload_summary']).to eq demand_temp_value[:model_payload_summary]
          expect(body['model_detail']).to eq demand_temp_value[:model_detail]
          expect(body['name']).to eq demand_temp_value[:name]
          expect(body['desc']).to eq demand_temp_value[:desc]
          expect(body['count']).to eq demand_temp_value[:count]
          expect(body['duration']).to eq demand_temp_value[:duration]
          expect(body['start_offset']).to eq 0
          # expect(body['end_offset']).to eq 0
          # # expect(body['around_amount']).to eq demand_temp_value[:around_amount]
          # # expect(body['around_amount_unit']).to eq demand_temp_value[:around_amount_unit]
          # # expect(body['around_amount_ratio']).to eq demand_temp_value[:around_amount_ratio]
          # # expect(body['around_amount_rmb']).to eq demand_temp_value[:around_amount_rmb]
          expect(body['amount']).to eq demand_temp_value[:amount]
          expect(body['amount_unit']).to eq demand_temp_value[:amount_unit]
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq demand_temp_value[:amount_rmb]
          expect(body['mode']).to eq demand_temp_value[:mode]
          expect(body['demand_define_id']).to eq demand_temp_value[:demand_define_id]
          expect(body['payload']).to eq demand_temp_value[:payload]
          expect(body['payload_summary']).to eq demand_temp_value[:payload_summary]
          expect(body['wait_for_move_to_start_offset']).to eq demand_temp_value[:wait_for_move_to_start_offset]
        }
      end
    end
  end

  path '/travel/manage/products/{product_id}/demand_temps/{id}' do
    parameter 'product_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:product_id) { @product.id }
        let(:id) { @travel_demand_temps.first.id }
        it {
          body = JSON.parse(response.body)
          demand_temp = @travel_demand_temps.first
          expect(body['product_id']).to eq demand_temp.product_id
          expect(body['demand_grp_temp_id']).to eq demand_temp.demand_grp_temp_id
          expect(body['app_id']).to eq demand_temp.app_id
          expect(body['poi_region_id']).to eq demand_temp.poi_region_id
          expect(body['model_flag']).to eq demand_temp.model_flag
          expect(body['model_payload']).to eq demand_temp.model_payload
          expect(body['model_payload_summary']).to eq demand_temp.model_payload_summary
          expect(body['model_detail']).to eq demand_temp.model_detail
          expect(body['name']).to eq demand_temp.name
          expect(body['desc']).to eq demand_temp.desc
          expect(body['count']).to eq demand_temp.count
          expect(body['duration']).to eq demand_temp.duration
          expect(body['start_offset']).to eq demand_temp.start_offset
          expect(body['end_offset']).to eq demand_temp.end_offset
          # # expect(body['around_amount']).to eq demand_temp.around_amount
          # # expect(body['around_amount_unit']).to eq demand_temp.around_amount_unit
          # # expect(body['around_amount_ratio']).to eq demand_temp.around_amount_ratio
          # # expect(body['around_amount_rmb']).to eq demand_temp.around_amount_rmb
          expect(body['amount']).to eq demand_temp.amount
          expect(body['amount_unit']).to eq demand_temp.amount_unit
          expect(body['amount_ratio']).not_to be_nil
          expect(body['amount_rmb']).to eq demand_temp.amount_rmb
          expect(body['mode']).to eq demand_temp.mode
          expect(body['demand_define_id']).to eq demand_temp.demand_define_id
          expect(body['payload']).to eq demand_temp.payload
          expect(body['payload_summary']).to eq demand_temp.payload_summary
          expect(body['wait_for_move_to_start_offset']).to eq demand_temp.wait_for_move_to_start_offset
        }
      end
    end

    patch(summary: 'update demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      parameter :demand_temp, in: :body, schema: demand_temp_ref
      response(201, description: 'successful') do
        let(:product_id) { @product.id }
        let(:id) { @travel_demand_temps.first.id }
        let(:demand_temp) do
          { demand_temp: demand_temp_value }
        end
      end
    end

    delete(summary: 'delete demand_temp') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:product_id) { @product.id }
        let(:id) { @travel_demand_temps.first.id }
        it {
          expect(@product.demand_temps.count).to eq(@travel_demand_temp_count-1)
        }
      end
    end
  end
end
