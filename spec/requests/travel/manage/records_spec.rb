require 'swagger_helper'

RSpec.describe 'travel/manage/records', type: :request, capture_examples: true, tags: ["travel manage"] do
  record_ref = {
    type: :object, properties: {
      record: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          stars_count: { type: :integer, description: '收藏人数' },
          hotted_at: { type: :datetime, description: '' },
          is_hotted: { type: :boolean, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          desc: { type: :string, description: '描述' },
          icon: { type: :string, description: 'icon' },
          content: { type: :string, description: '内容' },
          cover_image: { type: :jsonb, description: '封面图' },
          position: { type: :integer, description: '排序' },
          price: { type: :float, description: '价格' },
          score: { type: :float, description: '评分' },
          used_count: { type: :integer, description: '使用人数' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  record_value = FactoryBot.attributes_for(:travel_record)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/records' do

    get(summary: 'list records') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_record_count
        }
      end
    end

    post(summary: 'create record') do
      produces 'application/json'
      consumes 'application/json'
      parameter :record, in: :body, schema: record_ref
      response(201, description: 'successful') do
        let(:record) do
          { record: record_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq @user.id
          expect(body['source_type']).to eq record_value[:source_type]
          expect(body['source_id']).to eq record_value[:source_id]
          expect(body['type']).to eq record_value[:type]
          expect(body['stars_count']).to eq record_value[:stars_count]
          expect(body['hotted_at']).to eq record_value[:hotted_at]
          expect(body['is_hotted']).to eq record_value[:is_hotted]
          expect(body['name']).to eq record_value[:name]
          expect(body['state']).to eq record_value[:state]
          expect(body['desc']).to eq record_value[:desc]
          expect(body['icon']).to eq record_value[:icon]
          expect(body['content']).to eq record_value[:content]
          expect(body['cover_image']).to eq record_value[:cover_image]
          expect(body['position']).to eq record_value[:position]
          expect(body['price']).to eq record_value[:price]
          expect(body['score']).to eq record_value[:score]
          expect(body['used_count']).to eq record_value[:used_count]
          expect(body['payload']).to eq record_value[:payload]
        }
      end
    end
  end

  path '/travel/manage/records/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show record') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_records.first.id }
        it {
          body = JSON.parse(response.body)
          record = @travel_records.first
          expect(body['app_id']).to eq record.app_id
          expect(body['creator_id']).to eq record.creator_id
          expect(body['source_type']).to eq record.source_type
          expect(body['source_id']).to eq record.source_id
          expect(body['type']).to eq record.type
          expect(body['stars_count']).to eq record.stars_count
          expect(body['hotted_at']).to eq record.hotted_at
          expect(body['is_hotted']).to eq record.is_hotted
          expect(body['name']).to eq record.name
          expect(body['state']).to eq record.state
          expect(body['desc']).to eq record.desc
          expect(body['icon']).to eq record.icon
          expect(body['content']).to eq record.content
          expect(body['cover_image']).to eq record.cover_image
          expect(body['position']).to eq record.position
          expect(body['price']).to eq record.price
          expect(body['score']).to eq record.score
          expect(body['used_count']).to eq record.used_count
          expect(body['payload']).to eq record.payload
        }
      end
    end

    patch(summary: 'update record') do
      produces 'application/json'
      consumes 'application/json'
      parameter :record, in: :body, schema: record_ref
      response(201, description: 'successful') do
        let(:id) { @travel_records.first.id }
        let(:record) do
          { record: record_value }
        end
      end
    end

    delete(summary: 'delete record') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_records.first.id }
        it {
          expect(Travel::Record.count).to eq(@travel_record_count-1)
        }
      end
    end
  end
end
