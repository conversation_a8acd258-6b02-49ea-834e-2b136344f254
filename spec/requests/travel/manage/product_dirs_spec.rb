require 'swagger_helper'

RSpec.describe '/travel/manage/product_dirs', type: :request, capture_examples: true, tags: [" travel manage"] do
  product_dir_ref = {
    type: :object, properties: {
      product_dir: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          desc: { type: :string, description: '描述' },
        }
      }
    }
  }
  product_dir_value = FactoryBot.attributes_for(:travel_product_dir)

  before :each do
    @user.add_role(:travel_admin)
    @travel_product_dir.products.destroy_all
  end

  path '/travel/manage/product_dirs' do

    get(summary: 'list product_dirs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_product_dir_count
        }
      end
    end

    post(summary: 'create product_dir') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product_dir, in: :body, schema: product_dir_ref
      response(201, description: 'successful') do
        let(:product_dir) do
          { product_dir: product_dir_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq product_dir_value[:model_flag]
          expect(body['model_payload']).to eq product_dir_value[:model_payload]
          expect(body['model_payload_summary']).to eq product_dir_value[:model_payload_summary]
          expect(body['model_detail']).to eq product_dir_value[:model_detail]
          expect(body['name']).not_to be_nil
          expect(body['desc']).to eq product_dir_value[:desc]
        }
      end
    end
  end

  path '/travel/manage/product_dirs/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show product_dir') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_product_dirs.first.id }
        it {
          body = JSON.parse(response.body)
          product_dir = @travel_product_dirs.first
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq product_dir.model_flag
          expect(body['model_payload']).to eq product_dir.model_payload
          expect(body['model_payload_summary']).to eq product_dir.model_payload_summary
          expect(body['model_detail']).to eq product_dir.model_detail
          expect(body['name']).to eq product_dir.name
          expect(body['desc']).to eq product_dir.desc
        }
      end
    end

    patch(summary: 'update product_dir') do
      produces 'application/json'
      consumes 'application/json'
      parameter :product_dir, in: :body, schema: product_dir_ref
      response(201, description: 'successful') do
        let(:id) { @travel_product_dirs.first.id }
        let(:product_dir) do
          { product_dir: product_dir_value }
        end
      end
    end

    delete(summary: 'delete product_dir') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_product_dirs.first.id }
        it {
          expect(Travel::ProductDir.count).to eq(@travel_product_dir_count-1)
        }
      end
    end
  end
end
