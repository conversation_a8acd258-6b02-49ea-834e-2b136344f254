require 'swagger_helper'

RSpec.describe 'travel/manage/receipts', type: :request, capture_examples: true, tags: ["travel manage"] do
  receipt_ref = {
    type: :object, properties: {
      receipt: {
        type: :object, properties: {
          payment_id: { type: :integer, description: '' },
          seq: { type: :string, description: '编号' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '金额单位' },
          amount_ratio: { type: :decimal, description: '金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '金额兑换为人民币价格' },
          content: { type: :text, description: '内容' },
          attachments: { type: :jsonb, description: '附件' },
        }
      }
    }
  }
  receipt_value = FactoryBot.attributes_for(:travel_receipt)

  before :each do
    @user.add_role :travel_manage
  end

  path '/travel/manage/receipts' do

    get(summary: 'list receipts') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_receipt_count
        }
      end
    end

    post(summary: 'create receipt') do
      produces 'application/json'
      consumes 'application/json'
      parameter :receipt, in: :body, schema: receipt_ref
      response(201, description: 'successful') do
        let(:receipt) do
          { receipt: receipt_value.merge(
            payment_id: @travel_payment.id
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['payment_id']).to eq @travel_payment.id
          expect(body['creator_id']).to eq @user.id
          expect(body['seq']).not_to be_nil
          expect(body['model_flag']).to eq receipt_value[:model_flag]
          expect(body['model_payload']).to eq receipt_value[:model_payload]
          expect(body['model_payload_summary']).to eq receipt_value[:model_payload_summary]
          expect(body['model_detail']).to eq receipt_value[:model_detail]
          # expect(body['amount']).to eq receipt_value[:amount]
          # expect(body['amount_unit']).to eq receipt_value[:amount_unit]
          # expect(body['amount_ratio']).to eq receipt_value[:amount_ratio]
          # expect(body['amount_rmb']).to eq receipt_value[:amount_rmb]
          expect(body['content']).to eq receipt_value['content']
          expect(body['attachments']).to eq receipt_value[:attachments]
        }
      end
    end
  end

  path '/travel/manage/receipts/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show receipt') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @travel_receipts.first.id }
        it {
          body = JSON.parse(response.body)
          receipt = @travel_receipts.first
          expect(body['app_id']).to eq receipt.app_id
          expect(body['payment_id']).to eq receipt.payment_id
          expect(body['creator_id']).to eq receipt.creator_id
          expect(body['seq']).to eq receipt.seq
          expect(body['model_flag']).to eq receipt.model_flag
          expect(body['model_payload']).to eq receipt.model_payload
          expect(body['model_payload_summary']).to eq receipt.model_payload_summary
          expect(body['model_detail']).to eq receipt.model_detail
          # expect(body['amount']).to eq receipt.amount
          # expect(body['amount_unit']).to eq receipt.amount_unit
          # expect(body['amount_ratio']).to eq receipt.amount_ratio
          # expect(body['amount_rmb']).to eq receipt.amount_rmb
          expect(body['content']).to eq receipt.content
          expect(body['attachments']).to eq receipt.attachments
        }
      end
    end

    patch(summary: 'update receipt') do
      produces 'application/json'
      consumes 'application/json'
      parameter :receipt, in: :body, schema: receipt_ref
      response(201, description: 'successful') do
        let(:id) { @travel_receipts.first.id }
        let(:receipt) do
          { receipt: receipt_value.merge(payment_id: @travel_payment.id) }
        end
      end
    end

    delete(summary: 'delete receipt') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @travel_receipts.first.id }
        it {
          expect(Travel::Receipt.count).to eq(@travel_receipt_count-1)
        }
      end
    end
  end
end
