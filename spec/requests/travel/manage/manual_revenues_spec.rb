require 'swagger_helper'

RSpec.describe 'travel/manage/manual_revenues', type: :request, capture_examples: true, tags: ["travel manage"] do
  manual_revenue_ref = {
    type: :object, properties: {
      manual_revenue: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          seq: { type: :string, description: '编号' },
          amount: { type: :decimal, description: '金额' },
          amount_unit: { type: :string, description: '支付金额单位' },
          amount_ratio: { type: :decimal, description: '支付金额对人民币汇率' },
          amount_rmb: { type: :decimal, description: '支付金额兑换为人民币价格' },
          mode: { type: :string, description: '模式，例如收款/补款等' },
          remark: { type: :string, description: '备注信息' },
          type: { type: :string, description: 'STI属性' },
          attachments: { type: :jsonb, description: '附件' },
        }
      }
    }
  }
  manual_revenue_value = FactoryBot.attributes_for(:travel_manual_revenue)

  before :each do
    @user.add_role :travel_manage
    @order = @travel_order
    create(:travel_manual_revenue, source: @order, app: @app)
    @travel_manual_revenues = @order.manual_revenues
    @travel_manual_revenue_count = @travel_manual_revenues.count
  end

  path '/travel/manage/orders/{order_id}/manual_revenues' do
    parameter 'order_id', in: :path, type: :string

    get(summary: 'list manual_revenues') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_manual_revenue_count
        }
      end
    end

    post(summary: 'create manual_revenue') do
      produces 'application/json'
      consumes 'application/json'
      parameter :manual_revenue, in: :body, schema: manual_revenue_ref
      response(201, description: 'successful') do
        let(:order_id) { @order.id }
        let(:manual_revenue) do
          { manual_revenue: manual_revenue_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['source_type']).to eq 'Travel::Order'
          expect(body['source_id']).to eq @order.id
          expect(body['data_type']).to eq manual_revenue_value[:data_type]
          expect(body['data_id']).to eq manual_revenue_value[:data_id]
          expect(body['model_flag']).to eq manual_revenue_value[:model_flag]
          expect(body['model_payload']).to eq manual_revenue_value[:model_payload]
          expect(body['model_payload_summary']).to eq manual_revenue_value[:model_payload_summary]
          expect(body['model_detail']).to eq manual_revenue_value[:model_detail]
          expect(body['seq']).not_to be_nil
          expect(body['amount']).to eq manual_revenue_value[:amount]
          expect(body['amount_unit']).to eq 'CNY'
          expect(body['amount_ratio']).to eq '1.0'
          expect(body['amount_rmb']).to eq manual_revenue_value[:amount_rmb]
          expect(body['mode']).to eq manual_revenue_value[:mode]
          expect(body['remark']).to eq manual_revenue_value[:remark]
          expect(body['type']).to eq 'Travel::ManualRevenue'
          expect(body['attachments']).to eq manual_revenue_value[:attachments]
        }
      end
    end
  end

  path '/travel/manage/orders/{order_id}/manual_revenues/{id}' do
    parameter 'order_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show manual_revenue') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_manual_revenues.first.id }
        it {
          body = JSON.parse(response.body)
          manual_revenue = @travel_manual_revenues.first
          expect(body['app_id']).to eq manual_revenue.app_id
          expect(body['source_type']).to eq manual_revenue.source_type
          expect(body['source_id']).to eq manual_revenue.source_id
          expect(body['data_type']).to eq manual_revenue.data_type
          expect(body['data_id']).to eq manual_revenue.data_id
          expect(body['model_flag']).to eq manual_revenue.model_flag
          expect(body['model_payload']).to eq manual_revenue.model_payload
          expect(body['model_payload_summary']).to eq manual_revenue.model_payload_summary
          expect(body['model_detail']).to eq manual_revenue.model_detail
          expect(body['seq']).to eq manual_revenue.seq
          expect(body['amount']).to eq manual_revenue.amount
          expect(body['amount_unit']).to eq manual_revenue.amount_unit
          expect(body['amount_ratio']).to eq manual_revenue.amount_ratio.to_s
          expect(body['amount_rmb']).to eq manual_revenue.amount_rmb
          expect(body['mode']).to eq manual_revenue.mode
          expect(body['remark']).to eq manual_revenue.remark
          expect(body['type']).to eq manual_revenue.type
          expect(body['attachments']).to eq manual_revenue.attachments
        }
      end
    end

    patch(summary: 'update manual_revenue') do
      produces 'application/json'
      consumes 'application/json'
      parameter :manual_revenue, in: :body, schema: manual_revenue_ref
      response(201, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_manual_revenues.first.id }
        let(:manual_revenue) do
          { manual_revenue: manual_revenue_value }
        end
      end
    end

    delete(summary: 'delete manual_revenue') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_manual_revenues.first.id }
        it {
          expect(Travel::ManualRevenue.count).to eq(@travel_manual_revenue_count-1)
        }
      end
    end
  end
end
