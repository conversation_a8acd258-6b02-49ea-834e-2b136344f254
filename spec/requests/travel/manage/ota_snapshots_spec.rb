require 'swagger_helper'

RSpec.describe '/travel/manage/ota_snapshots', type: :request, capture_examples: true, tags: [" travel manage"] do
  ota_snapshot_ref = {
    type: :object, properties: {
      ota_snapshot: {
        type: :object, properties: {
          ota_sku_id: { type: :integer, description: '' },
          order_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          amount: { type: :decimal, description: '金额' },
          count: { type: :integer, description: '数量' },
          meta: { type: :jsonb, description: '元数据' },
        }
      }
    }
  }
  ota_snapshot_value = FactoryBot.attributes_for(:travel_ota_snapshot)

  before :each do
    @user.add_role(:travel_admin)
    @ota_sku = create(:travel_ota_sku, app: @app, channel: @travel_channel)
    @order = create(:travel_order, app: @app)
    @travel_ota_snapshot_count = 5
    @travel_ota_snapshots = FactoryBot.create_list(:travel_ota_snapshot, @travel_ota_snapshot_count, ota_sku: @ota_sku, order: @order)
  end

  path '/travel/manage/ota_skus/{ota_sku_id}/ota_snapshots' do
    parameter 'ota_sku_id', in: :path, type: :string

    get(summary: 'list ota_snapshots') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:ota_sku_id) { @ota_sku.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_ota_snapshot_count
        }
      end
    end
  end

  path '/travel/manage/ota_skus/{ota_sku_id}/ota_snapshots/{id}' do
    parameter 'ota_sku_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show ota_snapshot') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:ota_sku_id) { @ota_sku.id }
        let(:id) { @travel_ota_snapshots.first.id }
        it {
          body = JSON.parse(response.body)
          ota_snapshot = @travel_ota_snapshots.first
          expect(body['ota_sku_id']).to eq ota_snapshot.ota_sku_id
          expect(body['order_id']).to eq ota_snapshot.order_id
          expect(body['model_flag']).to eq ota_snapshot.model_flag
          expect(body['model_payload']).to eq ota_snapshot.model_payload
          expect(body['model_payload_summary']).to eq ota_snapshot.model_payload_summary
          expect(body['model_detail']).to eq ota_snapshot.model_detail
          expect(body['type']).to eq ota_snapshot.type
          expect(body['name']).to eq ota_snapshot.name
          expect(body['amount']).to eq ota_snapshot.amount
          expect(body['count']).to eq ota_snapshot.count
          expect(body['meta']).to eq ota_snapshot.meta
        }
      end
    end
  end

  path '/travel/manage/orders/{order_id}/ota_snapshots' do
    parameter 'order_id', in: :path, type: :string

    get(summary: 'list ota_snapshots') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @travel_ota_snapshot_count
        }
      end
    end
  end

  path '/travel/manage/orders/{order_id}/ota_snapshots/{id}' do
    parameter 'order_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show ota_snapshot') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:order_id) { @order.id }
        let(:id) { @travel_ota_snapshots.first.id }
        it {
          body = JSON.parse(response.body)
          ota_snapshot = @travel_ota_snapshots.first
          expect(body['ota_sku_id']).to eq ota_snapshot.ota_sku_id
          expect(body['order_id']).to eq ota_snapshot.order_id
          expect(body['model_flag']).to eq ota_snapshot.model_flag
          expect(body['model_payload']).to eq ota_snapshot.model_payload
          expect(body['model_payload_summary']).to eq ota_snapshot.model_payload_summary
          expect(body['model_detail']).to eq ota_snapshot.model_detail
          expect(body['type']).to eq ota_snapshot.type
          expect(body['name']).to eq ota_snapshot.name
          expect(body['amount']).to eq ota_snapshot.amount
          expect(body['count']).to eq ota_snapshot.count
          expect(body['meta']).to eq ota_snapshot.meta
        }
      end
    end
  end
end
