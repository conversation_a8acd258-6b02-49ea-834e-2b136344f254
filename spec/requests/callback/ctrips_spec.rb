require 'swagger_helper'

RSpec.describe 'callback/ctrips', type: :request, capture_examples: true, tags: ["callback"] do
  user_ref = {
    type: :object, properties: {
      user: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          tanent_id: { type: :integer, description: '' },
          ref_user_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          account: { type: :string, description: '账号，关联登录' },
          name: { type: :string, description: '用户姓名' },
          nickname: { type: :string, description: '用户昵称' },
          pinyin: { type: :string, description: '用户名拼音' },
          mobile: { type: :string, description: '用户手机号' },
          email: { type: :string, description: '用户邮箱' },
          gender: { type: :string, description: '性别' },
          avatar: { type: :jsonb, description: '用户头像' },
          identity_id: { type: :string, description: '证件号码，需要时候可以作为唯一标识' },
          last_visit_at: { type: :datetime, description: '最后访问时间' },
          blocked_at: { type: :datetime, description: '' },
          is_blocked: { type: :boolean, description: '' },
        }
      }
    }
  }
  user_value = FactoryBot.attributes_for(:user)

  before :each do
  end
end
