Rails.application.routes.draw do
  namespace :travel do
    namespace :manage do
      resources :channels

      resources :ota_skus do
        resources :ota_snapshots, only: [:index, :show]
      end

      resources :ota_products

      resources :orders do
        exportable
        versionable

        resources :order_divides
        resources :ota_snapshots, only: [:index, :show]
        # 收支具体的记录
        resources :revenues, only: [:index, :show, :update]
        # 手动支付记录
        resources :manual_revenues
        # 支付平台支付记录
        resources :pay_revenues

        post :fill_create_form, on: :collection
        post :sync, on: :member
        post :sync_all_orders, on: :collection
      end

      resources :activity_temps do
        resources :demand_grp_temps
        resources :demand_temps
        resources :day_temps
        resources :offer_temps

        post :clone, on: :member

        # 模板生成的行程，create 方法是由模版生成实例
        # resources :activities
      end

      resources :demand_grp_temps, only: [] do
        resources :offer_temps, only: [:index, :update, :destroy]
      end

      # 日历点进去每一天信息
      resources :days, only: [:index, :show]

      resources :activities do
        weixin_chatable
        post :export_xlsx, on: :member
        post :refresh_demands, on: :member
        post :fill_create_form, on: :collection
        post :fill_link_create_form, on: :collection
        resources :days

        resources :demand_grps
        resources :demands do
          post :import_to_demand_preset, on: :member
        end
        resources :offers
      end

      resources :demand_grps, only: [] do
        resources :offers, only: [:index, :update, :destroy]
      end

      resources :demand_grp_presets
      resources :demand_presets do
        exportable
        importable
        post :clone, on: :member
      end

      resources :product_dirs
      resources :activity_temp_dirs

      resources :skus

      resources :products do
        member do
          post :import_entity
        end
        resources :demand_temps
        resources :product_days
        resources :offer_temps
      end

      resources :serve_products

      resources :demand_defines

      resources :demands, only: [:index,:show, :update] do
        resources :serves
        post :import_to_demand_preset, on: :member
      end

      # 旅行社管理结算单
      resources :offers do
        exportable
        resources :demands, only: [:index, :show]
      end

      # 旅行社管理支付单
      resources :payments do
        exportable
        eventable
      end

      resources :receipts

      resources :currencies

      resources :customers

      resources :poi_geos

      ### Poi 库 ###
      # 通过参数过滤公有的还是私有的
      resources :poi_countries do
        # 管理已经私有化的region
        resources :poi_regions
      end

      # 申领/管理国家资源信息
      resources :poi_app_countries
      # 这里只能查看poi_app_countries下的
      resources :poi_regions, only: [:index, :show]

      # 供应商
      resources :suppliers
      resources :pay_accounts

      # 目的地
      resources :destinations
      resources :records

      resources :notices
      resources :notice_grps

      resources :statistics, only: [:create]
    end

    namespace :user do
      resources :apps, only: [] do
        post :ta_resource_statistic, on: :member
        post :ta_collection_statistic, on: :collection
      end

      resources :activities, only: [:index, :show]
      resources :orders,     only: [:index, :show]
    end

    namespace :api do
      resources :days,              only: [:index, :show]
      resources :demands,           only: [:index, :show]
      resources :demand_temps,      only: [:index, :show]
      resources :demand_grps,       only: [:index, :show]
      resources :poi_regions,       only: [:index, :show]
      resources :notices,           only: [:index, :show]
      resources :notice_grps,       only: [:index, :show]
      resources :products,          only: [:index, :show] do
        markable
      end
      resources :poi_app_countries, only: [:index, :show] do
        markable
      end

      resources :activities,    only: [:index, :show] do
        post :ta_collection_statistic, on: :collection
        post :ta_resource_statistic, on: :member
        # resources :days,        only: [:index, :show]
        # resources :demands,     only: [:index, :show]
        resources :demand_grps, only: [:index, :show]
      end

      resources :destinations, only: [:index, :show]
      resources :records, only: [:index, :show]

      resources :destinations, only: [:index, :show] do
        markable
      end
      resources :records, only: [:index, :show] do
        markable
      end
    end

    namespace :lite do
      resources :activities, only: [:index, :show]
      resources :demands, only: [:index, :show]
    end
  end

  namespace :callback do
    resources :alitrip, only: [] do
      post :notify, on: :collection
    end

    resources :ctrip, only: [] do
      post :notify, on: :collection
    end
  end
end
