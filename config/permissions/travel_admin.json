{"mod": "travel", "role": "travel_admin", "actions": [{"mod": "travel", "key": "Travel::Manage::ChannelsController#index", "action": "index", "klass": "Travel::Manage::ChannelsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ChannelsController#create", "action": "create", "klass": "Travel::Manage::ChannelsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ChannelsController#show", "action": "show", "klass": "Travel::Manage::ChannelsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ChannelsController#update", "action": "update", "klass": "Travel::Manage::ChannelsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ChannelsController#destroy", "action": "destroy", "klass": "Travel::Manage::ChannelsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OtaSnapshotsController#index", "action": "index", "klass": "Travel::Manage::OtaSnapshotsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OtaSnapshotsController#show", "action": "show", "klass": "Travel::Manage::OtaSnapshotsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OtaSkusController#index", "action": "index", "klass": "Travel::Manage::OtaSkusController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OtaSkusController#create", "action": "create", "klass": "Travel::Manage::OtaSkusController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::OtaSkusController#show", "action": "show", "klass": "Travel::Manage::OtaSkusController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OtaSkusController#update", "action": "update", "klass": "Travel::Manage::OtaSkusController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::OtaSkusController#destroy", "action": "destroy", "klass": "Travel::Manage::OtaSkusController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OtaProductsController#index", "action": "index", "klass": "Travel::Manage::OtaProductsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OtaProductsController#create", "action": "create", "klass": "Travel::Manage::OtaProductsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::OtaProductsController#show", "action": "show", "klass": "Travel::Manage::OtaProductsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OtaProductsController#update", "action": "update", "klass": "Travel::Manage::OtaProductsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::OtaProductsController#destroy", "action": "destroy", "klass": "Travel::Manage::OtaProductsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OrdersController#export", "action": "export", "klass": "Travel::Manage::OrdersController", "position": 70}, {"mod": "travel", "key": "Travel::Manage::OrdersController#export_async", "action": "export_async", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrdersController#version_relationships_index", "action": "version_relationships_index", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrdersController#version_relationships_show", "action": "version_relationships_show", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrdersController#paper_trail_versions_index", "action": "paper_trail_versions_index", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrdersController#paper_trail_versions_show", "action": "paper_trail_versions_show", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrderDividesController#index", "action": "index", "klass": "Travel::Manage::OrderDividesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OrderDividesController#create", "action": "create", "klass": "Travel::Manage::OrderDividesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::OrderDividesController#show", "action": "show", "klass": "Travel::Manage::OrderDividesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OrderDividesController#update", "action": "update", "klass": "Travel::Manage::OrderDividesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::OrderDividesController#destroy", "action": "destroy", "klass": "Travel::Manage::OrderDividesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::RevenuesController#index", "action": "index", "klass": "Travel::Manage::RevenuesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::RevenuesController#show", "action": "show", "klass": "Travel::Manage::RevenuesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::RevenuesController#update", "action": "update", "klass": "Travel::Manage::RevenuesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ManualRevenuesController#index", "action": "index", "klass": "Travel::Manage::ManualRevenuesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ManualRevenuesController#create", "action": "create", "klass": "Travel::Manage::ManualRevenuesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ManualRevenuesController#show", "action": "show", "klass": "Travel::Manage::ManualRevenuesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ManualRevenuesController#update", "action": "update", "klass": "Travel::Manage::ManualRevenuesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ManualRevenuesController#destroy", "action": "destroy", "klass": "Travel::Manage::ManualRevenuesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OrdersController#fill_create_form", "action": "fill_create_form", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrdersController#sync", "action": "sync", "klass": "Travel::Manage::OrdersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::OrdersController#index", "action": "index", "klass": "Travel::Manage::OrdersController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OrdersController#create", "action": "create", "klass": "Travel::Manage::OrdersController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::OrdersController#show", "action": "show", "klass": "Travel::Manage::OrdersController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OrdersController#update", "action": "update", "klass": "Travel::Manage::OrdersController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::OrdersController#destroy", "action": "destroy", "klass": "Travel::Manage::OrdersController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandGrpTempsController#index", "action": "index", "klass": "Travel::Manage::DemandGrpTempsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandGrpTempsController#create", "action": "create", "klass": "Travel::Manage::DemandGrpTempsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandGrpTempsController#show", "action": "show", "klass": "Travel::Manage::DemandGrpTempsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandGrpTempsController#update", "action": "update", "klass": "Travel::Manage::DemandGrpTempsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandGrpTempsController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandGrpTempsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandTempsController#index", "action": "index", "klass": "Travel::Manage::DemandTempsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandTempsController#create", "action": "create", "klass": "Travel::Manage::DemandTempsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandTempsController#show", "action": "show", "klass": "Travel::Manage::DemandTempsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandTempsController#update", "action": "update", "klass": "Travel::Manage::DemandTempsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandTempsController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandTempsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DayTempsController#index", "action": "index", "klass": "Travel::Manage::DayTempsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DayTempsController#create", "action": "create", "klass": "Travel::Manage::DayTempsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DayTempsController#show", "action": "show", "klass": "Travel::Manage::DayTempsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DayTempsController#update", "action": "update", "klass": "Travel::Manage::DayTempsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DayTempsController#destroy", "action": "destroy", "klass": "Travel::Manage::DayTempsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OfferTempsController#index", "action": "index", "klass": "Travel::Manage::OfferTempsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OfferTempsController#create", "action": "create", "klass": "Travel::Manage::OfferTempsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::OfferTempsController#show", "action": "show", "klass": "Travel::Manage::OfferTempsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OfferTempsController#update", "action": "update", "klass": "Travel::Manage::OfferTempsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::OfferTempsController#destroy", "action": "destroy", "klass": "Travel::Manage::OfferTempsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ActivityTempsController#clone", "action": "clone", "klass": "Travel::Manage::ActivityTempsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::ActivityTempsController#index", "action": "index", "klass": "Travel::Manage::ActivityTempsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ActivityTempsController#create", "action": "create", "klass": "Travel::Manage::ActivityTempsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ActivityTempsController#show", "action": "show", "klass": "Travel::Manage::ActivityTempsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ActivityTempsController#update", "action": "update", "klass": "Travel::Manage::ActivityTempsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ActivityTempsController#destroy", "action": "destroy", "klass": "Travel::Manage::ActivityTempsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DaysController#index", "action": "index", "klass": "Travel::Manage::DaysController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DaysController#show", "action": "show", "klass": "Travel::Manage::DaysController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#chat", "action": "chat", "klass": "Travel::Manage::ActivitiesController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#export_xlsx", "action": "export_xlsx", "klass": "Travel::Manage::ActivitiesController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#refresh_demands", "action": "refresh_demands", "klass": "Travel::Manage::ActivitiesController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#fill_create_form", "action": "fill_create_form", "klass": "Travel::Manage::ActivitiesController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#fill_link_create_form", "action": "fill_link_create_form", "klass": "Travel::Manage::ActivitiesController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::DaysController#create", "action": "create", "klass": "Travel::Manage::DaysController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DaysController#update", "action": "update", "klass": "Travel::Manage::DaysController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DaysController#destroy", "action": "destroy", "klass": "Travel::Manage::DaysController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandGrpsController#index", "action": "index", "klass": "Travel::Manage::DemandGrpsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandGrpsController#create", "action": "create", "klass": "Travel::Manage::DemandGrpsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandGrpsController#show", "action": "show", "klass": "Travel::Manage::DemandGrpsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandGrpsController#update", "action": "update", "klass": "Travel::Manage::DemandGrpsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandGrpsController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandGrpsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandsController#import_to_demand_preset", "action": "import_to_demand_preset", "klass": "Travel::Manage::DemandsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::DemandsController#index", "action": "index", "klass": "Travel::Manage::DemandsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandsController#create", "action": "create", "klass": "Travel::Manage::DemandsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandsController#show", "action": "show", "klass": "Travel::Manage::DemandsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandsController#update", "action": "update", "klass": "Travel::Manage::DemandsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandsController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OffersController#index", "action": "index", "klass": "Travel::Manage::OffersController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::OffersController#create", "action": "create", "klass": "Travel::Manage::OffersController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::OffersController#show", "action": "show", "klass": "Travel::Manage::OffersController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::OffersController#update", "action": "update", "klass": "Travel::Manage::OffersController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::OffersController#destroy", "action": "destroy", "klass": "Travel::Manage::OffersController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#index", "action": "index", "klass": "Travel::Manage::ActivitiesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#create", "action": "create", "klass": "Travel::Manage::ActivitiesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#show", "action": "show", "klass": "Travel::Manage::ActivitiesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#update", "action": "update", "klass": "Travel::Manage::ActivitiesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ActivitiesController#destroy", "action": "destroy", "klass": "Travel::Manage::ActivitiesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandGrpPresetsController#index", "action": "index", "klass": "Travel::Manage::DemandGrpPresetsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandGrpPresetsController#create", "action": "create", "klass": "Travel::Manage::DemandGrpPresetsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandGrpPresetsController#show", "action": "show", "klass": "Travel::Manage::DemandGrpPresetsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandGrpPresetsController#update", "action": "update", "klass": "Travel::Manage::DemandGrpPresetsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandGrpPresetsController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandGrpPresetsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#export", "action": "export", "klass": "Travel::Manage::DemandPresetsController", "position": 70}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#export_async", "action": "export_async", "klass": "Travel::Manage::DemandPresetsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#import", "action": "import", "klass": "Travel::Manage::DemandPresetsController", "position": 60}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#import_async", "action": "import_async", "klass": "Travel::Manage::DemandPresetsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#clone", "action": "clone", "klass": "Travel::Manage::DemandPresetsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#index", "action": "index", "klass": "Travel::Manage::DemandPresetsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#create", "action": "create", "klass": "Travel::Manage::DemandPresetsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#show", "action": "show", "klass": "Travel::Manage::DemandPresetsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#update", "action": "update", "klass": "Travel::Manage::DemandPresetsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandPresetsController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandPresetsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ProductDirsController#index", "action": "index", "klass": "Travel::Manage::ProductDirsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ProductDirsController#create", "action": "create", "klass": "Travel::Manage::ProductDirsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ProductDirsController#show", "action": "show", "klass": "Travel::Manage::ProductDirsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ProductDirsController#update", "action": "update", "klass": "Travel::Manage::ProductDirsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ProductDirsController#destroy", "action": "destroy", "klass": "Travel::Manage::ProductDirsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ActivityTempDirsController#index", "action": "index", "klass": "Travel::Manage::ActivityTempDirsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ActivityTempDirsController#create", "action": "create", "klass": "Travel::Manage::ActivityTempDirsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ActivityTempDirsController#show", "action": "show", "klass": "Travel::Manage::ActivityTempDirsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ActivityTempDirsController#update", "action": "update", "klass": "Travel::Manage::ActivityTempDirsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ActivityTempDirsController#destroy", "action": "destroy", "klass": "Travel::Manage::ActivityTempDirsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ProductsController#import_entity", "action": "import_entity", "klass": "Travel::Manage::ProductsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::ProductDaysController#index", "action": "index", "klass": "Travel::Manage::ProductDaysController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ProductDaysController#create", "action": "create", "klass": "Travel::Manage::ProductDaysController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ProductDaysController#show", "action": "show", "klass": "Travel::Manage::ProductDaysController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ProductDaysController#update", "action": "update", "klass": "Travel::Manage::ProductDaysController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ProductDaysController#destroy", "action": "destroy", "klass": "Travel::Manage::ProductDaysController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ProductsController#index", "action": "index", "klass": "Travel::Manage::ProductsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ProductsController#create", "action": "create", "klass": "Travel::Manage::ProductsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ProductsController#show", "action": "show", "klass": "Travel::Manage::ProductsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ProductsController#update", "action": "update", "klass": "Travel::Manage::ProductsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ProductsController#destroy", "action": "destroy", "klass": "Travel::Manage::ProductsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DemandDefinesController#index", "action": "index", "klass": "Travel::Manage::DemandDefinesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DemandDefinesController#create", "action": "create", "klass": "Travel::Manage::DemandDefinesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DemandDefinesController#show", "action": "show", "klass": "Travel::Manage::DemandDefinesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DemandDefinesController#update", "action": "update", "klass": "Travel::Manage::DemandDefinesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DemandDefinesController#destroy", "action": "destroy", "klass": "Travel::Manage::DemandDefinesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::OffersController#export", "action": "export", "klass": "Travel::Manage::OffersController", "position": 70}, {"mod": "travel", "key": "Travel::Manage::OffersController#export_async", "action": "export_async", "klass": "Travel::Manage::OffersController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#export", "action": "export", "klass": "Travel::Manage::PaymentsController", "position": 70}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#export_async", "action": "export_async", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_machine", "action": "state_machine", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_transitions_index", "action": "state_transitions_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_token_defines_index", "action": "state_token_defines_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_places_index", "action": "state_places_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_tokens_index", "action": "state_tokens_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_tokens_show", "action": "state_tokens_show", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_tokens_create", "action": "state_tokens_create", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_tokens_update", "action": "state_tokens_update", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_events_index", "action": "state_events_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_events_show", "action": "state_events_show", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_events_create", "action": "state_events_create", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_instances_index", "action": "state_instances_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#state_tasks_index", "action": "state_tasks_index", "klass": "Travel::Manage::PaymentsController", "position": 100}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#index", "action": "index", "klass": "Travel::Manage::PaymentsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#create", "action": "create", "klass": "Travel::Manage::PaymentsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#show", "action": "show", "klass": "Travel::Manage::PaymentsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#update", "action": "update", "klass": "Travel::Manage::PaymentsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::PaymentsController#destroy", "action": "destroy", "klass": "Travel::Manage::PaymentsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::ReceiptsController#index", "action": "index", "klass": "Travel::Manage::ReceiptsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::ReceiptsController#create", "action": "create", "klass": "Travel::Manage::ReceiptsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::ReceiptsController#show", "action": "show", "klass": "Travel::Manage::ReceiptsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::ReceiptsController#update", "action": "update", "klass": "Travel::Manage::ReceiptsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::ReceiptsController#destroy", "action": "destroy", "klass": "Travel::Manage::ReceiptsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::CurrenciesController#index", "action": "index", "klass": "Travel::Manage::CurrenciesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::CurrenciesController#create", "action": "create", "klass": "Travel::Manage::CurrenciesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::CurrenciesController#show", "action": "show", "klass": "Travel::Manage::CurrenciesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::CurrenciesController#update", "action": "update", "klass": "Travel::Manage::CurrenciesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::CurrenciesController#destroy", "action": "destroy", "klass": "Travel::Manage::CurrenciesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::CustomersController#index", "action": "index", "klass": "Travel::Manage::CustomersController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::CustomersController#create", "action": "create", "klass": "Travel::Manage::CustomersController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::CustomersController#show", "action": "show", "klass": "Travel::Manage::CustomersController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::CustomersController#update", "action": "update", "klass": "Travel::Manage::CustomersController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::CustomersController#destroy", "action": "destroy", "klass": "Travel::Manage::CustomersController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::PoiGeosController#index", "action": "index", "klass": "Travel::Manage::PoiGeosController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::PoiGeosController#create", "action": "create", "klass": "Travel::Manage::PoiGeosController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::PoiGeosController#show", "action": "show", "klass": "Travel::Manage::PoiGeosController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::PoiGeosController#update", "action": "update", "klass": "Travel::Manage::PoiGeosController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::PoiGeosController#destroy", "action": "destroy", "klass": "Travel::Manage::PoiGeosController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::PoiRegionsController#index", "action": "index", "klass": "Travel::Manage::PoiRegionsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::PoiRegionsController#create", "action": "create", "klass": "Travel::Manage::PoiRegionsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::PoiRegionsController#show", "action": "show", "klass": "Travel::Manage::PoiRegionsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::PoiRegionsController#update", "action": "update", "klass": "Travel::Manage::PoiRegionsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::PoiRegionsController#destroy", "action": "destroy", "klass": "Travel::Manage::PoiRegionsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::PoiCountriesController#index", "action": "index", "klass": "Travel::Manage::PoiCountriesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::PoiCountriesController#create", "action": "create", "klass": "Travel::Manage::PoiCountriesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::PoiCountriesController#show", "action": "show", "klass": "Travel::Manage::PoiCountriesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::PoiCountriesController#update", "action": "update", "klass": "Travel::Manage::PoiCountriesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::PoiCountriesController#destroy", "action": "destroy", "klass": "Travel::Manage::PoiCountriesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::PoiAppCountriesController#index", "action": "index", "klass": "Travel::Manage::PoiAppCountriesController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::PoiAppCountriesController#create", "action": "create", "klass": "Travel::Manage::PoiAppCountriesController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::PoiAppCountriesController#show", "action": "show", "klass": "Travel::Manage::PoiAppCountriesController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::PoiAppCountriesController#update", "action": "update", "klass": "Travel::Manage::PoiAppCountriesController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::PoiAppCountriesController#destroy", "action": "destroy", "klass": "Travel::Manage::PoiAppCountriesController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::SuppliersController#index", "action": "index", "klass": "Travel::Manage::SuppliersController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::SuppliersController#create", "action": "create", "klass": "Travel::Manage::SuppliersController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::SuppliersController#show", "action": "show", "klass": "Travel::Manage::SuppliersController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::SuppliersController#update", "action": "update", "klass": "Travel::Manage::SuppliersController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::SuppliersController#destroy", "action": "destroy", "klass": "Travel::Manage::SuppliersController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::PayAccountsController#index", "action": "index", "klass": "Travel::Manage::PayAccountsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::PayAccountsController#create", "action": "create", "klass": "Travel::Manage::PayAccountsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::PayAccountsController#show", "action": "show", "klass": "Travel::Manage::PayAccountsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::PayAccountsController#update", "action": "update", "klass": "Travel::Manage::PayAccountsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::PayAccountsController#destroy", "action": "destroy", "klass": "Travel::Manage::PayAccountsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::DestinationsController#index", "action": "index", "klass": "Travel::Manage::DestinationsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::DestinationsController#create", "action": "create", "klass": "Travel::Manage::DestinationsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::DestinationsController#show", "action": "show", "klass": "Travel::Manage::DestinationsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::DestinationsController#update", "action": "update", "klass": "Travel::Manage::DestinationsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::DestinationsController#destroy", "action": "destroy", "klass": "Travel::Manage::DestinationsController", "position": 50}, {"mod": "travel", "key": "Travel::Manage::RecordsController#index", "action": "index", "klass": "Travel::Manage::RecordsController", "position": 10}, {"mod": "travel", "key": "Travel::Manage::RecordsController#create", "action": "create", "klass": "Travel::Manage::RecordsController", "position": 30}, {"mod": "travel", "key": "Travel::Manage::RecordsController#show", "action": "show", "klass": "Travel::Manage::RecordsController", "position": 20}, {"mod": "travel", "key": "Travel::Manage::RecordsController#update", "action": "update", "klass": "Travel::Manage::RecordsController", "position": 40}, {"mod": "travel", "key": "Travel::Manage::RecordsController#destroy", "action": "destroy", "klass": "Travel::Manage::RecordsController", "position": 50}]}